# 元宇宙社交空间 - 环境变量配置示例
# 复制此文件为 .env 并根据实际环境修改配置值

# =============================================================================
# 基础配置
# =============================================================================

# Django密钥（生产环境必须修改）
SECRET_KEY=django-insecure-change-me-in-production-environment

# 调试模式（生产环境设置为False）
DEBUG=True

# 允许的主机（生产环境必须设置正确的域名）
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# 环境标识
ENVIRONMENT=development

# =============================================================================
# 数据库配置
# =============================================================================

# MySQL数据库配置
DB_NAME=soic
DB_USER=soic_user
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=3306

# =============================================================================
# 缓存和消息队列配置
# =============================================================================

# Redis配置
REDIS_URL=redis://localhost:6379/0

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/2
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# =============================================================================
# 邮件配置
# =============================================================================

# SMTP邮件服务器配置
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=元宇宙社交空间 <<EMAIL>>

# =============================================================================
# 第三方服务配置
# =============================================================================

# Sentry错误监控（可选）
SENTRY_DSN=

# AWS S3存储配置（可选）
USE_S3=False
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_STORAGE_BUCKET_NAME=
AWS_S3_REGION_NAME=us-east-1
AWS_S3_CUSTOM_DOMAIN=

# =============================================================================
# 安全配置
# =============================================================================

# CORS允许的源（生产环境必须设置）
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# JWT密钥（可选，默认使用SECRET_KEY）
JWT_SECRET_KEY=

# =============================================================================
# 功能开关
# =============================================================================

# 是否启用API限流
ENABLE_RATE_LIMITING=True

# 是否启用缓存
ENABLE_CACHING=True

# 是否启用WebSocket
ENABLE_WEBSOCKET=True

# 是否启用Celery异步任务
ENABLE_CELERY=True

# =============================================================================
# 开发环境专用配置
# =============================================================================

# 是否显示调试工具栏
SHOW_DEBUG_TOOLBAR=True

# 是否启用SQL查询日志
LOG_SQL_QUERIES=False

# 是否使用假数据填充
USE_FAKE_DATA=False
