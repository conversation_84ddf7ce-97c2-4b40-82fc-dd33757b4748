"""
元宇宙社交空间 - 内容管理Admin配置

这个文件配置了Django Admin界面中内容管理相关模型的管理功能
"""
from django.contrib import admin
from django.utils.html import format_html

from .models import Post, Comment, Like, Share, PostMedia, Topic


class PostMediaInline(admin.TabularInline):
    """动态媒体文件内联编辑"""
    model = PostMedia
    extra = 0
    readonly_fields = ('file_size_display', 'duration_display')
    
    def file_size_display(self, obj):
        return obj.get_file_size_display()
    file_size_display.short_description = '文件大小'


@admin.register(Topic)
class TopicAdmin(admin.ModelAdmin):
    """话题标签Admin配置"""
    
    list_display = (
        'display_name',
        'name',
        'get_post_count',
        'is_official',
        'is_active',
        'created_at',
    )
    
    list_filter = (
        'is_official',
        'is_active',
        'created_at',
    )
    
    search_fields = (
        'name',
        'display_name',
        'description',
    )
    
    ordering = ('-created_at',)
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'display_name', 'description')
        }),
        ('设置', {
            'fields': ('cover_url', 'is_official', 'is_active')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at')
    
    def get_post_count(self, obj):
        """获取话题下的动态数量"""
        return obj.posts.filter(is_deleted=False, status='published').count()
    get_post_count.short_description = '动态数量'


@admin.register(Post)
class PostAdmin(admin.ModelAdmin):
    """动态Admin配置"""
    
    list_display = (
        'get_content_preview',
        'author',
        'content_type',
        'visibility',
        'status',
        'like_count',
        'comment_count',
        'published_at',
    )
    
    list_filter = (
        'content_type',
        'visibility',
        'status',
        'is_pinned',
        'is_deleted',
        'published_at',
        'created_at',
    )
    
    search_fields = (
        'content',
        'author__username',
        'author__email',
        'location',
    )
    
    ordering = ('-published_at', '-created_at')
    
    fieldsets = (
        ('基本信息', {
            'fields': ('author', 'content', 'content_type')
        }),
        ('媒体和位置', {
            'fields': ('media_urls', 'location')
        }),
        ('设置', {
            'fields': ('visibility', 'status', 'is_pinned')
        }),
        ('统计信息', {
            'fields': ('like_count', 'comment_count', 'share_count', 'view_count'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('published_at', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('like_count', 'comment_count', 'share_count', 'view_count', 'published_at', 'created_at', 'updated_at')
    
    inlines = [PostMediaInline]
    
    filter_horizontal = ('topics',)
    
    def get_content_preview(self, obj):
        """获取内容预览"""
        content = obj.content[:100] + '...' if len(obj.content) > 100 else obj.content
        return content
    get_content_preview.short_description = '内容预览'
    
    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('author').prefetch_related('topics')


@admin.register(Comment)
class CommentAdmin(admin.ModelAdmin):
    """评论Admin配置"""
    
    list_display = (
        'get_content_preview',
        'author',
        'post',
        'parent',
        'like_count',
        'reply_count',
        'created_at',
    )
    
    list_filter = (
        'is_deleted',
        'created_at',
    )
    
    search_fields = (
        'content',
        'author__username',
        'post__content',
    )
    
    ordering = ('-created_at',)
    
    fieldsets = (
        ('基本信息', {
            'fields': ('post', 'author', 'parent', 'content')
        }),
        ('统计信息', {
            'fields': ('like_count', 'reply_count'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('like_count', 'reply_count', 'created_at', 'updated_at')
    
    def get_content_preview(self, obj):
        """获取内容预览"""
        content = obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
        return content
    get_content_preview.short_description = '内容预览'
    
    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('author', 'post', 'parent')


@admin.register(Like)
class LikeAdmin(admin.ModelAdmin):
    """点赞Admin配置"""
    
    list_display = (
        'user',
        'target_type',
        'target_id',
        'get_target_info',
        'created_at',
    )
    
    list_filter = (
        'target_type',
        'created_at',
    )
    
    search_fields = (
        'user__username',
        'user__email',
    )
    
    ordering = ('-created_at',)
    
    fieldsets = (
        ('点赞信息', {
            'fields': ('user', 'target_type', 'target_id')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at')
    
    def get_target_info(self, obj):
        """获取目标对象信息"""
        target = obj.get_target_object()
        if target:
            if obj.target_type == 'post':
                content = target.content[:30] + '...' if len(target.content) > 30 else target.content
                return f"动态: {content}"
            elif obj.target_type == 'comment':
                content = target.content[:30] + '...' if len(target.content) > 30 else target.content
                return f"评论: {content}"
        return "已删除"
    get_target_info.short_description = '目标对象'
    
    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('user')


@admin.register(Share)
class ShareAdmin(admin.ModelAdmin):
    """分享Admin配置"""
    
    list_display = (
        'user',
        'get_post_info',
        'get_comment_preview',
        'created_at',
    )
    
    list_filter = (
        'created_at',
    )
    
    search_fields = (
        'user__username',
        'post__content',
        'comment',
    )
    
    ordering = ('-created_at',)
    
    fieldsets = (
        ('分享信息', {
            'fields': ('user', 'post', 'comment')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at')
    
    def get_post_info(self, obj):
        """获取动态信息"""
        content = obj.post.content[:30] + '...' if len(obj.post.content) > 30 else obj.post.content
        return f"{obj.post.author.username}: {content}"
    get_post_info.short_description = '分享的动态'
    
    def get_comment_preview(self, obj):
        """获取分享评论预览"""
        if obj.comment:
            return obj.comment[:50] + '...' if len(obj.comment) > 50 else obj.comment
        return '无评论'
    get_comment_preview.short_description = '分享评论'
    
    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('user', 'post', 'post__author')


@admin.register(PostMedia)
class PostMediaAdmin(admin.ModelAdmin):
    """动态媒体文件Admin配置"""
    
    list_display = (
        'filename',
        'post',
        'media_type',
        'get_file_size_display',
        'get_dimensions',
        'uploaded_by',
        'created_at',
    )
    
    list_filter = (
        'media_type',
        'file_format',
        'is_deleted',
        'created_at',
    )
    
    search_fields = (
        'filename',
        'post__content',
        'uploaded_by__username',
    )
    
    ordering = ('-created_at',)
    
    fieldsets = (
        ('基本信息', {
            'fields': ('post', 'uploaded_by', 'filename', 'media_type')
        }),
        ('文件信息', {
            'fields': ('file_url', 'thumbnail_url', 'file_size', 'file_format')
        }),
        ('媒体属性', {
            'fields': ('width', 'height', 'duration', 'sort_order')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at')
    
    def get_dimensions(self, obj):
        """获取尺寸信息"""
        if obj.width and obj.height:
            return f"{obj.width} × {obj.height}"
        return "未知"
    get_dimensions.short_description = '尺寸'
    
    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('post', 'uploaded_by')
