"""
元宇宙社交空间 - 内容管理模型

这个文件定义了内容管理域的所有模型，包括：
- Post: 用户动态模型
- Comment: 评论模型
- Like: 点赞模型
- Share: 分享模型
- PostMedia: 动态媒体文件模型
- Topic: 话题标签模型

严格按照数据库设计文档实现，确保字段定义完全一致
"""
from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
import json

from core.models import BaseModel


class TopicManager(models.Manager):
    """话题管理器"""

    def get_or_create_topic(self, name):
        """获取或创建话题"""
        name = name.strip().lower()
        if not name:
            return None

        topic, created = self.get_or_create(
            name=name,
            defaults={'display_name': name.title()}
        )
        return topic

    def get_trending_topics(self, limit=10):
        """获取热门话题"""
        return self.annotate(
            post_count=models.Count('posts')
        ).filter(
            post_count__gt=0
        ).order_by('-post_count', '-updated_at')[:limit]


class Topic(BaseModel):
    """
    话题标签模型

    管理动态中的话题标签
    对应数据库表：topics
    """

    # 话题名称（用于URL和搜索）
    name = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='话题名称',
        help_text='话题的唯一标识名称'
    )

    # 显示名称
    display_name = models.CharField(
        max_length=50,
        verbose_name='显示名称',
        help_text='话题的显示名称'
    )

    # 话题描述
    description = models.TextField(
        blank=True,
        verbose_name='话题描述',
        help_text='话题的详细描述'
    )

    # 话题封面图
    cover_url = models.URLField(
        max_length=500,
        blank=True,
        verbose_name='封面图URL',
        help_text='话题封面图片的URL地址'
    )

    # 是否为官方话题
    is_official = models.BooleanField(
        default=False,
        verbose_name='官方话题',
        help_text='是否为官方认证话题'
    )

    # 话题状态
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否激活',
        help_text='话题是否可用'
    )

    # 自定义管理器
    objects = TopicManager()

    class Meta:
        db_table = 'topics'
        verbose_name = '话题标签'
        verbose_name_plural = '话题标签'
        indexes = [
            models.Index(fields=['name'], name='idx_topics_name'),
            models.Index(fields=['is_active'], name='idx_topics_is_active'),
            models.Index(fields=['is_official'], name='idx_topics_is_official'),
        ]

    def __str__(self):
        """字符串表示"""
        return f"#{self.display_name}"

    def save(self, *args, **kwargs):
        """保存时的额外处理"""
        # 确保name为小写
        self.name = self.name.lower().strip()
        super().save(*args, **kwargs)


class PostManager(models.Manager):
    """动态管理器"""

    def get_public_posts(self):
        """获取公开动态"""
        return self.filter(
            visibility='public',
            is_deleted=False,
            status='published'
        )

    def get_user_posts(self, user, viewer=None):
        """获取用户动态（考虑隐私设置）"""
        queryset = self.filter(author=user, is_deleted=False, status='published')

        if viewer == user:
            # 用户查看自己的动态，显示所有
            return queryset
        elif viewer and viewer.is_authenticated:
            # 已登录用户查看他人动态
            from apps.social.models import Friendship
            is_friend = Friendship.objects.are_friends(viewer, user)

            if is_friend:
                # 好友可以看到好友可见的动态
                return queryset.filter(visibility__in=['public', 'friends'])
            else:
                # 非好友只能看到公开动态
                return queryset.filter(visibility='public')
        else:
            # 未登录用户只能看到公开动态
            return queryset.filter(visibility='public')

    def get_feed_posts(self, user):
        """获取用户动态流"""
        from apps.social.models import Follow, Friendship

        # 获取关注的用户
        following_ids = Follow.objects.filter(follower=user).values_list('following_id', flat=True)

        # 获取好友
        friends = Friendship.objects.get_friends(user)
        friend_ids = [friend.id for friend in friends]

        # 合并关注和好友的ID
        user_ids = set(following_ids) | set(friend_ids)
        user_ids.add(user.id)  # 包含自己的动态

        return self.filter(
            author_id__in=user_ids,
            is_deleted=False,
            status='published'
        ).filter(
            models.Q(visibility='public') |
            models.Q(visibility='friends', author_id__in=friend_ids) |
            models.Q(author=user)  # 自己的所有动态
        )


class Post(BaseModel):
    """
    用户动态模型

    存储用户发布的动态内容
    对应数据库表：posts
    """

    # 内容类型选择
    CONTENT_TYPE_CHOICES = [
        ('text', '纯文本'),
        ('image', '图片'),
        ('video', '视频'),
        ('link', '链接'),
    ]

    # 可见性选择
    VISIBILITY_CHOICES = [
        ('public', '公开'),
        ('friends', '好友可见'),
        ('private', '私有'),
    ]

    # 状态选择
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('published', '已发布'),
        ('reviewing', '审核中'),
        ('rejected', '已拒绝'),
    ]

    # 作者
    author = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='posts',
        verbose_name='作者',
        help_text='动态的发布者'
    )

    # 动态内容
    content = models.TextField(
        verbose_name='动态内容',
        help_text='动态的文本内容'
    )

    # 内容类型
    content_type = models.CharField(
        max_length=20,
        choices=CONTENT_TYPE_CHOICES,
        default='text',
        verbose_name='内容类型',
        help_text='动态的内容类型'
    )

    # 媒体文件URLs（JSON格式存储）
    media_urls = models.JSONField(
        default=list,
        blank=True,
        verbose_name='媒体文件URLs',
        help_text='动态包含的媒体文件URL列表'
    )

    # 地理位置
    location = models.CharField(
        max_length=200,
        blank=True,
        verbose_name='地理位置',
        help_text='动态发布的地理位置'
    )

    # 可见性
    visibility = models.CharField(
        max_length=20,
        choices=VISIBILITY_CHOICES,
        default='public',
        verbose_name='可见性',
        help_text='动态的可见性设置'
    )

    # 状态
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='published',
        verbose_name='状态',
        help_text='动态的发布状态'
    )

    # 话题标签
    topics = models.ManyToManyField(
        Topic,
        blank=True,
        related_name='posts',
        verbose_name='话题标签',
        help_text='动态关联的话题标签'
    )

    # 统计字段
    like_count = models.PositiveIntegerField(
        default=0,
        verbose_name='点赞数',
        help_text='动态的点赞数量'
    )

    comment_count = models.PositiveIntegerField(
        default=0,
        verbose_name='评论数',
        help_text='动态的评论数量'
    )

    share_count = models.PositiveIntegerField(
        default=0,
        verbose_name='分享数',
        help_text='动态的分享数量'
    )

    view_count = models.PositiveIntegerField(
        default=0,
        verbose_name='浏览数',
        help_text='动态的浏览数量'
    )

    # 是否置顶
    is_pinned = models.BooleanField(
        default=False,
        verbose_name='是否置顶',
        help_text='动态是否置顶显示'
    )

    # 是否删除
    is_deleted = models.BooleanField(
        default=False,
        verbose_name='是否删除',
        help_text='动态是否已删除'
    )

    # 发布时间
    published_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='发布时间',
        help_text='动态的发布时间'
    )

    # 自定义管理器
    objects = PostManager()

    class Meta:
        db_table = 'posts'
        verbose_name = '用户动态'
        verbose_name_plural = '用户动态'
        ordering = ['-published_at', '-created_at']
        indexes = [
            models.Index(fields=['author'], name='idx_posts_author'),
            models.Index(fields=['content_type'], name='idx_posts_content_type'),
            models.Index(fields=['visibility'], name='idx_posts_visibility'),
            models.Index(fields=['status'], name='idx_posts_status'),
            models.Index(fields=['published_at'], name='idx_posts_published_at'),
            models.Index(fields=['is_deleted'], name='idx_posts_is_deleted'),
            models.Index(fields=['is_pinned'], name='idx_posts_is_pinned'),
        ]

    def __str__(self):
        """字符串表示"""
        content_preview = self.content[:50] + '...' if len(self.content) > 50 else self.content
        return f"{self.author.username}: {content_preview}"

    def save(self, *args, **kwargs):
        """保存时的额外处理"""
        # 如果状态变为已发布且没有发布时间，设置发布时间
        if self.status == 'published' and not self.published_at:
            self.published_at = timezone.now()

        super().save(*args, **kwargs)

    def soft_delete(self):
        """软删除动态"""
        self.is_deleted = True
        self.save(update_fields=['is_deleted', 'updated_at'])

    def get_media_urls(self):
        """获取媒体文件URL列表"""
        if isinstance(self.media_urls, str):
            try:
                return json.loads(self.media_urls)
            except json.JSONDecodeError:
                return []
        return self.media_urls or []

    def add_media_url(self, url):
        """添加媒体文件URL"""
        media_urls = self.get_media_urls()
        if url not in media_urls:
            media_urls.append(url)
            self.media_urls = media_urls
            self.save(update_fields=['media_urls', 'updated_at'])

    def increment_view_count(self):
        """增加浏览数"""
        self.view_count = models.F('view_count') + 1
        self.save(update_fields=['view_count'])


class CommentManager(models.Manager):
    """评论管理器"""

    def get_post_comments(self, post, include_replies=True):
        """获取动态的评论"""
        queryset = self.filter(post=post, is_deleted=False)

        if include_replies:
            return queryset
        else:
            # 只返回顶级评论
            return queryset.filter(parent=None)

    def get_comment_replies(self, comment):
        """获取评论的回复"""
        return self.filter(parent=comment, is_deleted=False)


class Comment(BaseModel):
    """
    评论模型

    存储用户对动态的评论和回复
    对应数据库表：comments
    """

    # 关联的动态
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        related_name='comments',
        verbose_name='动态',
        help_text='评论所属的动态'
    )

    # 评论作者
    author = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='comments',
        verbose_name='评论者',
        help_text='发表评论的用户'
    )

    # 父评论（用于回复）
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='replies',
        verbose_name='父评论',
        help_text='回复的父评论，为空表示顶级评论'
    )

    # 评论内容
    content = models.TextField(
        verbose_name='评论内容',
        help_text='评论的文本内容'
    )

    # 统计字段
    like_count = models.PositiveIntegerField(
        default=0,
        verbose_name='点赞数',
        help_text='评论的点赞数量'
    )

    reply_count = models.PositiveIntegerField(
        default=0,
        verbose_name='回复数',
        help_text='评论的回复数量'
    )

    # 是否删除
    is_deleted = models.BooleanField(
        default=False,
        verbose_name='是否删除',
        help_text='评论是否已删除'
    )

    # 自定义管理器
    objects = CommentManager()

    class Meta:
        db_table = 'comments'
        verbose_name = '评论'
        verbose_name_plural = '评论'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['post'], name='idx_comments_post'),
            models.Index(fields=['author'], name='idx_comments_author'),
            models.Index(fields=['parent'], name='idx_comments_parent'),
            models.Index(fields=['created_at'], name='idx_comments_created_at'),
            models.Index(fields=['is_deleted'], name='idx_comments_is_deleted'),
        ]

    def __str__(self):
        """字符串表示"""
        content_preview = self.content[:50] + '...' if len(self.content) > 50 else self.content
        return f"{self.author.username}: {content_preview}"

    def soft_delete(self):
        """软删除评论"""
        self.is_deleted = True
        self.save(update_fields=['is_deleted', 'updated_at'])

    def is_reply(self):
        """判断是否为回复"""
        return self.parent is not None

    def get_level(self):
        """获取评论层级"""
        level = 0
        parent = self.parent
        while parent:
            level += 1
            parent = parent.parent
        return level


class LikeManager(models.Manager):
    """点赞管理器"""

    def get_user_likes(self, user):
        """获取用户的所有点赞"""
        return self.filter(user=user)

    def is_liked_by_user(self, user, target_type, target_id):
        """检查用户是否已点赞"""
        return self.filter(
            user=user,
            target_type=target_type,
            target_id=target_id
        ).exists()

    def toggle_like(self, user, target_type, target_id):
        """切换点赞状态"""
        like, created = self.get_or_create(
            user=user,
            target_type=target_type,
            target_id=target_id
        )

        if not created:
            # 如果已存在，则删除（取消点赞）
            like.delete()
            return False

        return True


class Like(BaseModel):
    """
    点赞模型

    存储用户对动态和评论的点赞
    对应数据库表：likes
    """

    # 目标类型选择
    TARGET_TYPE_CHOICES = [
        ('post', '动态'),
        ('comment', '评论'),
    ]

    # 点赞用户
    user = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='likes',
        verbose_name='用户',
        help_text='点赞的用户'
    )

    # 目标类型
    target_type = models.CharField(
        max_length=20,
        choices=TARGET_TYPE_CHOICES,
        verbose_name='目标类型',
        help_text='点赞的目标类型'
    )

    # 目标ID
    target_id = models.PositiveIntegerField(
        verbose_name='目标ID',
        help_text='点赞的目标对象ID'
    )

    # 自定义管理器
    objects = LikeManager()

    class Meta:
        db_table = 'likes'
        verbose_name = '点赞'
        verbose_name_plural = '点赞'
        unique_together = ['user', 'target_type', 'target_id']
        indexes = [
            models.Index(fields=['user'], name='idx_likes_user'),
            models.Index(fields=['target_type', 'target_id'], name='idx_likes_target'),
            models.Index(fields=['created_at'], name='idx_likes_created_at'),
        ]

    def __str__(self):
        """字符串表示"""
        return f"{self.user.username} 点赞了 {self.target_type}#{self.target_id}"

    def get_target_object(self):
        """获取点赞的目标对象"""
        if self.target_type == 'post':
            try:
                return Post.objects.get(id=self.target_id)
            except Post.DoesNotExist:
                return None
        elif self.target_type == 'comment':
            try:
                return Comment.objects.get(id=self.target_id)
            except Comment.DoesNotExist:
                return None
        return None


class ShareManager(models.Manager):
    """分享管理器"""

    def get_post_shares(self, post):
        """获取动态的分享"""
        return self.filter(post=post)

    def get_user_shares(self, user):
        """获取用户的分享"""
        return self.filter(user=user)


class Share(BaseModel):
    """
    分享模型

    存储用户对动态的分享
    对应数据库表：shares
    """

    # 分享用户
    user = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='shares',
        verbose_name='分享用户',
        help_text='分享动态的用户'
    )

    # 分享的动态
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        related_name='shares',
        verbose_name='分享的动态',
        help_text='被分享的动态'
    )

    # 分享时的评论
    comment = models.TextField(
        blank=True,
        verbose_name='分享评论',
        help_text='分享时添加的评论'
    )

    # 自定义管理器
    objects = ShareManager()

    class Meta:
        db_table = 'shares'
        verbose_name = '分享'
        verbose_name_plural = '分享'
        unique_together = ['user', 'post']
        indexes = [
            models.Index(fields=['user'], name='idx_shares_user'),
            models.Index(fields=['post'], name='idx_shares_post'),
            models.Index(fields=['created_at'], name='idx_shares_created_at'),
        ]

    def __str__(self):
        """字符串表示"""
        return f"{self.user.username} 分享了 {self.post.author.username} 的动态"


class PostMediaManager(models.Manager):
    """动态媒体文件管理器"""

    def get_post_media(self, post):
        """获取动态的媒体文件"""
        return self.filter(post=post, is_deleted=False)

    def get_user_media(self, user):
        """获取用户上传的媒体文件"""
        return self.filter(uploaded_by=user, is_deleted=False)


class PostMedia(BaseModel):
    """
    动态媒体文件模型

    存储动态中的媒体文件信息
    对应数据库表：post_media
    """

    # 媒体类型选择
    MEDIA_TYPE_CHOICES = [
        ('image', '图片'),
        ('video', '视频'),
        ('audio', '音频'),
        ('document', '文档'),
    ]

    # 关联的动态
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        related_name='media_files',
        verbose_name='动态',
        help_text='媒体文件所属的动态'
    )

    # 上传用户
    uploaded_by = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='uploaded_media',
        verbose_name='上传用户',
        help_text='上传媒体文件的用户'
    )

    # 媒体类型
    media_type = models.CharField(
        max_length=20,
        choices=MEDIA_TYPE_CHOICES,
        verbose_name='媒体类型',
        help_text='媒体文件的类型'
    )

    # 文件名
    filename = models.CharField(
        max_length=255,
        verbose_name='文件名',
        help_text='原始文件名'
    )

    # 文件URL
    file_url = models.URLField(
        max_length=500,
        verbose_name='文件URL',
        help_text='媒体文件的访问URL'
    )

    # 缩略图URL
    thumbnail_url = models.URLField(
        max_length=500,
        blank=True,
        verbose_name='缩略图URL',
        help_text='媒体文件的缩略图URL'
    )

    # 文件大小（字节）
    file_size = models.PositiveIntegerField(
        verbose_name='文件大小',
        help_text='文件大小（字节）'
    )

    # 文件格式
    file_format = models.CharField(
        max_length=20,
        verbose_name='文件格式',
        help_text='文件的格式（如jpg、mp4等）'
    )

    # 媒体尺寸（宽度）
    width = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='宽度',
        help_text='图片或视频的宽度'
    )

    # 媒体尺寸（高度）
    height = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='高度',
        help_text='图片或视频的高度'
    )

    # 视频时长（秒）
    duration = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='时长',
        help_text='视频或音频的时长（秒）'
    )

    # 排序顺序
    sort_order = models.PositiveIntegerField(
        default=0,
        verbose_name='排序顺序',
        help_text='媒体文件在动态中的显示顺序'
    )

    # 是否删除
    is_deleted = models.BooleanField(
        default=False,
        verbose_name='是否删除',
        help_text='媒体文件是否已删除'
    )

    # 自定义管理器
    objects = PostMediaManager()

    class Meta:
        db_table = 'post_media'
        verbose_name = '动态媒体文件'
        verbose_name_plural = '动态媒体文件'
        ordering = ['sort_order', 'created_at']
        indexes = [
            models.Index(fields=['post'], name='idx_post_media_post'),
            models.Index(fields=['uploaded_by'], name='idx_post_media_uploaded_by'),
            models.Index(fields=['media_type'], name='idx_post_media_media_type'),
            models.Index(fields=['is_deleted'], name='idx_post_media_is_deleted'),
        ]

    def __str__(self):
        """字符串表示"""
        return f"{self.filename} ({self.media_type})"

    def soft_delete(self):
        """软删除媒体文件"""
        self.is_deleted = True
        self.save(update_fields=['is_deleted', 'updated_at'])

    def get_file_size_display(self):
        """获取文件大小的友好显示"""
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"

    def get_duration_display(self):
        """获取时长的友好显示"""
        if not self.duration:
            return None

        hours = self.duration // 3600
        minutes = (self.duration % 3600) // 60
        seconds = self.duration % 60

        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
