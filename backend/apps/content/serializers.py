"""
元宇宙社交空间 - 内容管理序列化器

这个文件定义了内容管理相关模型的序列化器，用于API数据的序列化和反序列化
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model

from .models import Post, Comment, Like, Share, PostMedia, Topic

User = get_user_model()


class UserBasicSerializer(serializers.ModelSerializer):
    """用户基础信息序列化器（用于内容功能）"""
    
    display_name = serializers.ReadOnlyField(source='get_display_name')
    avatar_url = serializers.CharField(source='profile.avatar_url', read_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'display_name', 'avatar_url',
            'is_active', 'is_verified'
        ]
        read_only_fields = ['id', 'is_active', 'is_verified']


class TopicSerializer(serializers.ModelSerializer):
    """话题标签序列化器"""
    
    post_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Topic
        fields = [
            'id', 'name', 'display_name', 'description', 'cover_url',
            'is_official', 'is_active', 'post_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_post_count(self, obj):
        """获取话题下的动态数量"""
        return obj.posts.filter(is_deleted=False, status='published').count()


class PostMediaSerializer(serializers.ModelSerializer):
    """动态媒体文件序列化器"""
    
    file_size_display = serializers.ReadOnlyField(source='get_file_size_display')
    duration_display = serializers.ReadOnlyField(source='get_duration_display')
    
    class Meta:
        model = PostMedia
        fields = [
            'id', 'media_type', 'filename', 'file_url', 'thumbnail_url',
            'file_size', 'file_size_display', 'file_format',
            'width', 'height', 'duration', 'duration_display',
            'sort_order', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class PostSerializer(serializers.ModelSerializer):
    """动态序列化器"""
    
    author = UserBasicSerializer(read_only=True)
    topics = TopicSerializer(many=True, read_only=True)
    media_files = PostMediaSerializer(many=True, read_only=True)
    
    # 用户交互状态
    is_liked = serializers.SerializerMethodField()
    is_shared = serializers.SerializerMethodField()
    
    class Meta:
        model = Post
        fields = [
            'id', 'author', 'content', 'content_type', 'media_urls',
            'location', 'visibility', 'status', 'topics', 'media_files',
            'like_count', 'comment_count', 'share_count', 'view_count',
            'is_pinned', 'is_liked', 'is_shared',
            'published_at', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'author', 'like_count', 'comment_count', 'share_count', 'view_count',
            'published_at', 'created_at', 'updated_at'
        ]
    
    def get_is_liked(self, obj):
        """检查当前用户是否已点赞"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return Like.objects.is_liked_by_user(request.user, 'post', obj.id)
        return False
    
    def get_is_shared(self, obj):
        """检查当前用户是否已分享"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return Share.objects.filter(user=request.user, post=obj).exists()
        return False


class PostCreateSerializer(serializers.ModelSerializer):
    """动态创建序列化器"""
    
    topic_names = serializers.ListField(
        child=serializers.CharField(max_length=50),
        required=False,
        write_only=True,
        help_text='话题标签名称列表'
    )
    
    class Meta:
        model = Post
        fields = [
            'content', 'content_type', 'media_urls', 'location',
            'visibility', 'topic_names'
        ]
    
    def create(self, validated_data):
        """创建动态"""
        topic_names = validated_data.pop('topic_names', [])
        
        # 设置作者
        validated_data['author'] = self.context['request'].user
        
        # 创建动态
        post = Post.objects.create(**validated_data)
        
        # 处理话题标签
        if topic_names:
            topics = []
            for name in topic_names:
                topic = Topic.objects.get_or_create_topic(name)
                if topic:
                    topics.append(topic)
            post.topics.set(topics)
        
        return post


class CommentSerializer(serializers.ModelSerializer):
    """评论序列化器"""
    
    author = UserBasicSerializer(read_only=True)
    replies = serializers.SerializerMethodField()
    is_liked = serializers.SerializerMethodField()
    
    class Meta:
        model = Comment
        fields = [
            'id', 'post', 'author', 'parent', 'content',
            'like_count', 'reply_count', 'is_liked', 'replies',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'author', 'like_count', 'reply_count',
            'created_at', 'updated_at'
        ]
    
    def get_replies(self, obj):
        """获取评论的回复（只返回一级回复）"""
        if obj.parent is None:  # 只为顶级评论获取回复
            replies = Comment.objects.get_comment_replies(obj)[:5]  # 限制返回数量
            return CommentSerializer(replies, many=True, context=self.context).data
        return []
    
    def get_is_liked(self, obj):
        """检查当前用户是否已点赞"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return Like.objects.is_liked_by_user(request.user, 'comment', obj.id)
        return False


class CommentCreateSerializer(serializers.ModelSerializer):
    """评论创建序列化器"""
    
    class Meta:
        model = Comment
        fields = ['post', 'parent', 'content']
    
    def create(self, validated_data):
        """创建评论"""
        validated_data['author'] = self.context['request'].user
        return Comment.objects.create(**validated_data)


class LikeSerializer(serializers.ModelSerializer):
    """点赞序列化器"""
    
    user = UserBasicSerializer(read_only=True)
    target_object = serializers.SerializerMethodField()
    
    class Meta:
        model = Like
        fields = [
            'id', 'user', 'target_type', 'target_id', 'target_object',
            'created_at'
        ]
        read_only_fields = ['id', 'user', 'created_at']
    
    def get_target_object(self, obj):
        """获取点赞的目标对象信息"""
        target = obj.get_target_object()
        if not target:
            return None
        
        if obj.target_type == 'post':
            return {
                'id': target.id,
                'content': target.content[:100],
                'author': target.author.username
            }
        elif obj.target_type == 'comment':
            return {
                'id': target.id,
                'content': target.content[:100],
                'author': target.author.username
            }
        return None


class ShareSerializer(serializers.ModelSerializer):
    """分享序列化器"""
    
    user = UserBasicSerializer(read_only=True)
    post = PostSerializer(read_only=True)
    
    class Meta:
        model = Share
        fields = [
            'id', 'user', 'post', 'comment', 'created_at'
        ]
        read_only_fields = ['id', 'user', 'created_at']


class ShareCreateSerializer(serializers.ModelSerializer):
    """分享创建序列化器"""
    
    class Meta:
        model = Share
        fields = ['post', 'comment']
    
    def create(self, validated_data):
        """创建分享"""
        validated_data['user'] = self.context['request'].user
        return Share.objects.create(**validated_data)


class MediaUploadSerializer(serializers.Serializer):
    """媒体文件上传序列化器"""
    
    file = serializers.FileField()
    media_type = serializers.ChoiceField(choices=PostMedia.MEDIA_TYPE_CHOICES)
    
    def validate_file(self, value):
        """验证文件"""
        # 文件大小限制（50MB）
        max_size = 50 * 1024 * 1024
        if value.size > max_size:
            raise serializers.ValidationError('文件大小不能超过50MB')
        
        return value
