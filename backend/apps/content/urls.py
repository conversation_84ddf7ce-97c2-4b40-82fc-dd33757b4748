"""
元宇宙社交空间 - 内容管理URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from rest_framework_nested import routers

from .views import (
    PostViewSet, CommentViewSet, TopicViewSet,
    MediaUploadView, UserPostsView
)

app_name = 'content'

# 创建主路由器
router = DefaultRouter()
router.register(r'posts', PostViewSet, basename='post')
router.register(r'topics', TopicViewSet, basename='topic')

# 创建嵌套路由器（动态的评论）
posts_router = routers.NestedDefaultRouter(router, r'posts', lookup='post')
posts_router.register(r'comments', CommentViewSet, basename='post-comments')

urlpatterns = [
    # 主路由
    path('', include(router.urls)),

    # 嵌套路由（评论）
    path('', include(posts_router.urls)),

    # 媒体文件上传
    path('media/upload/', MediaUploadView.as_view(), name='media-upload'),

    # 用户动态
    path('users/<int:user_id>/posts/', UserPostsView.as_view(), name='user-posts'),
]
