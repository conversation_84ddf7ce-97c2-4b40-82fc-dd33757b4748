"""
元宇宙社交空间 - 内容管理视图

这个文件定义了内容管理相关的API视图
"""
from rest_framework import generics, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from django.contrib.auth import get_user_model
from django.db.models import Q, F
from django.shortcuts import get_object_or_404
from django.core.files.storage import default_storage
from django.conf import settings
from drf_spectacular.utils import extend_schema, OpenApiParameter
import uuid
import os

from .models import Post, Comment, Like, Share, PostMedia, Topic
from .serializers import (
    PostSerializer, PostCreateSerializer, CommentSerializer, CommentCreateSerializer,
    LikeSerializer, ShareSerializer, ShareCreateSerializer,
    PostMediaSerializer, TopicSerializer, MediaUploadSerializer
)

User = get_user_model()


class PostViewSet(ModelViewSet):
    """动态视图集"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """获取动态查询集"""
        user = self.request.user
        
        # 根据不同的action返回不同的查询集
        if self.action == 'feed':
            return Post.objects.get_feed_posts(user)
        elif self.action == 'user_posts':
            target_user_id = self.kwargs.get('user_id')
            if target_user_id:
                target_user = get_object_or_404(User, id=target_user_id)
                return Post.objects.get_user_posts(target_user, user)
        
        # 默认返回公开动态
        return Post.objects.get_public_posts().select_related('author').prefetch_related('topics', 'media_files')
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return PostCreateSerializer
        return PostSerializer
    
    @extend_schema(
        summary="获取动态列表",
        description="获取公开动态列表，支持分页",
        responses={200: PostSerializer(many=True)}
    )
    def list(self, request, *args, **kwargs):
        """获取动态列表"""
        return super().list(request, *args, **kwargs)
    
    @extend_schema(
        summary="发布动态",
        description="发布新的动态内容",
        request=PostCreateSerializer,
        responses={201: PostSerializer}
    )
    def create(self, request, *args, **kwargs):
        """发布动态"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        post = serializer.save()
        
        # 返回完整的动态信息
        response_serializer = PostSerializer(post, context={'request': request})
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)
    
    @extend_schema(
        summary="获取动态详情",
        description="获取指定动态的详细信息",
        responses={200: PostSerializer}
    )
    def retrieve(self, request, *args, **kwargs):
        """获取动态详情"""
        post = self.get_object()
        
        # 增加浏览数（如果不是作者本人）
        if request.user != post.author:
            post.increment_view_count()
        
        return super().retrieve(request, *args, **kwargs)
    
    @extend_schema(
        summary="获取用户动态流",
        description="获取当前用户的个性化动态流",
        responses={200: PostSerializer(many=True)}
    )
    @action(detail=False, methods=['get'])
    def feed(self, request):
        """获取用户动态流"""
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = PostSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)
        
        serializer = PostSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)
    
    @extend_schema(
        summary="获取推荐动态",
        description="获取推荐的动态内容",
        responses={200: PostSerializer(many=True)}
    )
    @action(detail=False, methods=['get'])
    def recommended(self, request):
        """获取推荐动态"""
        # 简单的推荐算法：热门动态 + 用户兴趣
        queryset = Post.objects.get_public_posts().annotate(
            engagement_score=F('like_count') + F('comment_count') * 2 + F('share_count') * 3
        ).order_by('-engagement_score', '-published_at')[:50]
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = PostSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)
        
        serializer = PostSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)
    
    @extend_schema(
        summary="搜索动态",
        description="根据关键词搜索动态内容",
        parameters=[
            OpenApiParameter(
                name='q',
                description='搜索关键词',
                required=True,
                type=str
            ),
        ],
        responses={200: PostSerializer(many=True)}
    )
    @action(detail=False, methods=['get'])
    def search(self, request):
        """搜索动态"""
        query = request.query_params.get('q', '').strip()
        if not query:
            return Response({'error': '请提供搜索关键词'}, status=status.HTTP_400_BAD_REQUEST)
        
        queryset = Post.objects.get_public_posts().filter(
            Q(content__icontains=query) |
            Q(topics__name__icontains=query) |
            Q(topics__display_name__icontains=query)
        ).distinct()
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = PostSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)
        
        serializer = PostSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)
    
    @extend_schema(
        summary="点赞动态",
        description="点赞或取消点赞动态",
        responses={200: {"message": "操作成功", "is_liked": "bool"}}
    )
    @action(detail=True, methods=['post'])
    def like(self, request, pk=None):
        """点赞动态"""
        post = self.get_object()
        
        is_liked = Like.objects.toggle_like(request.user, 'post', post.id)
        
        # 更新动态的点赞数
        if is_liked:
            post.like_count = F('like_count') + 1
            message = '点赞成功'
        else:
            post.like_count = F('like_count') - 1
            message = '取消点赞成功'
        
        post.save(update_fields=['like_count'])
        
        return Response({
            'message': message,
            'is_liked': is_liked,
            'like_count': Post.objects.get(id=post.id).like_count
        })
    
    @extend_schema(
        summary="分享动态",
        description="分享动态到自己的动态流",
        request=ShareCreateSerializer,
        responses={201: ShareSerializer}
    )
    @action(detail=True, methods=['post'])
    def share(self, request, pk=None):
        """分享动态"""
        post = self.get_object()
        
        # 检查是否已经分享过
        if Share.objects.filter(user=request.user, post=post).exists():
            return Response({'error': '已经分享过此动态'}, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = ShareCreateSerializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        
        # 设置分享的动态
        share = serializer.save(post=post)
        
        # 更新动态的分享数
        post.share_count = F('share_count') + 1
        post.save(update_fields=['share_count'])
        
        response_serializer = ShareSerializer(share, context={'request': request})
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)
    
    def perform_destroy(self, instance):
        """删除动态（软删除）"""
        instance.soft_delete()


class CommentViewSet(ModelViewSet):
    """评论视图集"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """获取评论查询集"""
        post_id = self.kwargs.get('post_pk')
        if post_id:
            return Comment.objects.get_post_comments(
                Post.objects.get(id=post_id),
                include_replies=False  # 只返回顶级评论
            ).select_related('author', 'parent')
        return Comment.objects.none()
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return CommentCreateSerializer
        return CommentSerializer
    
    @extend_schema(
        summary="发表评论",
        description="对动态发表评论或回复",
        request=CommentCreateSerializer,
        responses={201: CommentSerializer}
    )
    def create(self, request, *args, **kwargs):
        """发表评论"""
        post_id = self.kwargs.get('post_pk')
        post = get_object_or_404(Post, id=post_id)
        
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        comment = serializer.save(post=post)
        
        # 更新动态的评论数
        post.comment_count = F('comment_count') + 1
        post.save(update_fields=['comment_count'])
        
        # 如果是回复，更新父评论的回复数
        if comment.parent:
            comment.parent.reply_count = F('reply_count') + 1
            comment.parent.save(update_fields=['reply_count'])
        
        response_serializer = CommentSerializer(comment, context={'request': request})
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)
    
    @extend_schema(
        summary="点赞评论",
        description="点赞或取消点赞评论",
        responses={200: {"message": "操作成功", "is_liked": "bool"}}
    )
    @action(detail=True, methods=['post'])
    def like(self, request, pk=None, post_pk=None):
        """点赞评论"""
        comment = self.get_object()
        
        is_liked = Like.objects.toggle_like(request.user, 'comment', comment.id)
        
        # 更新评论的点赞数
        if is_liked:
            comment.like_count = F('like_count') + 1
            message = '点赞成功'
        else:
            comment.like_count = F('like_count') - 1
            message = '取消点赞成功'
        
        comment.save(update_fields=['like_count'])
        
        return Response({
            'message': message,
            'is_liked': is_liked,
            'like_count': Comment.objects.get(id=comment.id).like_count
        })
    
    def perform_destroy(self, instance):
        """删除评论（软删除）"""
        instance.soft_delete()


class MediaUploadView(generics.CreateAPIView):
    """媒体文件上传视图"""

    serializer_class = MediaUploadSerializer
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="上传媒体文件",
        description="上传图片、视频等媒体文件",
        request=MediaUploadSerializer,
        responses={201: {"file_url": "string", "thumbnail_url": "string"}}
    )
    def post(self, request, *args, **kwargs):
        """上传媒体文件"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        file = serializer.validated_data['file']
        media_type = serializer.validated_data['media_type']

        # 生成唯一文件名
        file_extension = os.path.splitext(file.name)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"

        # 根据媒体类型确定存储路径
        upload_path = f"media/{media_type}s/{unique_filename}"

        # 保存文件
        file_path = default_storage.save(upload_path, file)
        file_url = default_storage.url(file_path)

        # TODO: 生成缩略图（对于图片和视频）
        thumbnail_url = ""
        if media_type in ['image', 'video']:
            # 这里应该实现缩略图生成逻辑
            thumbnail_url = file_url  # 临时使用原图

        return Response({
            'file_url': file_url,
            'thumbnail_url': thumbnail_url,
            'filename': file.name,
            'file_size': file.size,
            'media_type': media_type
        }, status=status.HTTP_201_CREATED)


class TopicViewSet(ModelViewSet):
    """话题标签视图集"""

    queryset = Topic.objects.filter(is_active=True)
    serializer_class = TopicSerializer
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="获取热门话题",
        description="获取当前热门的话题标签",
        responses={200: TopicSerializer(many=True)}
    )
    @action(detail=False, methods=['get'])
    def trending(self, request):
        """获取热门话题"""
        topics = Topic.objects.get_trending_topics(limit=20)
        serializer = TopicSerializer(topics, many=True, context={'request': request})
        return Response(serializer.data)

    @extend_schema(
        summary="搜索话题",
        description="根据关键词搜索话题",
        parameters=[
            OpenApiParameter(
                name='q',
                description='搜索关键词',
                required=True,
                type=str
            ),
        ],
        responses={200: TopicSerializer(many=True)}
    )
    @action(detail=False, methods=['get'])
    def search(self, request):
        """搜索话题"""
        query = request.query_params.get('q', '').strip()
        if not query:
            return Response({'error': '请提供搜索关键词'}, status=status.HTTP_400_BAD_REQUEST)

        topics = Topic.objects.filter(
            Q(name__icontains=query) | Q(display_name__icontains=query),
            is_active=True
        )[:20]

        serializer = TopicSerializer(topics, many=True, context={'request': request})
        return Response(serializer.data)


class UserPostsView(generics.ListAPIView):
    """用户动态列表视图"""

    serializer_class = PostSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取指定用户的动态"""
        user_id = self.kwargs.get('user_id')
        target_user = get_object_or_404(User, id=user_id)

        return Post.objects.get_user_posts(
            target_user,
            self.request.user
        ).select_related('author').prefetch_related('topics', 'media_files')

    @extend_schema(
        summary="获取用户动态",
        description="获取指定用户的动态列表",
        responses={200: PostSerializer(many=True)}
    )
    def get(self, request, *args, **kwargs):
        """获取用户动态"""
        return super().get(request, *args, **kwargs)
