"""
元宇宙社交空间 - 消息系统模型

这个文件定义了消息系统域的所有模型，包括：
- Conversation: 会话模型
- ConversationParticipant: 会话参与者模型
- Message: 消息模型
- MessageRead: 消息已读状态模型
- UserOnlineStatus: 用户在线状态模型

严格按照数据库设计文档实现，确保字段定义完全一致
"""
from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
import json
import uuid

from core.models import BaseModel


class ConversationManager(models.Manager):
    """会话管理器"""

    def get_or_create_private_conversation(self, user1, user2):
        """获取或创建私聊会话"""
        # 查找现有的私聊会话
        conversation = self.filter(
            conversation_type='private',
            participants__user__in=[user1, user2]
        ).annotate(
            participant_count=models.Count('participants')
        ).filter(participant_count=2).first()

        if conversation:
            # 验证参与者是否正确
            participant_users = set(
                conversation.participants.values_list('user_id', flat=True)
            )
            if participant_users == {user1.id, user2.id}:
                return conversation, False

        # 创建新的私聊会话
        conversation = self.create(
            conversation_type='private',
            name=f"{user1.username}, {user2.username}",
            created_by=user1
        )

        # 添加参与者
        ConversationParticipant.objects.bulk_create([
            ConversationParticipant(conversation=conversation, user=user1, role='member'),
            ConversationParticipant(conversation=conversation, user=user2, role='member'),
        ])

        return conversation, True

    def get_user_conversations(self, user):
        """获取用户的所有会话"""
        return self.filter(
            participants__user=user,
            is_active=True
        ).select_related('created_by').prefetch_related('participants__user')


class Conversation(BaseModel):
    """
    会话模型

    管理聊天会话，支持私聊和群聊
    对应数据库表：conversations
    """

    # 会话类型选择
    CONVERSATION_TYPE_CHOICES = [
        ('private', '私聊'),
        ('group', '群聊'),
        ('channel', '频道'),
    ]

    # 会话名称
    name = models.CharField(
        max_length=100,
        verbose_name='会话名称',
        help_text='会话的显示名称'
    )

    # 会话类型
    conversation_type = models.CharField(
        max_length=20,
        choices=CONVERSATION_TYPE_CHOICES,
        default='private',
        verbose_name='会话类型',
        help_text='会话的类型'
    )

    # 会话描述
    description = models.TextField(
        blank=True,
        verbose_name='会话描述',
        help_text='会话的详细描述'
    )

    # 会话头像
    avatar_url = models.URLField(
        max_length=500,
        blank=True,
        verbose_name='会话头像',
        help_text='会话的头像图片URL'
    )

    # 创建者
    created_by = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='created_conversations',
        verbose_name='创建者',
        help_text='创建会话的用户'
    )

    # 最后一条消息
    last_message = models.ForeignKey(
        'Message',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='+',
        verbose_name='最后一条消息',
        help_text='会话中的最后一条消息'
    )

    # 最后活跃时间
    last_activity_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='最后活跃时间',
        help_text='会话的最后活跃时间'
    )

    # 是否激活
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否激活',
        help_text='会话是否处于激活状态'
    )

    # 自定义管理器
    objects = ConversationManager()

    class Meta:
        db_table = 'conversations'
        verbose_name = '会话'
        verbose_name_plural = '会话'
        ordering = ['-last_activity_at']
        indexes = [
            models.Index(fields=['conversation_type'], name='idx_conversations_type'),
            models.Index(fields=['created_by'], name='idx_conversations_created_by'),
            models.Index(fields=['last_activity_at'], name='idx_conversations_last_activity'),
            models.Index(fields=['is_active'], name='idx_conversations_is_active'),
        ]

    def __str__(self):
        """字符串表示"""
        return f"{self.name} ({self.conversation_type})"

    def update_last_activity(self, message=None):
        """更新最后活跃时间"""
        self.last_activity_at = timezone.now()
        if message:
            self.last_message = message
        self.save(update_fields=['last_activity_at', 'last_message'])

    def get_participant_count(self):
        """获取参与者数量"""
        return self.participants.filter(is_active=True).count()

    def is_participant(self, user):
        """检查用户是否为参与者"""
        return self.participants.filter(user=user, is_active=True).exists()


class ConversationParticipantManager(models.Manager):
    """会话参与者管理器"""

    def get_active_participants(self, conversation):
        """获取会话的活跃参与者"""
        return self.filter(conversation=conversation, is_active=True)

    def get_user_role(self, conversation, user):
        """获取用户在会话中的角色"""
        try:
            participant = self.get(conversation=conversation, user=user, is_active=True)
            return participant.role
        except self.model.DoesNotExist:
            return None


class ConversationParticipant(BaseModel):
    """
    会话参与者模型

    管理会话中的参与者及其角色
    对应数据库表：conversation_participants
    """

    # 角色选择
    ROLE_CHOICES = [
        ('owner', '群主'),
        ('admin', '管理员'),
        ('member', '成员'),
    ]

    # 会话
    conversation = models.ForeignKey(
        Conversation,
        on_delete=models.CASCADE,
        related_name='participants',
        verbose_name='会话',
        help_text='参与的会话'
    )

    # 用户
    user = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='conversation_participations',
        verbose_name='用户',
        help_text='参与会话的用户'
    )

    # 角色
    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        default='member',
        verbose_name='角色',
        help_text='用户在会话中的角色'
    )

    # 加入时间
    joined_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='加入时间',
        help_text='用户加入会话的时间'
    )

    # 是否激活
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否激活',
        help_text='参与者是否处于激活状态'
    )

    # 是否静音
    is_muted = models.BooleanField(
        default=False,
        verbose_name='是否静音',
        help_text='是否静音此会话的通知'
    )

    # 最后已读消息
    last_read_message = models.ForeignKey(
        'Message',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='+',
        verbose_name='最后已读消息',
        help_text='用户最后已读的消息'
    )

    # 最后已读时间
    last_read_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='最后已读时间',
        help_text='用户最后已读消息的时间'
    )

    # 自定义管理器
    objects = ConversationParticipantManager()

    class Meta:
        db_table = 'conversation_participants'
        verbose_name = '会话参与者'
        verbose_name_plural = '会话参与者'
        unique_together = ['conversation', 'user']
        indexes = [
            models.Index(fields=['conversation'], name='idx_conv_participants_conversation'),
            models.Index(fields=['user'], name='idx_conv_participants_user'),
            models.Index(fields=['is_active'], name='idx_conv_participants_is_active'),
            models.Index(fields=['joined_at'], name='idx_conv_participants_joined_at'),
        ]

    def __str__(self):
        """字符串表示"""
        return f"{self.user.username} in {self.conversation.name} ({self.role})"

    def mark_as_read(self, message):
        """标记消息为已读"""
        self.last_read_message = message
        self.last_read_at = timezone.now()
        self.save(update_fields=['last_read_message', 'last_read_at'])

    def get_unread_count(self):
        """获取未读消息数量"""
        if not self.last_read_message:
            return self.conversation.messages.filter(
                created_at__gte=self.joined_at
            ).count()

        return self.conversation.messages.filter(
            id__gt=self.last_read_message.id
        ).count()


class MessageManager(models.Manager):
    """消息管理器"""

    def get_conversation_messages(self, conversation, limit=50):
        """获取会话的消息"""
        return self.filter(
            conversation=conversation,
            is_deleted=False
        ).select_related('sender').order_by('-created_at')[:limit]

    def get_unread_messages(self, conversation, user):
        """获取用户在会话中的未读消息"""
        try:
            participant = ConversationParticipant.objects.get(
                conversation=conversation, user=user, is_active=True
            )
            if participant.last_read_message:
                return self.filter(
                    conversation=conversation,
                    id__gt=participant.last_read_message.id,
                    is_deleted=False
                )
            else:
                return self.filter(
                    conversation=conversation,
                    created_at__gte=participant.joined_at,
                    is_deleted=False
                )
        except ConversationParticipant.DoesNotExist:
            return self.none()


class Message(BaseModel):
    """
    消息模型

    存储会话中的消息内容
    对应数据库表：messages
    """

    # 消息类型选择
    MESSAGE_TYPE_CHOICES = [
        ('text', '文本'),
        ('image', '图片'),
        ('file', '文件'),
        ('voice', '语音'),
        ('video', '视频'),
        ('system', '系统消息'),
    ]

    # 消息状态选择
    STATUS_CHOICES = [
        ('sent', '已发送'),
        ('delivered', '已送达'),
        ('read', '已读'),
        ('failed', '发送失败'),
    ]

    # 会话
    conversation = models.ForeignKey(
        Conversation,
        on_delete=models.CASCADE,
        related_name='messages',
        verbose_name='会话',
        help_text='消息所属的会话'
    )

    # 发送者
    sender = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='sent_messages',
        verbose_name='发送者',
        help_text='发送消息的用户'
    )

    # 消息类型
    message_type = models.CharField(
        max_length=20,
        choices=MESSAGE_TYPE_CHOICES,
        default='text',
        verbose_name='消息类型',
        help_text='消息的类型'
    )

    # 消息内容
    content = models.TextField(
        verbose_name='消息内容',
        help_text='消息的文本内容'
    )

    # 文件URL（用于图片、文件、语音、视频）
    file_url = models.URLField(
        max_length=500,
        blank=True,
        verbose_name='文件URL',
        help_text='消息附件的URL地址'
    )

    # 文件大小
    file_size = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='文件大小',
        help_text='文件大小（字节）'
    )

    # 文件名
    file_name = models.CharField(
        max_length=255,
        blank=True,
        verbose_name='文件名',
        help_text='原始文件名'
    )

    # 回复的消息
    reply_to = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='replies',
        verbose_name='回复的消息',
        help_text='回复的原消息'
    )

    # 消息状态
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='sent',
        verbose_name='消息状态',
        help_text='消息的发送状态'
    )

    # 是否删除
    is_deleted = models.BooleanField(
        default=False,
        verbose_name='是否删除',
        help_text='消息是否已删除'
    )

    # 是否编辑过
    is_edited = models.BooleanField(
        default=False,
        verbose_name='是否编辑过',
        help_text='消息是否被编辑过'
    )

    # 编辑时间
    edited_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='编辑时间',
        help_text='消息最后编辑的时间'
    )

    # 消息UUID（用于客户端去重）
    message_uuid = models.UUIDField(
        default=uuid.uuid4,
        unique=True,
        verbose_name='消息UUID',
        help_text='消息的唯一标识符'
    )

    # 自定义管理器
    objects = MessageManager()

    class Meta:
        db_table = 'messages'
        verbose_name = '消息'
        verbose_name_plural = '消息'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['conversation'], name='idx_messages_conversation'),
            models.Index(fields=['sender'], name='idx_messages_sender'),
            models.Index(fields=['created_at'], name='idx_messages_created_at'),
            models.Index(fields=['reply_to'], name='idx_messages_reply_to'),
            models.Index(fields=['message_uuid'], name='idx_messages_uuid'),
            models.Index(fields=['is_deleted'], name='idx_messages_is_deleted'),
        ]

    def __str__(self):
        """字符串表示"""
        content_preview = self.content[:50] + '...' if len(self.content) > 50 else self.content
        return f"{self.sender.username}: {content_preview}"

    def soft_delete(self):
        """软删除消息"""
        self.is_deleted = True
        self.save(update_fields=['is_deleted', 'updated_at'])

    def mark_as_edited(self):
        """标记消息为已编辑"""
        self.is_edited = True
        self.edited_at = timezone.now()
        self.save(update_fields=['is_edited', 'edited_at', 'updated_at'])

    def get_file_size_display(self):
        """获取文件大小的友好显示"""
        if not self.file_size:
            return None

        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"


class UserOnlineStatusManager(models.Manager):
    """用户在线状态管理器"""

    def update_user_status(self, user, status, device_info=None):
        """更新用户在线状态"""
        obj, created = self.update_or_create(
            user=user,
            defaults={
                'status': status,
                'last_activity_at': timezone.now(),
                'device_info': device_info or {},
            }
        )
        return obj

    def get_online_users(self):
        """获取在线用户"""
        # 5分钟内有活动的用户视为在线
        threshold = timezone.now() - timezone.timedelta(minutes=5)
        return self.filter(
            status='online',
            last_activity_at__gte=threshold
        )


class UserOnlineStatus(BaseModel):
    """
    用户在线状态模型

    跟踪用户的在线状态和最后活跃时间
    对应数据库表：user_online_status
    """

    # 状态选择
    STATUS_CHOICES = [
        ('online', '在线'),
        ('away', '离开'),
        ('busy', '忙碌'),
        ('offline', '离线'),
    ]

    # 用户
    user = models.OneToOneField(
        'users.User',
        on_delete=models.CASCADE,
        related_name='online_status',
        verbose_name='用户',
        help_text='用户'
    )

    # 在线状态
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='offline',
        verbose_name='在线状态',
        help_text='用户的在线状态'
    )

    # 最后活跃时间
    last_activity_at = models.DateTimeField(
        auto_now=True,
        verbose_name='最后活跃时间',
        help_text='用户最后活跃的时间'
    )

    # 设备信息
    device_info = models.JSONField(
        default=dict,
        blank=True,
        verbose_name='设备信息',
        help_text='用户设备的相关信息'
    )

    # 自定义管理器
    objects = UserOnlineStatusManager()

    class Meta:
        db_table = 'user_online_status'
        verbose_name = '用户在线状态'
        verbose_name_plural = '用户在线状态'
        indexes = [
            models.Index(fields=['status'], name='idx_user_online_status_status'),
            models.Index(fields=['last_activity_at'], name='idx_user_online_status_last_activity'),
        ]

    def __str__(self):
        """字符串表示"""
        return f"{self.user.username} - {self.status}"

    def is_online(self):
        """检查用户是否在线"""
        if self.status == 'offline':
            return False

        # 5分钟内有活动视为在线
        threshold = timezone.now() - timezone.timedelta(minutes=5)
        return self.last_activity_at >= threshold

    def set_online(self, device_info=None):
        """设置用户为在线状态"""
        self.status = 'online'
        self.last_activity_at = timezone.now()
        if device_info:
            self.device_info = device_info
        self.save()

    def set_offline(self):
        """设置用户为离线状态"""
        self.status = 'offline'
        self.save()
