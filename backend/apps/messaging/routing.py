"""
元宇宙社交空间 - WebSocket路由配置

这个文件定义了WebSocket的URL路由
"""
from django.urls import re_path
from . import consumers

websocket_urlpatterns = [
    # 聊天WebSocket连接
    re_path(r'ws/chat/(?P<conversation_id>\w+)/$', consumers.ChatConsumer.as_asgi()),
    
    # 用户状态WebSocket连接
    re_path(r'ws/status/$', consumers.UserStatusConsumer.as_asgi()),
    
    # 通知WebSocket连接
    re_path(r'ws/notifications/$', consumers.NotificationConsumer.as_asgi()),
]
