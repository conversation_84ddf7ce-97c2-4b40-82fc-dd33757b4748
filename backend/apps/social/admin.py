"""
元宇宙社交空间 - 社交功能Admin配置

这个文件配置了Django Admin界面中社交功能相关模型的管理功能
"""
from django.contrib import admin
from django.utils.html import format_html

from .models import Friendship, Follow, UserBlock, UserVisit


@admin.register(Friendship)
class FriendshipAdmin(admin.ModelAdmin):
    """好友关系Admin配置"""

    list_display = (
        'requester',
        'addressee',
        'status',
        'requested_at',
        'responded_at',
    )

    list_filter = (
        'status',
        'requested_at',
        'responded_at',
    )

    search_fields = (
        'requester__username',
        'requester__email',
        'addressee__username',
        'addressee__email',
    )

    ordering = ('-requested_at',)

    fieldsets = (
        ('关系信息', {
            'fields': ('requester', 'addressee', 'status')
        }),
        ('时间信息', {
            'fields': ('requested_at', 'responded_at', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('requested_at', 'responded_at', 'created_at', 'updated_at')

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('requester', 'addressee')


@admin.register(Follow)
class FollowAdmin(admin.ModelAdmin):
    """关注关系Admin配置"""

    list_display = (
        'follower',
        'following',
        'created_at',
    )

    list_filter = (
        'created_at',
    )

    search_fields = (
        'follower__username',
        'follower__email',
        'following__username',
        'following__email',
    )

    ordering = ('-created_at',)

    fieldsets = (
        ('关注信息', {
            'fields': ('follower', 'following')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('created_at', 'updated_at')

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('follower', 'following')


@admin.register(UserBlock)
class UserBlockAdmin(admin.ModelAdmin):
    """用户屏蔽Admin配置"""

    list_display = (
        'blocker',
        'blocked',
        'reason',
        'created_at',
    )

    list_filter = (
        'created_at',
    )

    search_fields = (
        'blocker__username',
        'blocker__email',
        'blocked__username',
        'blocked__email',
        'reason',
    )

    ordering = ('-created_at',)

    fieldsets = (
        ('屏蔽信息', {
            'fields': ('blocker', 'blocked', 'reason')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('created_at', 'updated_at')

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('blocker', 'blocked')


@admin.register(UserVisit)
class UserVisitAdmin(admin.ModelAdmin):
    """用户访问记录Admin配置"""

    list_display = (
        'visitor',
        'visited',
        'visit_count',
        'updated_at',
    )

    list_filter = (
        'created_at',
        'updated_at',
    )

    search_fields = (
        'visitor__username',
        'visitor__email',
        'visited__username',
        'visited__email',
    )

    ordering = ('-updated_at',)

    fieldsets = (
        ('访问信息', {
            'fields': ('visitor', 'visited', 'visit_count')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('created_at', 'updated_at')

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('visitor', 'visited')
