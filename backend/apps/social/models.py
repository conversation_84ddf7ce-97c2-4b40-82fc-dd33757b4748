"""
元宇宙社交空间 - 社交功能模型

这个文件定义了社交功能域的所有模型，包括：
- Friendship: 好友关系模型
- Follow: 关注关系模型
- UserBlock: 用户屏蔽模型
- UserVisit: 用户访问记录模型

严格按照数据库设计文档实现，确保字段定义完全一致
"""
from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.db.models import Q

from core.models import BaseModel


class FriendshipManager(models.Manager):
    """好友关系管理器"""

    def get_friendship(self, user1, user2):
        """获取两个用户之间的好友关系"""
        try:
            return self.get(
                Q(requester=user1, addressee=user2) |
                Q(requester=user2, addressee=user1)
            )
        except self.model.DoesNotExist:
            return None

    def are_friends(self, user1, user2):
        """检查两个用户是否为好友"""
        friendship = self.get_friendship(user1, user2)
        return friendship and friendship.status == 'accepted'

    def get_friends(self, user):
        """获取用户的所有好友"""
        friendships = self.filter(
            Q(requester=user) | Q(addressee=user),
            status='accepted'
        )

        friends = []
        for friendship in friendships:
            if friendship.requester == user:
                friends.append(friendship.addressee)
            else:
                friends.append(friendship.requester)
        return friends

    def get_pending_requests(self, user):
        """获取用户收到的待处理好友请求"""
        return self.filter(addressee=user, status='pending')

    def get_sent_requests(self, user):
        """获取用户发送的待处理好友请求"""
        return self.filter(requester=user, status='pending')


class Friendship(BaseModel):
    """
    好友关系模型

    管理用户之间的好友关系，包括好友请求、接受、拒绝等状态
    对应数据库表：friendships
    """

    # 关系状态选择
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('accepted', '已接受'),
        ('declined', '已拒绝'),
        ('blocked', '已屏蔽'),
    ]

    # 请求者（发送好友请求的用户）
    requester = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='sent_friend_requests',
        verbose_name='请求者',
        help_text='发送好友请求的用户'
    )

    # 接收者（接收好友请求的用户）
    addressee = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='received_friend_requests',
        verbose_name='接收者',
        help_text='接收好友请求的用户'
    )

    # 关系状态
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='关系状态',
        help_text='好友关系的当前状态'
    )

    # 请求时间
    requested_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='请求时间',
        help_text='发送好友请求的时间'
    )

    # 响应时间（接受或拒绝的时间）
    responded_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='响应时间',
        help_text='接受或拒绝好友请求的时间'
    )

    # 自定义管理器
    objects = FriendshipManager()

    class Meta:
        db_table = 'friendships'
        verbose_name = '好友关系'
        verbose_name_plural = '好友关系'
        unique_together = ['requester', 'addressee']
        indexes = [
            models.Index(fields=['requester'], name='idx_friendships_requester'),
            models.Index(fields=['addressee'], name='idx_friendships_addressee'),
            models.Index(fields=['status'], name='idx_friendships_status'),
            models.Index(fields=['requested_at'], name='idx_friendships_requested_at'),
        ]

    def __str__(self):
        """字符串表示"""
        return f"{self.requester.username} -> {self.addressee.username} ({self.status})"

    def clean(self):
        """模型验证"""
        if self.requester == self.addressee:
            raise ValidationError('用户不能向自己发送好友请求')

    def save(self, *args, **kwargs):
        """保存时的额外处理"""
        self.clean()

        # 如果状态从pending变为其他状态，设置响应时间
        if self.pk:
            old_instance = Friendship.objects.get(pk=self.pk)
            if old_instance.status == 'pending' and self.status != 'pending':
                self.responded_at = timezone.now()

        super().save(*args, **kwargs)

    def accept(self):
        """接受好友请求"""
        if self.status == 'pending':
            self.status = 'accepted'
            self.responded_at = timezone.now()
            self.save()
            return True
        return False

    def decline(self):
        """拒绝好友请求"""
        if self.status == 'pending':
            self.status = 'declined'
            self.responded_at = timezone.now()
            self.save()
            return True
        return False

    def block(self):
        """屏蔽用户"""
        self.status = 'blocked'
        self.responded_at = timezone.now()
        self.save()
        return True


class FollowManager(models.Manager):
    """关注关系管理器"""

    def is_following(self, follower, following):
        """检查用户是否关注了另一个用户"""
        return self.filter(follower=follower, following=following).exists()

    def get_followers(self, user):
        """获取用户的粉丝列表"""
        return self.filter(following=user).select_related('follower')

    def get_following(self, user):
        """获取用户关注的人列表"""
        return self.filter(follower=user).select_related('following')

    def get_followers_count(self, user):
        """获取用户的粉丝数量"""
        return self.filter(following=user).count()

    def get_following_count(self, user):
        """获取用户关注的人数量"""
        return self.filter(follower=user).count()


class Follow(BaseModel):
    """
    关注关系模型

    管理用户之间的关注关系，支持单向关注
    对应数据库表：follows
    """

    # 关注者
    follower = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='following_set',
        verbose_name='关注者',
        help_text='发起关注的用户'
    )

    # 被关注者
    following = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='followers_set',
        verbose_name='被关注者',
        help_text='被关注的用户'
    )

    # 自定义管理器
    objects = FollowManager()

    class Meta:
        db_table = 'follows'
        verbose_name = '关注关系'
        verbose_name_plural = '关注关系'
        unique_together = ['follower', 'following']
        indexes = [
            models.Index(fields=['follower'], name='idx_follows_follower'),
            models.Index(fields=['following'], name='idx_follows_following'),
            models.Index(fields=['created_at'], name='idx_follows_created_at'),
        ]

    def __str__(self):
        """字符串表示"""
        return f"{self.follower.username} 关注 {self.following.username}"

    def clean(self):
        """模型验证"""
        if self.follower == self.following:
            raise ValidationError('用户不能关注自己')

    def save(self, *args, **kwargs):
        """保存时的额外处理"""
        self.clean()
        super().save(*args, **kwargs)


class UserBlockManager(models.Manager):
    """用户屏蔽管理器"""

    def is_blocked(self, blocker, blocked):
        """检查用户是否被屏蔽"""
        return self.filter(blocker=blocker, blocked=blocked).exists()

    def get_blocked_users(self, user):
        """获取用户屏蔽的用户列表"""
        return self.filter(blocker=user).select_related('blocked')


class UserBlock(BaseModel):
    """
    用户屏蔽模型

    管理用户之间的屏蔽关系
    对应数据库表：user_blocks
    """

    # 屏蔽者
    blocker = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='blocking_set',
        verbose_name='屏蔽者',
        help_text='发起屏蔽的用户'
    )

    # 被屏蔽者
    blocked = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='blocked_by_set',
        verbose_name='被屏蔽者',
        help_text='被屏蔽的用户'
    )

    # 屏蔽原因
    reason = models.CharField(
        max_length=200,
        blank=True,
        verbose_name='屏蔽原因',
        help_text='屏蔽的原因说明'
    )

    # 自定义管理器
    objects = UserBlockManager()

    class Meta:
        db_table = 'user_blocks'
        verbose_name = '用户屏蔽'
        verbose_name_plural = '用户屏蔽'
        unique_together = ['blocker', 'blocked']
        indexes = [
            models.Index(fields=['blocker'], name='idx_user_blocks_blocker'),
            models.Index(fields=['blocked'], name='idx_user_blocks_blocked'),
        ]

    def __str__(self):
        """字符串表示"""
        return f"{self.blocker.username} 屏蔽 {self.blocked.username}"

    def clean(self):
        """模型验证"""
        if self.blocker == self.blocked:
            raise ValidationError('用户不能屏蔽自己')

    def save(self, *args, **kwargs):
        """保存时的额外处理"""
        self.clean()
        super().save(*args, **kwargs)


class UserVisitManager(models.Manager):
    """用户访问记录管理器"""

    def record_visit(self, visitor, visited):
        """记录用户访问"""
        if visitor == visited or not visitor.is_authenticated:
            return None

        # 更新或创建访问记录
        visit, created = self.update_or_create(
            visitor=visitor,
            visited=visited,
            defaults={'visit_count': models.F('visit_count') + 1}
        )

        if created:
            visit.visit_count = 1
            visit.save()

        return visit

    def get_recent_visitors(self, user, limit=10):
        """获取最近的访客"""
        return self.filter(visited=user).order_by('-updated_at')[:limit]

    def get_visit_count(self, user):
        """获取用户被访问的总次数"""
        return self.filter(visited=user).aggregate(
            total=models.Sum('visit_count')
        )['total'] or 0


class UserVisit(BaseModel):
    """
    用户访问记录模型

    记录用户之间的访问行为
    对应数据库表：user_visits
    """

    # 访问者
    visitor = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='visits_made',
        verbose_name='访问者',
        help_text='发起访问的用户'
    )

    # 被访问者
    visited = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='visits_received',
        verbose_name='被访问者',
        help_text='被访问的用户'
    )

    # 访问次数
    visit_count = models.PositiveIntegerField(
        default=1,
        verbose_name='访问次数',
        help_text='累计访问次数'
    )

    # 最后访问时间（使用BaseModel的updated_at字段）

    # 自定义管理器
    objects = UserVisitManager()

    class Meta:
        db_table = 'user_visits'
        verbose_name = '用户访问记录'
        verbose_name_plural = '用户访问记录'
        unique_together = ['visitor', 'visited']
        indexes = [
            models.Index(fields=['visitor'], name='idx_user_visits_visitor'),
            models.Index(fields=['visited'], name='idx_user_visits_visited'),
            models.Index(fields=['updated_at'], name='idx_user_visits_updated_at'),
        ]

    def __str__(self):
        """字符串表示"""
        return f"{self.visitor.username} 访问 {self.visited.username} ({self.visit_count}次)"

    def clean(self):
        """模型验证"""
        if self.visitor == self.visited:
            raise ValidationError('用户不能访问自己')

    def save(self, *args, **kwargs):
        """保存时的额外处理"""
        self.clean()
        super().save(*args, **kwargs)
