"""
元宇宙社交空间 - 社交功能序列化器

这个文件定义了社交功能相关模型的序列化器，用于API数据的序列化和反序列化
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model

from .models import Friendship, Follow, UserBlock, UserVisit

User = get_user_model()


class UserBasicSerializer(serializers.ModelSerializer):
    """用户基础信息序列化器（用于社交功能）"""
    
    display_name = serializers.ReadOnlyField(source='get_display_name')
    avatar_url = serializers.CharField(source='profile.avatar_url', read_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'display_name', 'avatar_url',
            'is_active', 'is_verified'
        ]
        read_only_fields = ['id', 'is_active', 'is_verified']


class FriendshipSerializer(serializers.ModelSerializer):
    """好友关系序列化器"""
    
    requester = UserBasicSerializer(read_only=True)
    addressee = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = Friendship
        fields = [
            'id', 'requester', 'addressee', 'status',
            'requested_at', 'responded_at', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'requested_at', 'responded_at', 'created_at', 'updated_at'
        ]


class FriendRequestSerializer(serializers.Serializer):
    """好友请求序列化器"""
    
    addressee_id = serializers.IntegerField()
    message = serializers.CharField(max_length=200, required=False, allow_blank=True)
    
    def validate_addressee_id(self, value):
        """验证接收者ID"""
        try:
            user = User.objects.get(id=value, is_active=True)
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError('用户不存在或已被禁用')
    
    def validate(self, attrs):
        """验证好友请求"""
        request = self.context['request']
        addressee_id = attrs['addressee_id']
        
        # 不能向自己发送好友请求
        if request.user.id == addressee_id:
            raise serializers.ValidationError('不能向自己发送好友请求')
        
        # 检查是否已经是好友或已有待处理请求
        addressee = User.objects.get(id=addressee_id)
        existing_friendship = Friendship.objects.get_friendship(request.user, addressee)
        
        if existing_friendship:
            if existing_friendship.status == 'accepted':
                raise serializers.ValidationError('你们已经是好友了')
            elif existing_friendship.status == 'pending':
                raise serializers.ValidationError('已有待处理的好友请求')
            elif existing_friendship.status == 'blocked':
                raise serializers.ValidationError('无法发送好友请求')
        
        return attrs


class FollowSerializer(serializers.ModelSerializer):
    """关注关系序列化器"""
    
    follower = UserBasicSerializer(read_only=True)
    following = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = Follow
        fields = [
            'id', 'follower', 'following', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class UserBlockSerializer(serializers.ModelSerializer):
    """用户屏蔽序列化器"""
    
    blocker = UserBasicSerializer(read_only=True)
    blocked = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = UserBlock
        fields = [
            'id', 'blocker', 'blocked', 'reason', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class UserVisitSerializer(serializers.ModelSerializer):
    """用户访问记录序列化器"""
    
    visitor = UserBasicSerializer(read_only=True)
    visited = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = UserVisit
        fields = [
            'id', 'visitor', 'visited', 'visit_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class UserSearchSerializer(serializers.ModelSerializer):
    """用户搜索结果序列化器"""
    
    display_name = serializers.ReadOnlyField(source='get_display_name')
    avatar_url = serializers.CharField(source='profile.avatar_url', read_only=True)
    bio = serializers.CharField(source='profile.bio', read_only=True)
    location = serializers.CharField(source='profile.location', read_only=True)
    
    # 社交关系状态
    is_friend = serializers.SerializerMethodField()
    is_following = serializers.SerializerMethodField()
    is_followed_by = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'display_name', 'avatar_url', 'bio', 'location',
            'is_active', 'is_verified', 'is_friend', 'is_following', 'is_followed_by'
        ]
        read_only_fields = ['id', 'is_active', 'is_verified']
    
    def get_is_friend(self, obj):
        """检查是否为好友"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return Friendship.objects.are_friends(request.user, obj)
        return False
    
    def get_is_following(self, obj):
        """检查是否正在关注"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return Follow.objects.is_following(request.user, obj)
        return False
    
    def get_is_followed_by(self, obj):
        """检查是否被关注"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return Follow.objects.is_following(obj, request.user)
        return False


class UserStatsSerializer(serializers.Serializer):
    """用户社交统计序列化器"""
    
    friends_count = serializers.IntegerField()
    followers_count = serializers.IntegerField()
    following_count = serializers.IntegerField()
    visits_count = serializers.IntegerField()


class UserDetailSerializer(UserSearchSerializer):
    """用户详情序列化器"""
    
    stats = UserStatsSerializer(read_only=True)
    
    class Meta(UserSearchSerializer.Meta):
        fields = UserSearchSerializer.Meta.fields + ['stats']
