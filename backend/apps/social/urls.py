"""
元宇宙社交空间 - 社交功能URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter

from .views import (
    FriendshipViewSet, FollowViewSet, UserSearchViewSet,
    UserDetailView, UserBlockViewSet
)

app_name = 'social'

# 创建路由器
router = DefaultRouter()
router.register(r'friends', FriendshipViewSet, basename='friendship')
router.register(r'follows', FollowViewSet, basename='follow')
router.register(r'users/search', UserSearchViewSet, basename='user-search')
router.register(r'blocks', UserBlockViewSet, basename='user-block')

urlpatterns = [
    # 路由器管理的URL
    path('', include(router.urls)),

    # 用户详情
    path('users/<int:id>/', UserDetailView.as_view(), name='user-detail'),
]
