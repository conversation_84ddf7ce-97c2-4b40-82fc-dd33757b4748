"""
元宇宙社交空间 - 社交功能视图

这个文件定义了社交功能相关的API视图
"""
from rest_framework import generics, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ReadOnlyModelViewSet
from django.contrib.auth import get_user_model
from django.db.models import Q, Count
from django.shortcuts import get_object_or_404
from drf_spectacular.utils import extend_schema, OpenApiParameter

from .models import Friendship, Follow, UserBlock, UserVisit
from .serializers import (
    FriendshipSerializer, FriendRequestSerializer, FollowSerializer,
    UserBlockSerializer, UserVisitSerializer, UserSearchSerializer,
    UserDetailSerializer, UserStatsSerializer
)

User = get_user_model()


class FriendshipViewSet(ModelViewSet):
    """好友关系视图集"""
    
    serializer_class = FriendshipSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """获取当前用户相关的好友关系"""
        user = self.request.user
        return Friendship.objects.filter(
            Q(requester=user) | Q(addressee=user)
        ).select_related('requester', 'addressee')
    
    @extend_schema(
        summary="发送好友请求",
        description="向指定用户发送好友请求",
        request=FriendRequestSerializer,
        responses={201: FriendshipSerializer}
    )
    @action(detail=False, methods=['post'], url_path='request')
    def send_request(self, request):
        """发送好友请求"""
        serializer = FriendRequestSerializer(
            data=request.data,
            context={'request': request}
        )
        serializer.is_valid(raise_exception=True)
        
        addressee = User.objects.get(id=serializer.validated_data['addressee_id'])
        
        # 创建好友请求
        friendship = Friendship.objects.create(
            requester=request.user,
            addressee=addressee,
            status='pending'
        )
        
        response_serializer = FriendshipSerializer(friendship)
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)
    
    @extend_schema(
        summary="接受好友请求",
        description="接受指定的好友请求",
        responses={200: FriendshipSerializer}
    )
    @action(detail=True, methods=['post'], url_path='accept')
    def accept_request(self, request, pk=None):
        """接受好友请求"""
        friendship = get_object_or_404(
            Friendship,
            pk=pk,
            addressee=request.user,
            status='pending'
        )
        
        if friendship.accept():
            serializer = FriendshipSerializer(friendship)
            return Response(serializer.data)
        
        return Response(
            {'error': '无法接受此好友请求'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    @extend_schema(
        summary="拒绝好友请求",
        description="拒绝指定的好友请求",
        responses={200: FriendshipSerializer}
    )
    @action(detail=True, methods=['post'], url_path='decline')
    def decline_request(self, request, pk=None):
        """拒绝好友请求"""
        friendship = get_object_or_404(
            Friendship,
            pk=pk,
            addressee=request.user,
            status='pending'
        )
        
        if friendship.decline():
            serializer = FriendshipSerializer(friendship)
            return Response(serializer.data)
        
        return Response(
            {'error': '无法拒绝此好友请求'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    @extend_schema(
        summary="获取好友列表",
        description="获取当前用户的所有好友",
        responses={200: FriendshipSerializer(many=True)}
    )
    @action(detail=False, methods=['get'], url_path='list')
    def friends_list(self, request):
        """获取好友列表"""
        friendships = Friendship.objects.filter(
            Q(requester=request.user) | Q(addressee=request.user),
            status='accepted'
        ).select_related('requester', 'addressee')
        
        serializer = FriendshipSerializer(friendships, many=True)
        return Response(serializer.data)
    
    @extend_schema(
        summary="获取待处理的好友请求",
        description="获取当前用户收到的待处理好友请求",
        responses={200: FriendshipSerializer(many=True)}
    )
    @action(detail=False, methods=['get'], url_path='pending')
    def pending_requests(self, request):
        """获取待处理的好友请求"""
        friendships = Friendship.objects.get_pending_requests(request.user)
        serializer = FriendshipSerializer(friendships, many=True)
        return Response(serializer.data)
    
    @extend_schema(
        summary="删除好友关系",
        description="删除与指定用户的好友关系",
        responses={204: None}
    )
    def destroy(self, request, pk=None):
        """删除好友关系"""
        friendship = get_object_or_404(
            Friendship,
            pk=pk,
            status='accepted'
        )
        
        # 只有关系中的用户才能删除
        if request.user not in [friendship.requester, friendship.addressee]:
            return Response(
                {'error': '无权限删除此好友关系'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        friendship.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class FollowViewSet(ModelViewSet):
    """关注关系视图集"""
    
    serializer_class = FollowSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """获取当前用户相关的关注关系"""
        user = self.request.user
        return Follow.objects.filter(
            Q(follower=user) | Q(following=user)
        ).select_related('follower', 'following')
    
    @extend_schema(
        summary="关注用户",
        description="关注指定用户",
        responses={201: FollowSerializer}
    )
    @action(detail=False, methods=['post'], url_path='follow/(?P<user_id>[^/.]+)')
    def follow_user(self, request, user_id=None):
        """关注用户"""
        try:
            user_to_follow = User.objects.get(id=user_id, is_active=True)
        except User.DoesNotExist:
            return Response(
                {'error': '用户不存在'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        if request.user == user_to_follow:
            return Response(
                {'error': '不能关注自己'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 检查是否已经关注
        if Follow.objects.is_following(request.user, user_to_follow):
            return Response(
                {'error': '已经关注了此用户'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 创建关注关系
        follow = Follow.objects.create(
            follower=request.user,
            following=user_to_follow
        )
        
        serializer = FollowSerializer(follow)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    @extend_schema(
        summary="取消关注",
        description="取消关注指定用户",
        responses={204: None}
    )
    @action(detail=False, methods=['delete'], url_path='unfollow/(?P<user_id>[^/.]+)')
    def unfollow_user(self, request, user_id=None):
        """取消关注用户"""
        try:
            user_to_unfollow = User.objects.get(id=user_id, is_active=True)
        except User.DoesNotExist:
            return Response(
                {'error': '用户不存在'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        try:
            follow = Follow.objects.get(
                follower=request.user,
                following=user_to_unfollow
            )
            follow.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Follow.DoesNotExist:
            return Response(
                {'error': '未关注此用户'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @extend_schema(
        summary="获取粉丝列表",
        description="获取当前用户的粉丝列表",
        responses={200: FollowSerializer(many=True)}
    )
    @action(detail=False, methods=['get'], url_path='followers')
    def followers_list(self, request):
        """获取粉丝列表"""
        follows = Follow.objects.get_followers(request.user)
        serializer = FollowSerializer(follows, many=True)
        return Response(serializer.data)
    
    @extend_schema(
        summary="获取关注列表",
        description="获取当前用户关注的人列表",
        responses={200: FollowSerializer(many=True)}
    )
    @action(detail=False, methods=['get'], url_path='following')
    def following_list(self, request):
        """获取关注列表"""
        follows = Follow.objects.get_following(request.user)
        serializer = FollowSerializer(follows, many=True)
        return Response(serializer.data)


class UserSearchViewSet(ReadOnlyModelViewSet):
    """用户搜索视图集"""

    serializer_class = UserSearchSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取搜索结果"""
        queryset = User.objects.filter(is_active=True).select_related('profile')

        # 搜索关键词
        query = self.request.query_params.get('q', '').strip()
        if query:
            queryset = queryset.filter(
                Q(username__icontains=query) |
                Q(profile__display_name__icontains=query) |
                Q(profile__first_name__icontains=query) |
                Q(profile__last_name__icontains=query)
            )

        # 排除当前用户
        queryset = queryset.exclude(id=self.request.user.id)

        return queryset.distinct()

    @extend_schema(
        summary="搜索用户",
        description="根据关键词搜索用户",
        parameters=[
            OpenApiParameter(
                name='q',
                description='搜索关键词',
                required=False,
                type=str
            ),
        ],
        responses={200: UserSearchSerializer(many=True)}
    )
    def list(self, request, *args, **kwargs):
        """搜索用户列表"""
        return super().list(request, *args, **kwargs)

    @extend_schema(
        summary="获取推荐用户",
        description="获取推荐的用户列表",
        responses={200: UserSearchSerializer(many=True)}
    )
    @action(detail=False, methods=['get'], url_path='recommended')
    def recommended_users(self, request):
        """获取推荐用户"""
        # 简单的推荐算法：
        # 1. 排除已关注的用户
        # 2. 排除已屏蔽的用户
        # 3. 优先推荐有共同好友的用户
        # 4. 按注册时间倒序

        user = request.user

        # 获取已关注的用户ID
        following_ids = Follow.objects.filter(follower=user).values_list('following_id', flat=True)

        # 获取已屏蔽的用户ID
        blocked_ids = UserBlock.objects.filter(blocker=user).values_list('blocked_id', flat=True)

        # 获取好友ID
        friends = Friendship.objects.get_friends(user)
        friend_ids = [friend.id for friend in friends]

        # 构建推荐查询
        queryset = User.objects.filter(is_active=True).exclude(
            Q(id=user.id) |
            Q(id__in=following_ids) |
            Q(id__in=blocked_ids)
        ).select_related('profile')

        # 如果有好友，优先推荐好友的好友
        if friend_ids:
            # 获取好友的好友
            friends_of_friends = Friendship.objects.filter(
                Q(requester_id__in=friend_ids) | Q(addressee_id__in=friend_ids),
                status='accepted'
            ).exclude(
                Q(requester=user) | Q(addressee=user)
            )

            fof_ids = set()
            for friendship in friends_of_friends:
                if friendship.requester_id in friend_ids:
                    fof_ids.add(friendship.addressee_id)
                else:
                    fof_ids.add(friendship.requester_id)

            # 优先返回好友的好友
            if fof_ids:
                queryset = queryset.filter(id__in=fof_ids)

        # 限制返回数量
        queryset = queryset.order_by('-date_joined')[:20]

        serializer = UserSearchSerializer(
            queryset,
            many=True,
            context={'request': request}
        )
        return Response(serializer.data)


class UserDetailView(generics.RetrieveAPIView):
    """用户详情视图"""

    serializer_class = UserDetailSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'

    def get_queryset(self):
        """获取用户查询集"""
        return User.objects.filter(is_active=True).select_related('profile')

    def get_object(self):
        """获取用户对象并记录访问"""
        user = super().get_object()

        # 记录访问（如果不是访问自己）
        if self.request.user != user:
            UserVisit.objects.record_visit(self.request.user, user)

        return user

    def get_serializer_context(self):
        """获取序列化器上下文"""
        context = super().get_serializer_context()

        # 添加用户统计信息
        user = self.get_object()
        context['stats'] = {
            'friends_count': len(Friendship.objects.get_friends(user)),
            'followers_count': Follow.objects.get_followers_count(user),
            'following_count': Follow.objects.get_following_count(user),
            'visits_count': UserVisit.objects.get_visit_count(user),
        }

        return context

    @extend_schema(
        summary="获取用户详情",
        description="获取指定用户的详细信息",
        responses={200: UserDetailSerializer}
    )
    def get(self, request, *args, **kwargs):
        """获取用户详情"""
        return super().get(request, *args, **kwargs)
