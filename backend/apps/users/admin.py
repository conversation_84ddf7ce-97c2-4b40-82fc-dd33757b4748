"""
元宇宙社交空间 - 用户管理Admin配置

这个文件配置了Django Admin界面中用户相关模型的管理功能
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from .models import User, UserProfile, UserPreferences


class UserProfileInline(admin.StackedInline):
    """用户资料内联编辑"""
    model = UserProfile
    can_delete = False
    verbose_name = '用户资料'
    verbose_name_plural = '用户资料'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('first_name', 'last_name', 'display_name', 'bio')
        }),
        ('头像和封面', {
            'fields': ('avatar_url', 'cover_url')
        }),
        ('个人详情', {
            'fields': ('birth_date', 'gender', 'location', 'website')
        }),
    )


class UserPreferencesInline(admin.StackedInline):
    """用户偏好设置内联编辑"""
    model = UserPreferences
    can_delete = False
    verbose_name = '偏好设置'
    verbose_name_plural = '偏好设置'
    
    fieldsets = (
        ('界面设置', {
            'fields': ('language', 'timezone', 'theme')
        }),
        ('通知设置', {
            'fields': ('notification_email', 'notification_push', 'notification_sms')
        }),
        ('隐私设置', {
            'fields': ('privacy_profile', 'privacy_posts')
        }),
    )


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """用户模型Admin配置"""
    
    # 内联编辑
    inlines = (UserProfileInline, UserPreferencesInline)
    
    # 列表页显示字段
    list_display = (
        'username',
        'email',
        'get_display_name',
        'is_active',
        'is_verified',
        'is_staff',
        'last_login_at',
        'created_at',
    )
    
    # 列表页过滤器
    list_filter = (
        'is_active',
        'is_verified',
        'is_staff',
        'is_superuser',
        'created_at',
        'last_login_at',
    )
    
    # 搜索字段
    search_fields = (
        'username',
        'email',
        'first_name',
        'last_name',
        'profile__display_name',
    )
    
    # 排序字段
    ordering = ('-created_at',)
    
    # 详情页字段分组
    fieldsets = (
        ('基本信息', {
            'fields': ('username', 'email', 'phone')
        }),
        ('权限', {
            'fields': ('is_active', 'is_verified', 'is_staff', 'is_superuser', 'groups', 'user_permissions')
        }),
        ('重要日期', {
            'fields': ('last_login', 'last_login_at', 'date_joined')
        }),
    )
    
    # 添加用户时的字段
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'email', 'password1', 'password2'),
        }),
    )
    
    # 只读字段
    readonly_fields = ('last_login', 'date_joined', 'created_at', 'updated_at')
    
    def get_display_name(self, obj):
        """获取显示名称"""
        return obj.get_display_name()
    get_display_name.short_description = '显示名称'
    
    def get_queryset(self, request):
        """优化查询，预加载相关对象"""
        return super().get_queryset(request).select_related('profile')


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """用户资料Admin配置"""
    
    list_display = (
        'user',
        'get_full_name',
        'display_name',
        'gender',
        'location',
        'get_age',
        'updated_at',
    )
    
    list_filter = (
        'gender',
        'created_at',
        'updated_at',
    )
    
    search_fields = (
        'user__username',
        'user__email',
        'first_name',
        'last_name',
        'display_name',
        'location',
    )
    
    ordering = ('-updated_at',)
    
    fieldsets = (
        ('关联用户', {
            'fields': ('user',)
        }),
        ('基本信息', {
            'fields': ('first_name', 'last_name', 'display_name', 'bio')
        }),
        ('头像和封面', {
            'fields': ('avatar_url', 'cover_url')
        }),
        ('个人详情', {
            'fields': ('birth_date', 'gender', 'location', 'website')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at')
    
    def get_full_name(self, obj):
        """获取全名"""
        return obj.full_name or '未设置'
    get_full_name.short_description = '全名'
    
    def get_age(self, obj):
        """获取年龄"""
        age = obj.age
        return f"{age}岁" if age else '未设置'
    get_age.short_description = '年龄'
    
    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('user')


@admin.register(UserPreferences)
class UserPreferencesAdmin(admin.ModelAdmin):
    """用户偏好设置Admin配置"""
    
    list_display = (
        'user',
        'language',
        'theme',
        'timezone',
        'notification_email',
        'notification_push',
        'privacy_profile',
        'updated_at',
    )
    
    list_filter = (
        'language',
        'theme',
        'notification_email',
        'notification_push',
        'notification_sms',
        'privacy_profile',
        'privacy_posts',
        'created_at',
    )
    
    search_fields = (
        'user__username',
        'user__email',
        'language',
        'timezone',
    )
    
    ordering = ('-updated_at',)
    
    fieldsets = (
        ('关联用户', {
            'fields': ('user',)
        }),
        ('界面设置', {
            'fields': ('language', 'timezone', 'theme')
        }),
        ('通知设置', {
            'fields': ('notification_email', 'notification_push', 'notification_sms')
        }),
        ('隐私设置', {
            'fields': ('privacy_profile', 'privacy_posts')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at')
    
    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('user')


# 自定义Admin站点标题
admin.site.site_header = '元宇宙社交空间管理后台'
admin.site.site_title = '元宇宙社交空间'
admin.site.index_title = '欢迎使用元宇宙社交空间管理后台'
