"""
元宇宙社交空间 - 用户模型

这个文件定义了用户管理域的所有模型，包括：
- User: 用户基础信息模型
- UserProfile: 用户资料模型  
- UserPreferences: 用户偏好设置模型

严格按照数据库设计文档实现，确保字段定义完全一致
"""
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import MinLengthValidator, EmailValidator
from django.utils import timezone

from core.models import BaseModel


class User(AbstractUser):
    """
    用户模型
    
    继承Django的AbstractUser，扩展用户基础信息
    对应数据库表：users
    """
    
    # 重写邮箱字段，设为唯一且必填
    email = models.EmailField(
        unique=True,
        validators=[EmailValidator()],
        verbose_name='邮箱',
        help_text='用户邮箱地址，用于登录和通知'
    )
    
    # 手机号字段
    phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name='手机号',
        help_text='用户手机号码'
    )
    
    # 账户状态字段
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否激活',
        help_text='指定用户是否应被视为活跃的。取消选择此项而不是删除帐户。'
    )
    
    is_verified = models.BooleanField(
        default=False,
        verbose_name='是否已验证',
        help_text='用户是否已通过邮箱或手机验证'
    )
    
    # 最后登录时间（AbstractUser已有last_login，这里重新定义以添加中文说明）
    last_login_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='最后登录时间',
        help_text='用户最后一次登录的时间'
    )
    
    # 时间戳字段（AbstractUser已有date_joined，这里添加updated_at）
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间',
        help_text='账户创建时间'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间',
        help_text='账户信息最后更新时间'
    )
    
    # 设置邮箱为登录字段
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']
    
    class Meta:
        db_table = 'users'
        verbose_name = '用户'
        verbose_name_plural = '用户'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['email'], name='idx_users_email'),
            models.Index(fields=['username'], name='idx_users_username'),
            models.Index(fields=['phone'], name='idx_users_phone'),
            models.Index(fields=['created_at'], name='idx_users_created_at'),
            models.Index(fields=['is_active'], name='idx_users_is_active'),
        ]
    
    def __str__(self):
        """字符串表示"""
        return f"{self.username} ({self.email})"
    
    def get_full_name(self):
        """获取用户全名"""
        if hasattr(self, 'profile'):
            return f"{self.profile.first_name} {self.profile.last_name}".strip()
        return self.username
    
    def get_display_name(self):
        """获取显示名称"""
        if hasattr(self, 'profile') and self.profile.display_name:
            return self.profile.display_name
        return self.get_full_name() or self.username
    
    def update_last_login(self):
        """更新最后登录时间"""
        self.last_login_at = timezone.now()
        self.save(update_fields=['last_login_at'])


class UserProfile(models.Model):
    """
    用户资料模型
    
    存储用户的详细资料信息
    对应数据库表：user_profiles
    """
    
    # 与用户的一对一关系
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='profile',
        verbose_name='用户',
        help_text='关联的用户账户'
    )
    
    # 姓名字段
    first_name = models.CharField(
        max_length=50,
        blank=True,
        verbose_name='名',
        help_text='用户的名'
    )
    
    last_name = models.CharField(
        max_length=50,
        blank=True,
        verbose_name='姓',
        help_text='用户的姓'
    )
    
    display_name = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='显示名称',
        help_text='用户自定义的显示名称'
    )
    
    # 个人信息字段
    bio = models.TextField(
        max_length=500,
        blank=True,
        verbose_name='个人简介',
        help_text='用户的个人简介，最多500字符'
    )
    
    avatar_url = models.URLField(
        max_length=500,
        blank=True,
        verbose_name='头像URL',
        help_text='用户头像图片的URL地址'
    )
    
    cover_url = models.URLField(
        max_length=500,
        blank=True,
        verbose_name='封面图URL',
        help_text='用户封面图片的URL地址'
    )
    
    # 个人详细信息
    birth_date = models.DateField(
        null=True,
        blank=True,
        verbose_name='生日',
        help_text='用户的出生日期'
    )
    
    GENDER_CHOICES = [
        ('male', '男'),
        ('female', '女'),
        ('other', '其他'),
        ('prefer_not_to_say', '不愿透露'),
    ]
    
    gender = models.CharField(
        max_length=20,
        choices=GENDER_CHOICES,
        blank=True,
        verbose_name='性别',
        help_text='用户的性别'
    )
    
    location = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='所在地',
        help_text='用户的所在地'
    )
    
    website = models.URLField(
        max_length=200,
        blank=True,
        verbose_name='个人网站',
        help_text='用户的个人网站URL'
    )
    
    # 时间戳字段
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间',
        help_text='资料创建时间'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间',
        help_text='资料最后更新时间'
    )
    
    class Meta:
        db_table = 'user_profiles'
        verbose_name = '用户资料'
        verbose_name_plural = '用户资料'
        indexes = [
            models.Index(fields=['user'], name='idx_user_profiles_user'),
            models.Index(fields=['display_name'], name='idx_user_profiles_display_name'),
            models.Index(fields=['location'], name='idx_user_profiles_location'),
        ]
    
    def __str__(self):
        """字符串表示"""
        return f"{self.user.username}的资料"
    
    @property
    def full_name(self):
        """获取全名"""
        return f"{self.first_name} {self.last_name}".strip()
    
    @property
    def age(self):
        """计算年龄"""
        if self.birth_date:
            today = timezone.now().date()
            return today.year - self.birth_date.year - (
                (today.month, today.day) < (self.birth_date.month, self.birth_date.day)
            )
        return None


class UserPreferences(models.Model):
    """
    用户偏好设置模型
    
    存储用户的个性化偏好设置
    对应数据库表：user_preferences
    """
    
    # 与用户的一对一关系
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='preferences',
        verbose_name='用户',
        help_text='关联的用户账户'
    )
    
    # 语言和地区设置
    language = models.CharField(
        max_length=10,
        default='zh-CN',
        verbose_name='语言偏好',
        help_text='用户的界面语言偏好'
    )
    
    timezone = models.CharField(
        max_length=50,
        default='Asia/Shanghai',
        verbose_name='时区',
        help_text='用户所在的时区'
    )
    
    # 主题设置
    THEME_CHOICES = [
        ('light', '浅色主题'),
        ('dark', '深色主题'),
        ('auto', '自动'),
    ]
    
    theme = models.CharField(
        max_length=10,
        choices=THEME_CHOICES,
        default='auto',
        verbose_name='主题偏好',
        help_text='用户的界面主题偏好'
    )
    
    # 通知设置
    notification_email = models.BooleanField(
        default=True,
        verbose_name='邮件通知',
        help_text='是否接收邮件通知'
    )
    
    notification_push = models.BooleanField(
        default=True,
        verbose_name='推送通知',
        help_text='是否接收推送通知'
    )
    
    notification_sms = models.BooleanField(
        default=False,
        verbose_name='短信通知',
        help_text='是否接收短信通知'
    )
    
    # 隐私设置
    PRIVACY_CHOICES = [
        ('public', '公开'),
        ('friends', '好友可见'),
        ('private', '私有'),
    ]
    
    privacy_profile = models.CharField(
        max_length=10,
        choices=PRIVACY_CHOICES,
        default='public',
        verbose_name='资料隐私',
        help_text='个人资料的可见性设置'
    )
    
    privacy_posts = models.CharField(
        max_length=10,
        choices=PRIVACY_CHOICES,
        default='public',
        verbose_name='动态隐私',
        help_text='发布动态的默认可见性设置'
    )
    
    # 时间戳字段
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间',
        help_text='偏好设置创建时间'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间',
        help_text='偏好设置最后更新时间'
    )
    
    class Meta:
        db_table = 'user_preferences'
        verbose_name = '用户偏好设置'
        verbose_name_plural = '用户偏好设置'
        indexes = [
            models.Index(fields=['user'], name='idx_user_preferences_user'),
            models.Index(fields=['language'], name='idx_user_preferences_language'),
            models.Index(fields=['theme'], name='idx_user_preferences_theme'),
        ]
    
    def __str__(self):
        """字符串表示"""
        return f"{self.user.username}的偏好设置"
