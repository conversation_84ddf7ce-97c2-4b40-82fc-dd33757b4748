"""
元宇宙社交空间 - 用户序列化器

这个文件定义了用户相关模型的序列化器，用于API数据的序列化和反序列化
"""
from rest_framework import serializers
from django.contrib.auth.password_validation import validate_password
from django.contrib.auth import authenticate

from .models import User, UserProfile, UserPreferences


class UserProfileSerializer(serializers.ModelSerializer):
    """用户资料序列化器"""
    
    full_name = serializers.ReadOnlyField()
    age = serializers.ReadOnlyField()
    
    class Meta:
        model = UserProfile
        fields = [
            'first_name', 'last_name', 'display_name', 'bio',
            'avatar_url', 'cover_url', 'birth_date', 'gender',
            'location', 'website', 'full_name', 'age',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class UserPreferencesSerializer(serializers.ModelSerializer):
    """用户偏好设置序列化器"""
    
    class Meta:
        model = UserPreferences
        fields = [
            'language', 'timezone', 'theme',
            'notification_email', 'notification_push', 'notification_sms',
            'privacy_profile', 'privacy_posts',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class UserSerializer(serializers.ModelSerializer):
    """用户序列化器"""
    
    profile = UserProfileSerializer(read_only=True)
    preferences = UserPreferencesSerializer(read_only=True)
    display_name = serializers.ReadOnlyField(source='get_display_name')
    full_name = serializers.ReadOnlyField(source='get_full_name')
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'phone',
            'is_active', 'is_verified', 'last_login_at',
            'created_at', 'updated_at',
            'display_name', 'full_name',
            'profile', 'preferences'
        ]
        read_only_fields = [
            'id', 'is_verified', 'last_login_at',
            'created_at', 'updated_at'
        ]


class UserCreateSerializer(serializers.ModelSerializer):
    """用户创建序列化器"""
    
    password = serializers.CharField(
        write_only=True,
        validators=[validate_password],
        style={'input_type': 'password'}
    )
    password_confirm = serializers.CharField(
        write_only=True,
        style={'input_type': 'password'}
    )
    
    class Meta:
        model = User
        fields = [
            'username', 'email', 'password', 'password_confirm', 'phone'
        ]
    
    def validate(self, attrs):
        """验证密码确认"""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("密码确认不匹配")
        return attrs
    
    def create(self, validated_data):
        """创建用户"""
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        
        # 创建用户资料和偏好设置
        UserProfile.objects.create(user=user)
        UserPreferences.objects.create(user=user)
        
        return user


class UserUpdateSerializer(serializers.ModelSerializer):
    """用户更新序列化器"""
    
    class Meta:
        model = User
        fields = ['username', 'phone']


class LoginSerializer(serializers.Serializer):
    """登录序列化器"""
    
    email = serializers.EmailField()
    password = serializers.CharField(style={'input_type': 'password'})
    
    def validate(self, attrs):
        """验证登录凭据"""
        email = attrs.get('email')
        password = attrs.get('password')
        
        if email and password:
            user = authenticate(
                request=self.context.get('request'),
                username=email,
                password=password
            )
            
            if not user:
                raise serializers.ValidationError('邮箱或密码错误')
            
            if not user.is_active:
                raise serializers.ValidationError('账户已被禁用')
            
            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError('必须提供邮箱和密码')


class ChangePasswordSerializer(serializers.Serializer):
    """修改密码序列化器"""

    old_password = serializers.CharField(style={'input_type': 'password'})
    new_password = serializers.CharField(
        validators=[validate_password],
        style={'input_type': 'password'}
    )
    new_password_confirm = serializers.CharField(style={'input_type': 'password'})

    def validate_old_password(self, value):
        """验证旧密码"""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError('旧密码错误')
        return value

    def validate(self, attrs):
        """验证新密码确认"""
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError('新密码确认不匹配')
        return attrs

    def save(self):
        """保存新密码"""
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()
        return user


class ForgotPasswordSerializer(serializers.Serializer):
    """忘记密码序列化器"""

    email = serializers.EmailField()

    def validate_email(self, value):
        """验证邮箱是否存在"""
        try:
            user = User.objects.get(email=value, is_active=True)
            self.context['user'] = user
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError('该邮箱未注册或账户已被禁用')


class ResetPasswordSerializer(serializers.Serializer):
    """重置密码序列化器"""

    token = serializers.CharField()
    new_password = serializers.CharField(
        validators=[validate_password],
        style={'input_type': 'password'}
    )
    new_password_confirm = serializers.CharField(style={'input_type': 'password'})

    def validate(self, attrs):
        """验证新密码确认"""
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError('新密码确认不匹配')
        return attrs


class EmailVerificationSerializer(serializers.Serializer):
    """邮箱验证序列化器"""

    token = serializers.CharField()

    def validate_token(self, value):
        """验证token"""
        # TODO: 实现token验证逻辑
        return value
