"""
元宇宙社交空间 - 用户信号处理器

这个文件定义了用户相关的信号处理器，用于自动化处理用户相关的操作
"""
from django.db.models.signals import post_save
from django.dispatch import receiver

from .models import User, UserProfile, UserPreferences


@receiver(post_save, sender=User)
def create_user_profile_and_preferences(sender, instance, created, **kwargs):
    """
    用户创建后自动创建用户资料和偏好设置
    
    当新用户被创建时，自动为其创建对应的UserProfile和UserPreferences记录
    """
    if created:
        # 创建用户资料
        UserProfile.objects.get_or_create(user=instance)
        
        # 创建用户偏好设置
        UserPreferences.objects.get_or_create(user=instance)


@receiver(post_save, sender=User)
def save_user_profile_and_preferences(sender, instance, **kwargs):
    """
    用户保存时确保资料和偏好设置存在
    
    确保每个用户都有对应的资料和偏好设置记录
    """
    # 确保用户资料存在
    if not hasattr(instance, 'profile'):
        UserProfile.objects.get_or_create(user=instance)
    
    # 确保用户偏好设置存在
    if not hasattr(instance, 'preferences'):
        UserPreferences.objects.get_or_create(user=instance)
