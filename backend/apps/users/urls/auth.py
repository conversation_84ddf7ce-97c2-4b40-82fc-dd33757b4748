"""
元宇宙社交空间 - 认证相关URL配置
"""
from django.urls import path
from rest_framework_simplejwt.views import (
    TokenRefreshView,
    TokenVerifyView,
)

from ..views import (
    LoginView, RegisterView, LogoutView,
    ForgotPasswordView, ResetPasswordView, EmailVerificationView
)

app_name = 'auth'

urlpatterns = [
    # 用户认证
    path('login/', LoginView.as_view(), name='login'),
    path('register/', RegisterView.as_view(), name='register'),
    path('logout/', LogoutView.as_view(), name='logout'),

    # 密码管理
    path('forgot-password/', ForgotPasswordView.as_view(), name='forgot_password'),
    path('reset-password/', ResetPasswordView.as_view(), name='reset_password'),

    # 邮箱验证
    path('verify-email/', EmailVerificationView.as_view(), name='verify_email'),

    # JWT令牌管理
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('token/verify/', TokenVerifyView.as_view(), name='token_verify'),
]
