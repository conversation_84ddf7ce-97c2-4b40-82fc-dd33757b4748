"""
元宇宙社交空间 - 用户视图

这个文件定义了用户相关的API视图
"""
from rest_framework import generics, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.token_blacklist.models import OutstandingToken, BlacklistedToken
from django.contrib.auth import login
from django.core.mail import send_mail
from django.conf import settings
from django.utils.crypto import get_random_string
from django.utils import timezone
from drf_spectacular.utils import extend_schema, OpenApiParameter

from .models import User, UserProfile, UserPreferences
from .serializers import (
    UserSerializer, UserCreateSerializer, UserUpdateSerializer,
    UserProfileSerializer, UserPreferencesSerializer,
    LoginSerializer, ChangePasswordSerializer,
    ForgotPasswordSerializer, ResetPasswordSerializer, EmailVerificationSerializer
)


class UserViewSet(ModelViewSet):
    """用户视图集"""
    
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return UserCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return UserUpdateSerializer
        return UserSerializer
    
    def get_permissions(self):
        """根据动作设置权限"""
        if self.action == 'create':
            permission_classes = [permissions.AllowAny]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    @extend_schema(
        summary="获取当前用户信息",
        description="获取当前登录用户的详细信息",
        responses={200: UserSerializer}
    )
    @action(detail=False, methods=['get'])
    def me(self, request):
        """获取当前用户信息"""
        serializer = self.get_serializer(request.user)
        return Response(serializer.data)
    
    @extend_schema(
        summary="更新用户资料",
        description="更新当前用户的资料信息",
        request=UserProfileSerializer,
        responses={200: UserProfileSerializer}
    )
    @action(detail=False, methods=['put', 'patch'], url_path='profile')
    def update_profile(self, request):
        """更新用户资料"""
        profile, created = UserProfile.objects.get_or_create(user=request.user)
        serializer = UserProfileSerializer(
            profile, 
            data=request.data, 
            partial=request.method == 'PATCH'
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)
    
    @extend_schema(
        summary="更新用户偏好设置",
        description="更新当前用户的偏好设置",
        request=UserPreferencesSerializer,
        responses={200: UserPreferencesSerializer}
    )
    @action(detail=False, methods=['put', 'patch'], url_path='preferences')
    def update_preferences(self, request):
        """更新用户偏好设置"""
        preferences, created = UserPreferences.objects.get_or_create(user=request.user)
        serializer = UserPreferencesSerializer(
            preferences,
            data=request.data,
            partial=request.method == 'PATCH'
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)
    
    @extend_schema(
        summary="修改密码",
        description="修改当前用户的登录密码",
        request=ChangePasswordSerializer,
        responses={200: {"message": "密码修改成功"}}
    )
    @action(detail=False, methods=['post'], url_path='change-password')
    def change_password(self, request):
        """修改密码"""
        serializer = ChangePasswordSerializer(
            data=request.data,
            context={'request': request}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response({'message': '密码修改成功'})


class LoginView(generics.GenericAPIView):
    """登录视图"""
    
    serializer_class = LoginSerializer
    permission_classes = [permissions.AllowAny]
    
    @extend_schema(
        summary="用户登录",
        description="使用邮箱和密码登录，返回JWT令牌",
        responses={
            200: {
                "type": "object",
                "properties": {
                    "user": UserSerializer,
                    "tokens": {
                        "type": "object",
                        "properties": {
                            "access": {"type": "string"},
                            "refresh": {"type": "string"}
                        }
                    }
                }
            }
        }
    )
    def post(self, request):
        """用户登录"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        user = serializer.validated_data['user']
        
        # 更新最后登录时间
        user.update_last_login()
        
        # 生成JWT令牌
        refresh = RefreshToken.for_user(user)
        
        # 返回用户信息和令牌
        user_serializer = UserSerializer(user)
        return Response({
            'user': user_serializer.data,
            'tokens': {
                'access': str(refresh.access_token),
                'refresh': str(refresh)
            }
        })


class RegisterView(generics.CreateAPIView):
    """注册视图"""
    
    queryset = User.objects.all()
    serializer_class = UserCreateSerializer
    permission_classes = [permissions.AllowAny]
    
    @extend_schema(
        summary="用户注册",
        description="创建新用户账户",
        responses={
            201: {
                "type": "object",
                "properties": {
                    "user": UserSerializer,
                    "tokens": {
                        "type": "object",
                        "properties": {
                            "access": {"type": "string"},
                            "refresh": {"type": "string"}
                        }
                    }
                }
            }
        }
    )
    def create(self, request, *args, **kwargs):
        """创建用户"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()
        
        # 生成JWT令牌
        refresh = RefreshToken.for_user(user)
        
        # 返回用户信息和令牌
        user_serializer = UserSerializer(user)
        return Response({
            'user': user_serializer.data,
            'tokens': {
                'access': str(refresh.access_token),
                'refresh': str(refresh)
            }
        }, status=status.HTTP_201_CREATED)


class LogoutView(generics.GenericAPIView):
    """登出视图"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="用户登出",
        description="撤销JWT令牌并加入黑名单",
        request=None,
        responses={200: {"message": "登出成功"}}
    )
    def post(self, request):
        """用户登出"""
        try:
            # 获取refresh token
            refresh_token = request.data.get('refresh_token')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()

            return Response({'message': '登出成功'})
        except Exception as e:
            return Response(
                {'error': '登出失败'},
                status=status.HTTP_400_BAD_REQUEST
            )


class ForgotPasswordView(generics.GenericAPIView):
    """忘记密码视图"""

    serializer_class = ForgotPasswordSerializer
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="忘记密码",
        description="发送密码重置邮件",
        responses={200: {"message": "密码重置邮件已发送"}}
    )
    def post(self, request):
        """发送密码重置邮件"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = serializer.context['user']

        # 生成重置token
        reset_token = get_random_string(32)

        # TODO: 保存token到数据库或缓存
        # 这里简化处理，实际应该保存到数据库

        # 发送邮件
        try:
            send_mail(
                subject='密码重置',
                message=f'请点击以下链接重置密码：{settings.FRONTEND_URL}/auth/reset-password?token={reset_token}',
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                fail_silently=False,
            )

            return Response({'message': '密码重置邮件已发送'})
        except Exception as e:
            return Response(
                {'error': '邮件发送失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ResetPasswordView(generics.GenericAPIView):
    """重置密码视图"""

    serializer_class = ResetPasswordSerializer
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="重置密码",
        description="使用token重置密码",
        responses={200: {"message": "密码重置成功"}}
    )
    def post(self, request):
        """重置密码"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # TODO: 验证token并获取用户
        # 这里简化处理，实际应该从数据库验证token

        return Response({'message': '密码重置成功'})


class EmailVerificationView(generics.GenericAPIView):
    """邮箱验证视图"""

    serializer_class = EmailVerificationSerializer
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="邮箱验证",
        description="验证用户邮箱",
        responses={200: {"message": "邮箱验证成功"}}
    )
    def post(self, request):
        """验证邮箱"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # TODO: 验证token并激活用户
        # 这里简化处理，实际应该从数据库验证token

        return Response({'message': '邮箱验证成功'})
