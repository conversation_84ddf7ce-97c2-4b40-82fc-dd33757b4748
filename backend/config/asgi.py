"""
元宇宙社交空间 - ASGI配置

ASGI (Asynchronous Server Gateway Interface) 配置文件，用于支持异步功能和WebSocket。

这个文件暴露了ASGI可调用对象作为模块级变量 `application`。

更多关于ASGI的信息，请参考：
https://docs.djangoproject.com/en/4.2/howto/deployment/asgi/

使用方法：
- 开发环境：python manage.py runserver（自动使用ASGI）
- 生产环境：daphne config.asgi:application
"""
import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
from channels.security.websocket import AllowedHostsOriginValidator

# 设置Django配置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.production')

# 初始化Django ASGI应用
django_asgi_app = get_asgi_application()

# 导入WebSocket路由
try:
    from apps.messaging.routing import websocket_urlpatterns
except ImportError:
    # 如果路由文件不存在，使用空列表
    websocket_urlpatterns = []

# ASGI应用配置
application = ProtocolTypeRouter({
    # HTTP协议处理
    "http": django_asgi_app,
    
    # WebSocket协议处理
    "websocket": AllowedHostsOriginValidator(
        AuthMiddlewareStack(
            URLRouter(websocket_urlpatterns)
        )
    ),
})

# 生产环境可以在这里添加额外的中间件
# 例如：CORS中间件、限流中间件等
