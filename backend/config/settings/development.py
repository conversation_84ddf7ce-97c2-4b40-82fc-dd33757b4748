"""
元宇宙社交空间 - 开发环境配置

这个文件包含了开发环境特有的配置，包括：
- 调试模式开启
- 开发工具配置
- 宽松的安全设置
- 详细的日志输出
"""
from .base import *

# 调试模式
DEBUG = True

# 允许的主机（开发环境）
ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0']

# 开发环境专用应用
DEVELOPMENT_APPS = [
    'django_extensions',  # Django扩展工具
    'debug_toolbar',      # 调试工具栏
]

INSTALLED_APPS += DEVELOPMENT_APPS

# 开发环境中间件
DEVELOPMENT_MIDDLEWARE = [
    'debug_toolbar.middleware.DebugToolbarMiddleware',
]

MIDDLEWARE = DEVELOPMENT_MIDDLEWARE + MIDDLEWARE

# 调试工具栏配置
INTERNAL_IPS = [
    '127.0.0.1',
    'localhost',
]

DEBUG_TOOLBAR_CONFIG = {
    'SHOW_TOOLBAR_CALLBACK': lambda request: DEBUG,
}

# CORS配置（开发环境允许所有来源）
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

# 邮件配置（开发环境使用控制台后端）
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# 缓存配置（开发环境使用本地内存缓存）
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
    }
}

# 日志配置（开发环境更详细的日志）
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'DEBUG',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.db.backends': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'apps': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# 开发环境安全设置（较宽松）
SECURE_SSL_REDIRECT = False
SECURE_HSTS_SECONDS = 0
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False
SECURE_CONTENT_TYPE_NOSNIFF = False
SECURE_BROWSER_XSS_FILTER = False
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False

# 开发环境数据库配置（如果需要特殊配置）
# DATABASES['default']['OPTIONS']['init_command'] = "SET sql_mode='STRICT_TRANS_TABLES'"

print("🚀 开发环境配置已加载")
