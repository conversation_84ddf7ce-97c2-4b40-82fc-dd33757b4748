"""
元宇宙社交空间 - 测试环境配置

这个文件包含了测试环境特有的配置，包括：
- 内存数据库配置
- 快速缓存配置
- 简化的认证配置
- 测试专用设置
"""
from .base import *

# 调试模式（测试环境开启以便调试）
DEBUG = True

# 允许的主机
ALLOWED_HOSTS = ['localhost', '127.0.0.1', 'testserver']

# 数据库配置（测试环境使用内存数据库）
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }
}

# 缓存配置（测试环境使用本地内存缓存）
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'test-cache',
    }
}

# 密码哈希配置（测试环境使用快速哈希）
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',
]

# 邮件配置（测试环境使用内存后端）
EMAIL_BACKEND = 'django.core.mail.backends.locmem.EmailBackend'

# 媒体文件配置（测试环境使用临时目录）
import tempfile
MEDIA_ROOT = tempfile.mkdtemp()

# 静态文件配置（测试环境不收集静态文件）
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# Celery配置（测试环境同步执行任务）
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True

# Channels配置（测试环境使用内存层）
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels.layers.InMemoryChannelLayer',
    },
}

# 日志配置（测试环境最小日志）
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'level': 'ERROR',
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'ERROR',
    },
}

# 安全设置（测试环境宽松设置）
SECURE_SSL_REDIRECT = False
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False

# CORS配置（测试环境允许所有来源）
CORS_ALLOW_ALL_ORIGINS = True

# JWT配置（测试环境使用较短的过期时间）
SIMPLE_JWT.update({
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=5),
    'REFRESH_TOKEN_LIFETIME': timedelta(minutes=10),
})

# 测试数据库配置
TEST_RUNNER = 'django.test.runner.DiscoverRunner'

# 禁用迁移（加快测试速度）
class DisableMigrations:
    def __contains__(self, item):
        return True
    
    def __getitem__(self, item):
        return None

MIGRATION_MODULES = DisableMigrations()

print("🧪 测试环境配置已加载")
