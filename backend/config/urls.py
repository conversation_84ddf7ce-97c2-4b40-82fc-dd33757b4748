"""
元宇宙社交空间 - 主URL配置

这个文件定义了项目的主要URL路由，包括：
- API路由
- 管理后台路由
- 静态文件路由
- API文档路由
- WebSocket路由
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import JsonResponse
from drf_spectacular.views import (
    SpectacularAPIView,
    SpectacularSwaggerView,
    SpectacularRedocView,
)

def api_root(request):
    """
    API根路径信息页面
    
    返回API的基本信息，包括版本、状态、可用端点等
    """
    return JsonResponse({
        'name': '元宇宙社交空间 API',
        'version': '1.0.0',
        'status': 'running',
        'description': '元宇宙社交空间后端API服务',
        'endpoints': {
            'api_root': '/api/v1/',
            'admin': '/admin/',
            'docs': '/api/docs/',
            'redoc': '/api/redoc/',
            'schema': '/api/schema/',
            'auth': '/api/v1/auth/',
            'users': '/api/v1/users/',
            'social': '/api/v1/social/',
            'messaging': '/api/v1/messaging/',
            'content': '/api/v1/content/',
        },
        'authentication': {
            'type': 'JWT Bearer Token',
            'header': 'Authorization: Bearer <token>',
            'login': '/api/v1/auth/login/',
            'refresh': '/api/v1/auth/refresh/',
        },
        'websocket': {
            'endpoint': '/ws/',
            'authentication': 'JWT Token in query parameter',
        }
    })

# 主URL模式
urlpatterns = [
    # API根路径
    path('', api_root, name='api-root'),
    
    # 管理后台
    path('admin/', admin.site.urls),
    
    # API文档
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
    
    # API v1路由
    path('api/v1/', include([
        # 认证相关路由
        path('auth/', include('apps.users.urls.auth')),
        
        # 用户管理路由
        path('users/', include('apps.users.urls.users')),
        
        # 社交功能路由
        path('social/', include('apps.social.urls')),
        
        # 消息系统路由
        path('messaging/', include('apps.messaging.urls')),
        
        # 内容管理路由
        path('content/', include('apps.content.urls')),
    ])),
]

# 开发环境特殊配置
if settings.DEBUG:
    # 静态文件和媒体文件服务
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    
    # 调试工具栏
    if 'debug_toolbar' in settings.INSTALLED_APPS:
        import debug_toolbar
        urlpatterns = [
            path('__debug__/', include(debug_toolbar.urls)),
        ] + urlpatterns

# 自定义管理后台配置
admin.site.site_header = '元宇宙社交空间管理后台'
admin.site.site_title = '元宇宙社交空间'
admin.site.index_title = '欢迎使用元宇宙社交空间管理后台'
