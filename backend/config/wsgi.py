"""
元宇宙社交空间 - WSGI配置

WSGI (Web Server Gateway Interface) 配置文件，用于部署Django应用到生产环境。

这个文件暴露了WSGI可调用对象作为模块级变量 `application`。

更多关于WSGI的信息，请参考：
https://docs.djangoproject.com/en/4.2/howto/deployment/wsgi/

使用方法：
- 开发环境：python manage.py runserver
- 生产环境：gunicorn config.wsgi:application
"""
import os
from django.core.wsgi import get_wsgi_application

# 设置Django配置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.production')

# 获取WSGI应用
application = get_wsgi_application()

# 生产环境可以在这里添加中间件
# 例如：WhiteNoise静态文件服务
try:
    from whitenoise import WhiteNoise
    application = WhiteNoise(application)
    # 添加静态文件目录
    application.add_files('/path/to/static/files', prefix='/static/')
except ImportError:
    # WhiteNoise未安装时跳过
    pass
