"""
元宇宙社交空间 - 核心应用配置
"""
from django.apps import AppConfig


class CoreConfig(AppConfig):
    """核心应用配置类"""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'core'
    verbose_name = '核心模块'
    
    def ready(self):
        """应用准备就绪时的初始化操作"""
        # 导入信号处理器
        try:
            import core.signals  # noqa
        except ImportError:
            pass
        
        # 导入任务定义
        try:
            import core.tasks  # noqa
        except ImportError:
            pass
