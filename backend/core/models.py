"""
元宇宙社交空间 - 核心模型

这个文件定义了项目中所有模型的基础类，包括：
- 时间戳字段
- 软删除功能
- 通用字段
- 模型管理器
"""
from django.db import models
from django.utils import timezone
from django.contrib.auth import get_user_model
import uuid


class TimestampedModel(models.Model):
    """
    时间戳基础模型
    
    为所有继承的模型自动添加创建时间和更新时间字段
    """
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间',
        help_text='记录创建的时间'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间',
        help_text='记录最后更新的时间'
    )
    
    class Meta:
        abstract = True
        ordering = ['-created_at']


class SoftDeleteManager(models.Manager):
    """软删除管理器"""
    
    def get_queryset(self):
        """只返回未删除的记录"""
        return super().get_queryset().filter(is_deleted=False)
    
    def with_deleted(self):
        """返回包括已删除的所有记录"""
        return super().get_queryset()
    
    def deleted_only(self):
        """只返回已删除的记录"""
        return super().get_queryset().filter(is_deleted=True)


class SoftDeleteModel(TimestampedModel):
    """
    软删除基础模型
    
    提供软删除功能，删除时只标记为已删除而不真正删除数据
    """
    is_deleted = models.BooleanField(
        default=False,
        verbose_name='是否已删除',
        help_text='标记记录是否已被软删除'
    )
    deleted_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='删除时间',
        help_text='记录被软删除的时间'
    )
    
    objects = SoftDeleteManager()
    all_objects = models.Manager()  # 包含已删除记录的管理器
    
    class Meta:
        abstract = True
    
    def delete(self, using=None, keep_parents=False):
        """软删除方法"""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save(using=using, update_fields=['is_deleted', 'deleted_at'])
    
    def hard_delete(self, using=None, keep_parents=False):
        """硬删除方法（真正删除数据）"""
        super().delete(using=using, keep_parents=keep_parents)
    
    def restore(self):
        """恢复软删除的记录"""
        self.is_deleted = False
        self.deleted_at = None
        self.save(update_fields=['is_deleted', 'deleted_at'])


class UUIDModel(models.Model):
    """
    UUID基础模型
    
    使用UUID作为主键的模型基类
    """
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        verbose_name='唯一标识符'
    )
    
    class Meta:
        abstract = True


class BaseModel(SoftDeleteModel):
    """
    项目基础模型
    
    结合了时间戳和软删除功能的基础模型
    所有业务模型都应该继承这个类
    """
    
    class Meta:
        abstract = True
    
    def __str__(self):
        """默认字符串表示"""
        if hasattr(self, 'name'):
            return self.name
        elif hasattr(self, 'title'):
            return self.title
        else:
            return f"{self.__class__.__name__}({self.pk})"


class UserRelatedModel(BaseModel):
    """
    与用户相关的基础模型
    
    为需要关联用户的模型提供基础字段
    """
    created_by = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)s_created',
        verbose_name='创建者',
        help_text='创建此记录的用户'
    )
    updated_by = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)s_updated',
        verbose_name='更新者',
        help_text='最后更新此记录的用户'
    )
    
    class Meta:
        abstract = True
    
    def save(self, *args, **kwargs):
        """保存时自动设置创建者和更新者"""
        user = kwargs.pop('user', None)
        if user and user.is_authenticated:
            if not self.pk:  # 新建记录
                self.created_by = user
            self.updated_by = user
        super().save(*args, **kwargs)


class VisibilityChoices(models.TextChoices):
    """可见性选择"""
    PUBLIC = 'public', '公开'
    FRIENDS = 'friends', '好友可见'
    PRIVATE = 'private', '私有'


class StatusChoices(models.TextChoices):
    """状态选择"""
    ACTIVE = 'active', '活跃'
    INACTIVE = 'inactive', '非活跃'
    PENDING = 'pending', '待处理'
    APPROVED = 'approved', '已批准'
    REJECTED = 'rejected', '已拒绝'
    SUSPENDED = 'suspended', '已暂停'


class PriorityChoices(models.TextChoices):
    """优先级选择"""
    LOW = 'low', '低'
    MEDIUM = 'medium', '中'
    HIGH = 'high', '高'
    URGENT = 'urgent', '紧急'
