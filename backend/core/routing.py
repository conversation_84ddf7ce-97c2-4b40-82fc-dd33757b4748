"""
元宇宙社交空间 - WebSocket路由配置

这个文件定义了WebSocket的路由配置，包括：
- 实时聊天路由
- 通知推送路由
- 在线状态路由
- 系统广播路由
"""
from django.urls import re_path
from channels.routing import URLRouter

# 导入WebSocket消费者（稍后创建）
try:
    from apps.messaging.consumers import ChatConsumer
    from apps.users.consumers import NotificationConsumer, OnlineStatusConsumer
    from core.consumers import SystemConsumer
except ImportError:
    # 如果消费者还未创建，使用占位符
    ChatConsumer = None
    NotificationConsumer = None
    OnlineStatusConsumer = None
    SystemConsumer = None

# WebSocket URL模式
websocket_urlpatterns = []

# 聊天相关WebSocket路由
if ChatConsumer:
    websocket_urlpatterns.extend([
        re_path(r'ws/chat/(?P<conversation_id>\d+)/$', ChatConsumer.as_asgi()),
        re_path(r'ws/chat/private/(?P<user_id>\d+)/$', ChatConsumer.as_asgi()),
    ])

# 通知相关WebSocket路由
if NotificationConsumer:
    websocket_urlpatterns.extend([
        re_path(r'ws/notifications/$', NotificationConsumer.as_asgi()),
    ])

# 在线状态WebSocket路由
if OnlineStatusConsumer:
    websocket_urlpatterns.extend([
        re_path(r'ws/status/$', OnlineStatusConsumer.as_asgi()),
    ])

# 系统广播WebSocket路由
if SystemConsumer:
    websocket_urlpatterns.extend([
        re_path(r'ws/system/$', SystemConsumer.as_asgi()),
    ])

# 如果没有可用的消费者，提供一个默认的空路由
if not websocket_urlpatterns:
    from channels.generic.websocket import AsyncWebsocketConsumer
    
    class PlaceholderConsumer(AsyncWebsocketConsumer):
        """占位符WebSocket消费者"""
        
        async def connect(self):
            await self.accept()
            await self.send(text_data='WebSocket连接成功，但功能尚未实现')
        
        async def disconnect(self, close_code):
            pass
        
        async def receive(self, text_data):
            await self.send(text_data=f'收到消息: {text_data}')
    
    websocket_urlpatterns = [
        re_path(r'ws/test/$', PlaceholderConsumer.as_asgi()),
    ]
