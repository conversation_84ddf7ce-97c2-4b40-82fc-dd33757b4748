#!/usr/bin/env python
"""
元宇宙社交空间 Django 管理脚本

这是Django项目的命令行工具，用于执行各种管理任务，如：
- 启动开发服务器
- 执行数据库迁移
- 创建超级用户
- 运行测试等

使用方法：
    python manage.py <command> [options]

常用命令：
    python manage.py runserver          # 启动开发服务器
    python manage.py migrate            # 执行数据库迁移
    python manage.py createsuperuser    # 创建超级用户
    python manage.py collectstatic      # 收集静态文件
    python manage.py test               # 运行测试
"""
import os
import sys

if __name__ == '__main__':
    # 设置Django配置模块
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
    
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "无法导入Django。请确保Django已正确安装并且虚拟环境已激活。"
            "如果您使用的是虚拟环境，请确保已激活。"
        ) from exc
    
    execute_from_command_line(sys.argv)
