# 元宇宙社交空间 - 基础依赖包
# 这个文件包含了所有环境都需要的基础Python包

# Django核心框架
Django==4.2.7
djangorestframework==3.14.0

# 数据库相关
mysqlclient==2.2.0
django-redis==5.4.0

# 认证和安全
djangorestframework-simplejwt==5.3.0
djangorestframework-simplejwt[crypto]==5.3.0
django-cors-headers==4.3.1
python-decouple==3.8

# API文档
drf-spectacular==0.26.5
drf-nested-routers==0.93.4

# WebSocket和实时通信
channels==4.0.0
channels-redis==4.1.0
redis==4.6.0

# 异步支持
asgiref==3.7.2
uvloop==0.19.0

# 异步和WebSocket支持
channels==4.0.0
channels-redis==4.1.0

# 异步任务处理
celery==5.3.4
redis==5.0.1

# 过滤和搜索
django-filter==23.4

# 文件处理
Pillow==10.1.0

# 时间处理
python-dateutil==2.8.2

# JSON处理
orjson==3.9.10

# 验证和序列化
marshmallow==3.20.1

# 工具库
python-slugify==8.0.1
shortuuid==1.0.11

# 监控和日志
structlog==23.2.0

# 环境变量管理
python-dotenv==1.0.0
