# 元宇宙社交空间 - 开发环境依赖包
# 这个文件包含了开发环境特有的Python包

# 基础依赖
-r base.txt

# 开发工具
django-extensions==3.2.3
django-debug-toolbar==4.2.0

# 代码质量工具
black==23.11.0
isort==5.12.0
flake8==6.1.0
pylint==3.0.3
mypy==1.7.1

# 测试工具
pytest==7.4.3
pytest-django==4.7.0
pytest-cov==4.1.0
factory-boy==3.3.0
faker==20.1.0

# 文档生成
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# 性能分析
django-silk==5.0.4
memory-profiler==0.61.0

# 数据库工具
django-seed==0.3.1

# 开发服务器增强
werkzeug==3.0.1

# 环境管理
pip-tools==7.3.0

# Git钩子
pre-commit==3.6.0

# 调试工具
ipdb==0.13.13
django-pdb==0.6.2

# API测试
httpie==3.2.2
