# 元宇宙社交空间 - 生产环境依赖包
# 这个文件包含了生产环境特有的Python包

# 基础依赖
-r base.txt

# WSGI/ASGI服务器
gunicorn==21.2.0
daphne==4.0.0
uvicorn==0.24.0

# 静态文件服务
whitenoise==6.6.0

# 云存储支持
django-storages==1.14.2
boto3==1.34.0

# 监控和错误追踪
sentry-sdk==1.38.0
django-health-check==3.17.0

# 性能优化
django-cachalot==2.6.1
django-compressor==4.4

# 安全增强
django-security==0.20.0
django-ratelimit==4.1.0

# 数据库连接池
django-db-pool==1.0.1

# 邮件服务
django-anymail==10.2

# 缓存
django-cache-machine==1.2.0

# 日志处理
django-structlog==6.0.0

# 环境配置
django-environ==0.11.2

# 备份工具
django-dbbackup==4.0.2
