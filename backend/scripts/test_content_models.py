#!/usr/bin/env python
"""
元宇宙社交空间 - 内容管理模型测试脚本

这个脚本用于测试内容管理模型的定义是否正确
"""
import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(BASE_DIR))

# 设置Django配置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')

# 初始化Django
django.setup()

def test_content_models():
    """测试内容管理模型"""
    print("🚀 开始测试内容管理模型...")
    
    try:
        # 测试模型导入
        from apps.content.models import Post, Comment, Like, Share, PostMedia, Topic
        from apps.users.models import User
        
        print("✅ 内容管理模型导入成功")
        
        # 测试模型字段
        print("\n📋 测试模型字段定义...")
        
        # 测试Topic模型
        topic_fields = [f.name for f in Topic._meta.fields]
        print(f"✅ Topic模型字段: {topic_fields}")
        
        # 测试Post模型
        post_fields = [f.name for f in Post._meta.fields]
        print(f"✅ Post模型字段: {post_fields}")
        
        # 测试Comment模型
        comment_fields = [f.name for f in Comment._meta.fields]
        print(f"✅ Comment模型字段: {comment_fields}")
        
        # 测试Like模型
        like_fields = [f.name for f in Like._meta.fields]
        print(f"✅ Like模型字段: {like_fields}")
        
        # 测试Share模型
        share_fields = [f.name for f in Share._meta.fields]
        print(f"✅ Share模型字段: {share_fields}")
        
        # 测试PostMedia模型
        media_fields = [f.name for f in PostMedia._meta.fields]
        print(f"✅ PostMedia模型字段: {media_fields}")
        
        # 测试管理器方法
        print("\n🔧 测试管理器方法...")
        
        # 测试Topic管理器
        print("✅ Topic管理器方法:", [
            method for method in dir(Topic.objects) 
            if not method.startswith('_') and callable(getattr(Topic.objects, method))
        ])
        
        # 测试Post管理器
        print("✅ Post管理器方法:", [
            method for method in dir(Post.objects) 
            if not method.startswith('_') and callable(getattr(Post.objects, method))
        ])
        
        print("\n🎉 内容管理模型测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 内容管理模型测试失败: {e}")
        return False

def test_model_relationships():
    """测试模型关系"""
    print("\n🔗 测试模型关系...")
    
    try:
        from apps.content.models import Post, Comment, Like, Share, PostMedia, Topic
        from apps.users.models import User
        
        # 测试外键关系
        print("✅ 测试外键关系...")
        
        # Post关系
        post_relations = [
            f.name for f in Post._meta.fields 
            if hasattr(f, 'related_model') and f.related_model
        ]
        print(f"  Post外键关系: {post_relations}")
        
        # Comment关系
        comment_relations = [
            f.name for f in Comment._meta.fields 
            if hasattr(f, 'related_model') and f.related_model
        ]
        print(f"  Comment外键关系: {comment_relations}")
        
        # 测试多对多关系
        print("✅ 测试多对多关系...")
        
        # Post的多对多关系
        post_m2m = [f.name for f in Post._meta.many_to_many]
        print(f"  Post多对多关系: {post_m2m}")
        
        print("✅ 模型关系测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 模型关系测试失败: {e}")
        return False

def test_model_constraints():
    """测试模型约束"""
    print("\n🔒 测试模型约束...")
    
    try:
        from apps.content.models import Post, Comment, Like, Share, PostMedia, Topic
        
        # 测试唯一约束
        print("✅ 测试唯一约束...")
        
        # Like唯一约束
        like_unique = getattr(Like._meta, 'unique_together', [])
        print(f"  Like唯一约束: {like_unique}")
        
        # Share唯一约束
        share_unique = getattr(Share._meta, 'unique_together', [])
        print(f"  Share唯一约束: {share_unique}")
        
        # 测试索引
        print("✅ 测试数据库索引...")
        
        # Post索引
        post_indexes = [index.name for index in Post._meta.indexes]
        print(f"  Post索引: {post_indexes}")
        
        # Comment索引
        comment_indexes = [index.name for index in Comment._meta.indexes]
        print(f"  Comment索引: {comment_indexes}")
        
        print("✅ 模型约束测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 模型约束测试失败: {e}")
        return False

def test_model_methods():
    """测试模型方法"""
    print("\n⚙️ 测试模型方法...")
    
    try:
        from apps.content.models import Post, Comment, Like, Share, PostMedia, Topic
        
        # 测试Post模型方法
        print("✅ 测试Post模型方法...")
        post_methods = [
            'soft_delete', 'get_media_urls', 'add_media_url', 'increment_view_count'
        ]
        for method in post_methods:
            if hasattr(Post, method):
                print(f"  ✓ Post.{method} 方法存在")
            else:
                print(f"  ✗ Post.{method} 方法缺失")
        
        # 测试Comment模型方法
        print("✅ 测试Comment模型方法...")
        comment_methods = ['soft_delete', 'is_reply', 'get_level']
        for method in comment_methods:
            if hasattr(Comment, method):
                print(f"  ✓ Comment.{method} 方法存在")
            else:
                print(f"  ✗ Comment.{method} 方法缺失")
        
        # 测试PostMedia模型方法
        print("✅ 测试PostMedia模型方法...")
        media_methods = ['soft_delete', 'get_file_size_display', 'get_duration_display']
        for method in media_methods:
            if hasattr(PostMedia, method):
                print(f"  ✓ PostMedia.{method} 方法存在")
            else:
                print(f"  ✗ PostMedia.{method} 方法缺失")
        
        print("✅ 模型方法测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 模型方法测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("元宇宙社交空间 - 内容管理模型测试")
    print("=" * 60)
    
    # 运行测试
    model_ok = test_content_models()
    relation_ok = test_model_relationships()
    constraint_ok = test_model_constraints()
    method_ok = test_model_methods()
    
    print("\n" + "=" * 60)
    if model_ok and relation_ok and constraint_ok and method_ok:
        print("🎉 所有测试通过！内容管理模型定义正确。")
        print("\n下一步操作:")
        print("1. 运行 python manage.py makemigrations content")
        print("2. 运行 python manage.py migrate")
        print("3. 在Django Admin中测试模型管理")
        print("4. 测试API端点功能")
    else:
        print("❌ 测试失败，请检查模型定义。")
    print("=" * 60)

if __name__ == '__main__':
    main()
