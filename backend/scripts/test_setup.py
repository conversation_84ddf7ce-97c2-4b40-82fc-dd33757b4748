#!/usr/bin/env python
"""
元宇宙社交空间 - Django设置测试脚本

这个脚本用于测试Django项目的基础配置是否正确
"""
import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(BASE_DIR))

# 设置Django配置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')

# 初始化Django
django.setup()

def test_django_setup():
    """测试Django基础设置"""
    print("🚀 开始测试Django设置...")
    
    try:
        # 测试导入Django
        import django
        print(f"✅ Django版本: {django.get_version()}")
        
        # 测试设置导入
        from django.conf import settings
        print(f"✅ 设置模块: {settings.SETTINGS_MODULE}")
        print(f"✅ 调试模式: {settings.DEBUG}")
        print(f"✅ 数据库: {settings.DATABASES['default']['ENGINE']}")
        
        # 测试应用导入
        print("\n📦 测试应用导入...")
        
        # 测试核心模块
        from core.models import BaseModel
        print("✅ 核心模块导入成功")
        
        # 测试用户应用
        from apps.users.models import User, UserProfile, UserPreferences
        print("✅ 用户应用导入成功")
        
        # 测试其他应用
        from apps.social.apps import SocialConfig
        from apps.messaging.apps import MessagingConfig
        from apps.content.apps import ContentConfig
        print("✅ 其他应用导入成功")
        
        print("\n🎉 Django设置测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ Django设置测试失败: {e}")
        return False

def test_model_definitions():
    """测试模型定义"""
    print("\n🔍 测试模型定义...")
    
    try:
        from apps.users.models import User, UserProfile, UserPreferences
        
        # 测试User模型
        print(f"✅ User模型字段: {[f.name for f in User._meta.fields]}")
        
        # 测试UserProfile模型
        print(f"✅ UserProfile模型字段: {[f.name for f in UserProfile._meta.fields]}")
        
        # 测试UserPreferences模型
        print(f"✅ UserPreferences模型字段: {[f.name for f in UserPreferences._meta.fields]}")
        
        print("✅ 模型定义测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 模型定义测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("元宇宙社交空间 - Django设置测试")
    print("=" * 50)
    
    # 运行测试
    setup_ok = test_django_setup()
    model_ok = test_model_definitions()
    
    print("\n" + "=" * 50)
    if setup_ok and model_ok:
        print("🎉 所有测试通过！Django项目配置正确。")
        print("\n下一步操作:")
        print("1. 运行 python manage.py makemigrations")
        print("2. 运行 python manage.py migrate")
        print("3. 运行 python manage.py createsuperuser")
        print("4. 运行 python manage.py runserver")
    else:
        print("❌ 测试失败，请检查配置。")
    print("=" * 50)

if __name__ == '__main__':
    main()
