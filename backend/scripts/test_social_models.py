#!/usr/bin/env python
"""
元宇宙社交空间 - 社交功能模型测试脚本

这个脚本用于测试社交功能模型的定义是否正确
"""
import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(BASE_DIR))

# 设置Django配置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')

# 初始化Django
django.setup()

def test_social_models():
    """测试社交功能模型"""
    print("🚀 开始测试社交功能模型...")
    
    try:
        # 测试模型导入
        from apps.social.models import Friendship, Follow, UserBlock, UserVisit
        from apps.users.models import User
        
        print("✅ 社交功能模型导入成功")
        
        # 测试模型字段
        print("\n📋 测试模型字段定义...")
        
        # 测试Friendship模型
        friendship_fields = [f.name for f in Friendship._meta.fields]
        print(f"✅ Friendship模型字段: {friendship_fields}")
        
        # 测试Follow模型
        follow_fields = [f.name for f in Follow._meta.fields]
        print(f"✅ Follow模型字段: {follow_fields}")
        
        # 测试UserBlock模型
        block_fields = [f.name for f in UserBlock._meta.fields]
        print(f"✅ UserBlock模型字段: {block_fields}")
        
        # 测试UserVisit模型
        visit_fields = [f.name for f in UserVisit._meta.fields]
        print(f"✅ UserVisit模型字段: {visit_fields}")
        
        # 测试管理器方法
        print("\n🔧 测试管理器方法...")
        
        # 测试Friendship管理器
        print("✅ Friendship管理器方法:", [
            method for method in dir(Friendship.objects) 
            if not method.startswith('_') and callable(getattr(Friendship.objects, method))
        ])
        
        # 测试Follow管理器
        print("✅ Follow管理器方法:", [
            method for method in dir(Follow.objects) 
            if not method.startswith('_') and callable(getattr(Follow.objects, method))
        ])
        
        print("\n🎉 社交功能模型测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 社交功能模型测试失败: {e}")
        return False

def test_model_relationships():
    """测试模型关系"""
    print("\n🔗 测试模型关系...")
    
    try:
        from apps.social.models import Friendship, Follow, UserBlock, UserVisit
        from apps.users.models import User
        
        # 测试外键关系
        print("✅ 测试外键关系...")
        
        # Friendship关系
        friendship_relations = [
            f.name for f in Friendship._meta.fields 
            if hasattr(f, 'related_model') and f.related_model
        ]
        print(f"  Friendship外键关系: {friendship_relations}")
        
        # Follow关系
        follow_relations = [
            f.name for f in Follow._meta.fields 
            if hasattr(f, 'related_model') and f.related_model
        ]
        print(f"  Follow外键关系: {follow_relations}")
        
        print("✅ 模型关系测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 模型关系测试失败: {e}")
        return False

def test_model_constraints():
    """测试模型约束"""
    print("\n🔒 测试模型约束...")
    
    try:
        from apps.social.models import Friendship, Follow, UserBlock, UserVisit
        
        # 测试唯一约束
        print("✅ 测试唯一约束...")
        
        # Friendship唯一约束
        friendship_unique = getattr(Friendship._meta, 'unique_together', [])
        print(f"  Friendship唯一约束: {friendship_unique}")
        
        # Follow唯一约束
        follow_unique = getattr(Follow._meta, 'unique_together', [])
        print(f"  Follow唯一约束: {follow_unique}")
        
        # UserBlock唯一约束
        block_unique = getattr(UserBlock._meta, 'unique_together', [])
        print(f"  UserBlock唯一约束: {block_unique}")
        
        # UserVisit唯一约束
        visit_unique = getattr(UserVisit._meta, 'unique_together', [])
        print(f"  UserVisit唯一约束: {visit_unique}")
        
        print("✅ 模型约束测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 模型约束测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("元宇宙社交空间 - 社交功能模型测试")
    print("=" * 60)
    
    # 运行测试
    model_ok = test_social_models()
    relation_ok = test_model_relationships()
    constraint_ok = test_model_constraints()
    
    print("\n" + "=" * 60)
    if model_ok and relation_ok and constraint_ok:
        print("🎉 所有测试通过！社交功能模型定义正确。")
        print("\n下一步操作:")
        print("1. 运行 python manage.py makemigrations social")
        print("2. 运行 python manage.py migrate")
        print("3. 在Django Admin中测试模型管理")
    else:
        print("❌ 测试失败，请检查模型定义。")
    print("=" * 60)

if __name__ == '__main__':
    main()
