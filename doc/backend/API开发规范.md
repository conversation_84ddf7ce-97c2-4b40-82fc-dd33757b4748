# 后端API开发规范

## 📋 开发规范概述

本文档定义了Django后端API开发的标准规范，包括RESTful设计原则、代码结构、错误处理、安全机制等内容。

## 🏗️ RESTful API设计原则

### URL设计规范
```python
# 资源命名使用复数形式
/api/v1/users/              # 用户资源
/api/v1/posts/              # 动态资源
/api/v1/conversations/      # 会话资源

# 嵌套资源设计
/api/v1/posts/{id}/comments/        # 动态的评论
/api/v1/users/{id}/friends/         # 用户的好友
/api/v1/conversations/{id}/messages/ # 会话的消息

# 避免深层嵌套（最多2层）
# 错误示例：/api/v1/users/{id}/posts/{id}/comments/{id}/replies/
# 正确示例：/api/v1/comments/{id}/replies/
```

### HTTP方法使用规范
```python
# GET - 获取资源
GET /api/v1/users/          # 获取用户列表
GET /api/v1/users/{id}/     # 获取特定用户

# POST - 创建资源
POST /api/v1/users/         # 创建新用户
POST /api/v1/posts/         # 发布新动态

# PUT - 完整更新资源
PUT /api/v1/users/{id}/     # 完整更新用户信息

# PATCH - 部分更新资源
PATCH /api/v1/users/{id}/   # 部分更新用户信息

# DELETE - 删除资源
DELETE /api/v1/users/{id}/  # 删除用户
```

### 状态码使用规范
```python
# 成功响应
200 OK          # 请求成功
201 Created     # 资源创建成功
204 No Content  # 删除成功，无返回内容

# 客户端错误
400 Bad Request         # 请求参数错误
401 Unauthorized        # 未认证
403 Forbidden          # 无权限
404 Not Found          # 资源不存在
409 Conflict           # 资源冲突
422 Unprocessable Entity # 请求格式正确但语义错误

# 服务器错误
500 Internal Server Error # 服务器内部错误
502 Bad Gateway          # 网关错误
503 Service Unavailable  # 服务不可用
```

## 📁 代码结构规范

### Django应用结构
```python
apps/
├── users/                  # 用户管理应用
│   ├── __init__.py
│   ├── models.py          # 数据模型
│   ├── serializers.py     # 序列化器
│   ├── views.py           # 视图
│   ├── urls.py            # URL路由
│   ├── permissions.py     # 权限控制
│   ├── filters.py         # 过滤器
│   ├── validators.py      # 验证器
│   └── tests/             # 测试文件
│       ├── test_models.py
│       ├── test_views.py
│       └── test_serializers.py
├── social/                # 社交功能应用
├── messaging/             # 消息系统应用
└── content/               # 内容管理应用
```

### 视图类设计规范
```python
# views.py
from rest_framework import generics, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend

class UserViewSet(viewsets.ModelViewSet):
    """
    用户管理视图集
    
    提供用户的CRUD操作和相关业务功能
    """
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['is_active', 'created_at']
    search_fields = ['username', 'email', 'first_name', 'last_name']
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return UserCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return UserUpdateSerializer
        return UserSerializer
    
    def get_permissions(self):
        """根据动作设置权限"""
        if self.action == 'create':
            permission_classes = [permissions.AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    @action(detail=True, methods=['get'])
    def friends(self, request, pk=None):
        """获取用户好友列表"""
        user = self.get_object()
        friends = user.get_friends()
        serializer = UserSerializer(friends, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def me(self, request):
        """获取当前用户信息"""
        serializer = self.get_serializer(request.user)
        return Response(serializer.data)
```

### 序列化器设计规范
```python
# serializers.py
from rest_framework import serializers
from django.contrib.auth.password_validation import validate_password

class UserSerializer(serializers.ModelSerializer):
    """用户序列化器"""
    display_name = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'display_name', 'avatar_url', 'bio', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_display_name(self, obj):
        """获取显示名称"""
        return f"{obj.first_name} {obj.last_name}".strip() or obj.username

class UserCreateSerializer(serializers.ModelSerializer):
    """用户创建序列化器"""
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'username', 'email', 'password', 'password_confirm',
            'first_name', 'last_name'
        ]
    
    def validate(self, attrs):
        """验证密码确认"""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("密码确认不匹配")
        return attrs
    
    def create(self, validated_data):
        """创建用户"""
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        return user

class UserUpdateSerializer(serializers.ModelSerializer):
    """用户更新序列化器"""
    
    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'bio', 'avatar_url']
```

### 模型设计规范
```python
# models.py
from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import MinLengthValidator

class BaseModel(models.Model):
    """基础模型类"""
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        abstract = True

class User(AbstractUser):
    """用户模型"""
    email = models.EmailField(unique=True, verbose_name='邮箱')
    bio = models.TextField(max_length=500, blank=True, verbose_name='个人简介')
    avatar_url = models.URLField(blank=True, verbose_name='头像URL')
    birth_date = models.DateField(null=True, blank=True, verbose_name='生日')
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']
    
    class Meta:
        db_table = 'users'
        verbose_name = '用户'
        verbose_name_plural = '用户'
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['username']),
            models.Index(fields=['created_at']),
        ]
    
    def get_friends(self):
        """获取好友列表"""
        from .social.models import Friendship
        friendships = Friendship.objects.filter(
            models.Q(requester=self) | models.Q(addressee=self),
            status='accepted'
        )
        friend_ids = []
        for friendship in friendships:
            if friendship.requester == self:
                friend_ids.append(friendship.addressee_id)
            else:
                friend_ids.append(friendship.requester_id)
        return User.objects.filter(id__in=friend_ids)
    
    def __str__(self):
        return self.username
```

## 🔐 认证与权限

### JWT认证配置
```python
# settings.py
from datetime import timedelta

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=1),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'UPDATE_LAST_LOGIN': True,
    
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    
    'AUTH_HEADER_TYPES': ('Bearer',),
    'AUTH_HEADER_NAME': 'HTTP_AUTHORIZATION',
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
}
```

### 自定义权限类
```python
# permissions.py
from rest_framework import permissions

class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    只有对象的所有者才能编辑，其他人只能读取
    """
    
    def has_object_permission(self, request, view, obj):
        # 读取权限对所有请求开放
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 写入权限只给对象的所有者
        return obj.author == request.user

class IsFriendOrPublic(permissions.BasePermission):
    """
    只有好友或公开内容才能访问
    """
    
    def has_object_permission(self, request, view, obj):
        if obj.visibility == 'public':
            return True
        elif obj.visibility == 'friends':
            return request.user in obj.author.get_friends()
        elif obj.visibility == 'private':
            return obj.author == request.user
        return False
```

## 🚨 错误处理规范

### 全局异常处理
```python
# exceptions.py
from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
import logging

logger = logging.getLogger(__name__)

def custom_exception_handler(exc, context):
    """自定义异常处理器"""
    response = exception_handler(exc, context)
    
    if response is not None:
        custom_response_data = {
            'code': response.status_code,
            'message': get_error_message(exc, response),
            'data': None,
            'timestamp': timezone.now().isoformat(),
            'request_id': context['request'].META.get('HTTP_X_REQUEST_ID', '')
        }
        
        # 记录错误日志
        if response.status_code >= 500:
            logger.error(f"服务器错误: {exc}", exc_info=True)
        
        response.data = custom_response_data
    
    return response

def get_error_message(exc, response):
    """获取错误消息"""
    if hasattr(exc, 'detail'):
        if isinstance(exc.detail, dict):
            # 字段验证错误
            errors = []
            for field, messages in exc.detail.items():
                if isinstance(messages, list):
                    errors.extend([f"{field}: {msg}" for msg in messages])
                else:
                    errors.append(f"{field}: {messages}")
            return "; ".join(errors)
        else:
            return str(exc.detail)
    return "未知错误"
```

### 业务异常定义
```python
# exceptions.py
from rest_framework import status
from rest_framework.exceptions import APIException

class BusinessException(APIException):
    """业务异常基类"""
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = '业务处理失败'
    default_code = 'business_error'

class UserNotFoundError(BusinessException):
    status_code = status.HTTP_404_NOT_FOUND
    default_detail = '用户不存在'
    default_code = 'user_not_found'

class FriendRequestAlreadySentError(BusinessException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = '好友请求已发送'
    default_code = 'friend_request_already_sent'

class CannotFriendYourselfError(BusinessException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = '不能向自己发送好友请求'
    default_code = 'cannot_friend_yourself'
```

## 📊 API文档生成

### drf-spectacular配置
```python
# settings.py
SPECTACULAR_SETTINGS = {
    'TITLE': '元宇宙社交空间 API',
    'DESCRIPTION': '元宇宙社交空间后端API文档',
    'VERSION': '1.0.0',
    'SERVE_INCLUDE_SCHEMA': False,
    'COMPONENT_SPLIT_REQUEST': True,
    'SCHEMA_PATH_PREFIX': '/api/v1/',
}

# urls.py
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView

urlpatterns = [
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
]
```

### API文档注释规范
```python
from drf_spectacular.utils import extend_schema, OpenApiParameter

class UserViewSet(viewsets.ModelViewSet):
    
    @extend_schema(
        summary="获取用户列表",
        description="获取系统中所有用户的列表，支持分页和搜索",
        parameters=[
            OpenApiParameter(
                name='search',
                description='搜索关键词',
                required=False,
                type=str
            ),
            OpenApiParameter(
                name='is_active',
                description='是否激活',
                required=False,
                type=bool
            ),
        ],
        responses={200: UserSerializer(many=True)},
        tags=['用户管理']
    )
    def list(self, request):
        return super().list(request)
    
    @extend_schema(
        summary="创建新用户",
        description="注册新用户账户",
        request=UserCreateSerializer,
        responses={
            201: UserSerializer,
            400: "请求参数错误"
        },
        tags=['用户管理']
    )
    def create(self, request):
        return super().create(request)
```

## 🧪 测试规范

### 单元测试
```python
# tests/test_views.py
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status

User = get_user_model()

class UserViewSetTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_create_user(self):
        """测试用户创建"""
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'newpass123',
            'password_confirm': 'newpass123',
            'first_name': '新',
            'last_name': '用户'
        }
        response = self.client.post('/api/v1/users/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(User.objects.filter(email='<EMAIL>').exists())
    
    def test_get_user_profile(self):
        """测试获取用户资料"""
        self.client.force_authenticate(user=self.user)
        response = self.client.get('/api/v1/users/me/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['email'], self.user.email)
```

## 📈 性能优化

### 数据库查询优化
```python
# 使用select_related和prefetch_related优化查询
class PostViewSet(viewsets.ModelViewSet):
    def get_queryset(self):
        return Post.objects.select_related('author').prefetch_related(
            'comments__author',
            'likes__user'
        )

# 使用缓存减少数据库查询
from django.core.cache import cache

def get_user_stats(user_id):
    cache_key = f"user_stats_{user_id}"
    stats = cache.get(cache_key)
    if stats is None:
        stats = calculate_user_stats(user_id)
        cache.set(cache_key, stats, timeout=300)  # 缓存5分钟
    return stats
```

### 分页优化
```python
# 使用游标分页处理大数据集
from rest_framework.pagination import CursorPagination

class PostCursorPagination(CursorPagination):
    page_size = 20
    ordering = '-created_at'
    cursor_query_param = 'cursor'
    page_size_query_param = 'page_size'
    max_page_size = 100
```

---

**文档版本**: v1.0  
**最后更新**: 2025-08-05  
**维护人员**: 后端开发团队
