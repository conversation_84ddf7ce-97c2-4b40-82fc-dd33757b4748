# 后端DDD领域驱动设计文档

## 📋 项目概述

**项目名称**: 元宇宙社交空间 - 后端服务  
**架构模式**: DDD领域驱动设计 + 微服务架构  
**技术栈**: Django 4.2 + Python 3.11  
**数据库**: MySQL 8.0 + Redis 7.0  

## 🏗️ DDD架构设计

### 核心概念
- **领域 (Domain)**: 业务问题空间，包含业务规则和逻辑
- **子域 (Subdomain)**: 领域的细分，每个子域解决特定的业务问题
- **限界上下文 (Bounded Context)**: 明确的边界，定义模型的适用范围
- **聚合 (Aggregate)**: 数据一致性和业务规则的边界
- **实体 (Entity)**: 具有唯一标识的业务对象
- **值对象 (Value Object)**: 不可变的、描述性的对象

### 分层架构
```
┌─────────────────────────────────────┐
│           用户接口层 (UI Layer)        │
│     Controllers, Serializers       │
├─────────────────────────────────────┤
│          应用服务层 (App Layer)       │
│      Application Services          │
├─────────────────────────────────────┤
│           领域层 (Domain Layer)      │
│   Entities, Value Objects,         │
│   Domain Services, Repositories    │
├─────────────────────────────────────┤
│         基础设施层 (Infra Layer)      │
│   Database, External APIs,         │
│   Message Queues                   │
└─────────────────────────────────────┘
```

## 🎯 领域划分

### 1. 用户管理域 (User Management)
**职责**: 用户注册、认证、资料管理
**核心实体**:
- User (用户)
- UserProfile (用户资料)
- UserPreferences (用户偏好)

**聚合根**: User
**领域服务**:
- UserRegistrationService (用户注册服务)
- UserAuthenticationService (用户认证服务)
- UserProfileService (用户资料服务)

```python
# domain/user/entities.py
from dataclasses import dataclass
from datetime import datetime
from typing import Optional

@dataclass
class User:
    """用户实体"""
    id: int
    username: str
    email: str
    password_hash: str
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    def change_password(self, new_password_hash: str) -> None:
        """修改密码"""
        self.password_hash = new_password_hash
        self.updated_at = datetime.now()
    
    def deactivate(self) -> None:
        """停用账户"""
        self.is_active = False
        self.updated_at = datetime.now()

@dataclass
class UserProfile:
    """用户资料值对象"""
    first_name: str
    last_name: str
    bio: Optional[str] = None
    avatar_url: Optional[str] = None
    birth_date: Optional[datetime] = None
    
    @property
    def display_name(self) -> str:
        """显示名称"""
        return f"{self.first_name} {self.last_name}"
```

### 2. 社交互动域 (Social Interaction)
**职责**: 好友关系、关注、消息、动态
**核心实体**:
- Friendship (好友关系)
- Follow (关注关系)
- Post (动态)
- Comment (评论)
- Like (点赞)

**聚合根**: Friendship, Post
**领域服务**:
- FriendshipService (好友关系服务)
- PostService (动态服务)
- InteractionService (互动服务)

```python
# domain/social/entities.py
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import List

class FriendshipStatus(Enum):
    """好友关系状态"""
    PENDING = "pending"
    ACCEPTED = "accepted"
    BLOCKED = "blocked"

@dataclass
class Friendship:
    """好友关系聚合根"""
    id: int
    requester_id: int
    addressee_id: int
    status: FriendshipStatus
    created_at: datetime
    updated_at: datetime
    
    def accept(self) -> None:
        """接受好友请求"""
        if self.status != FriendshipStatus.PENDING:
            raise ValueError("只能接受待处理的好友请求")
        self.status = FriendshipStatus.ACCEPTED
        self.updated_at = datetime.now()
    
    def block(self) -> None:
        """拉黑用户"""
        self.status = FriendshipStatus.BLOCKED
        self.updated_at = datetime.now()

@dataclass
class Post:
    """动态聚合根"""
    id: int
    author_id: int
    content: str
    images: List[str]
    created_at: datetime
    updated_at: datetime
    is_deleted: bool = False
    
    def update_content(self, new_content: str) -> None:
        """更新动态内容"""
        if self.is_deleted:
            raise ValueError("已删除的动态无法编辑")
        self.content = new_content
        self.updated_at = datetime.now()
    
    def delete(self) -> None:
        """删除动态"""
        self.is_deleted = True
        self.updated_at = datetime.now()
```

### 3. 实时通信域 (Real-time Communication)
**职责**: 即时消息、群聊、通知
**核心实体**:
- Conversation (会话)
- Message (消息)
- Notification (通知)

**聚合根**: Conversation
**领域服务**:
- MessageService (消息服务)
- NotificationService (通知服务)
- ConversationService (会话服务)

```python
# domain/communication/entities.py
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import List, Optional

class MessageType(Enum):
    """消息类型"""
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    SYSTEM = "system"

@dataclass
class Message:
    """消息实体"""
    id: int
    conversation_id: int
    sender_id: int
    content: str
    message_type: MessageType
    created_at: datetime
    is_read: bool = False
    reply_to_id: Optional[int] = None
    
    def mark_as_read(self) -> None:
        """标记为已读"""
        self.is_read = True

@dataclass
class Conversation:
    """会话聚合根"""
    id: int
    participants: List[int]
    is_group: bool
    name: Optional[str]
    created_at: datetime
    updated_at: datetime
    last_message_at: Optional[datetime] = None
    
    def add_participant(self, user_id: int) -> None:
        """添加参与者"""
        if user_id not in self.participants:
            self.participants.append(user_id)
            self.updated_at = datetime.now()
    
    def remove_participant(self, user_id: int) -> None:
        """移除参与者"""
        if user_id in self.participants:
            self.participants.remove(user_id)
            self.updated_at = datetime.now()
```

### 4. 内容管理域 (Content Management)
**职责**: 内容发布、审核、推荐
**核心实体**:
- Content (内容)
- ContentModeration (内容审核)
- ContentRecommendation (内容推荐)

**聚合根**: Content
**领域服务**:
- ContentModerationService (内容审核服务)
- ContentRecommendationService (内容推荐服务)

## 🔧 技术实现

### 1. 仓储模式 (Repository Pattern)
```python
# domain/user/repositories.py
from abc import ABC, abstractmethod
from typing import Optional, List
from .entities import User

class UserRepository(ABC):
    """用户仓储接口"""
    
    @abstractmethod
    def get_by_id(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        pass
    
    @abstractmethod
    def get_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        pass
    
    @abstractmethod
    def save(self, user: User) -> User:
        """保存用户"""
        pass
    
    @abstractmethod
    def delete(self, user_id: int) -> None:
        """删除用户"""
        pass

# infrastructure/repositories/user_repository.py
from django.contrib.auth.models import User as DjangoUser
from domain.user.repositories import UserRepository
from domain.user.entities import User

class DjangoUserRepository(UserRepository):
    """Django用户仓储实现"""
    
    def get_by_id(self, user_id: int) -> Optional[User]:
        try:
            django_user = DjangoUser.objects.get(id=user_id)
            return self._to_domain_entity(django_user)
        except DjangoUser.DoesNotExist:
            return None
    
    def _to_domain_entity(self, django_user: DjangoUser) -> User:
        """转换为领域实体"""
        return User(
            id=django_user.id,
            username=django_user.username,
            email=django_user.email,
            password_hash=django_user.password,
            is_active=django_user.is_active,
            created_at=django_user.date_joined,
            updated_at=django_user.last_login or django_user.date_joined
        )
```

### 2. 应用服务层 (Application Services)
```python
# application/user/services.py
from dataclasses import dataclass
from typing import Optional
from domain.user.entities import User, UserProfile
from domain.user.repositories import UserRepository
from domain.user.services import UserRegistrationService

@dataclass
class RegisterUserCommand:
    """注册用户命令"""
    username: str
    email: str
    password: str
    first_name: str
    last_name: str

class UserApplicationService:
    """用户应用服务"""
    
    def __init__(
        self, 
        user_repository: UserRepository,
        registration_service: UserRegistrationService
    ):
        self._user_repository = user_repository
        self._registration_service = registration_service
    
    def register_user(self, command: RegisterUserCommand) -> User:
        """注册用户"""
        # 检查邮箱是否已存在
        existing_user = self._user_repository.get_by_email(command.email)
        if existing_user:
            raise ValueError("邮箱已被注册")
        
        # 使用领域服务创建用户
        user = self._registration_service.register(
            username=command.username,
            email=command.email,
            password=command.password
        )
        
        # 保存用户
        return self._user_repository.save(user)
    
    def get_user_profile(self, user_id: int) -> Optional[UserProfile]:
        """获取用户资料"""
        user = self._user_repository.get_by_id(user_id)
        if not user:
            return None
        
        return user.profile
```

### 3. 领域服务 (Domain Services)
```python
# domain/user/services.py
from datetime import datetime
from .entities import User
from .value_objects import Email, Password

class UserRegistrationService:
    """用户注册领域服务"""
    
    def register(self, username: str, email: str, password: str) -> User:
        """注册新用户"""
        # 验证邮箱格式
        email_vo = Email(email)
        if not email_vo.is_valid():
            raise ValueError("邮箱格式不正确")
        
        # 验证密码强度
        password_vo = Password(password)
        if not password_vo.is_strong():
            raise ValueError("密码强度不足")
        
        # 创建用户实体
        now = datetime.now()
        return User(
            id=0,  # 由数据库生成
            username=username,
            email=email,
            password_hash=password_vo.hash(),
            is_active=True,
            created_at=now,
            updated_at=now
        )

# domain/social/services.py
class FriendshipService:
    """好友关系领域服务"""
    
    def send_friend_request(self, requester_id: int, addressee_id: int) -> Friendship:
        """发送好友请求"""
        if requester_id == addressee_id:
            raise ValueError("不能向自己发送好友请求")
        
        now = datetime.now()
        return Friendship(
            id=0,
            requester_id=requester_id,
            addressee_id=addressee_id,
            status=FriendshipStatus.PENDING,
            created_at=now,
            updated_at=now
        )
```

## 📊 数据库设计

### 1. 聚合边界与表设计
每个聚合对应一组相关的数据库表：

**用户聚合**:
- users (用户基本信息)
- user_profiles (用户资料)
- user_preferences (用户偏好)

**社交聚合**:
- friendships (好友关系)
- follows (关注关系)
- posts (动态)
- comments (评论)
- likes (点赞)

**通信聚合**:
- conversations (会话)
- messages (消息)
- conversation_participants (会话参与者)

### 2. 事务边界
- **聚合内事务**: 保证聚合内数据一致性
- **跨聚合事务**: 使用最终一致性，通过领域事件实现
- **分布式事务**: 使用Saga模式处理跨服务事务

## 🚀 部署架构

### 1. 微服务拆分
```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   用户服务       │  │   社交服务       │  │   通信服务       │
│ User Service    │  │ Social Service  │  │ Chat Service    │
└─────────────────┘  └─────────────────┘  └─────────────────┘
         │                     │                     │
         └─────────────────────┼─────────────────────┘
                               │
                    ┌─────────────────┐
                    │   API网关       │
                    │ API Gateway     │
                    └─────────────────┘
```

### 2. 数据一致性策略
- **强一致性**: 聚合内使用数据库事务
- **最终一致性**: 跨聚合使用领域事件
- **补偿机制**: 失败场景的数据回滚策略

---

**文档版本**: v1.0  
**最后更新**: 2025-08-05  
**维护人员**: 后端开发团队
