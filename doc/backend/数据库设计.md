# 数据库设计文档

## 📋 数据库概述

**数据库类型**: MySQL 8.0 (主数据库) + Redis 7.0 (缓存)  
**设计原则**: DDD聚合边界 + 数据一致性 + 性能优化  
**字符集**: utf8mb4 (支持emoji和特殊字符)  
**时区**: UTC (统一时区管理)  

## 🏗️ 数据库架构

### 主从架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   主数据库       │───►│   从数据库1      │    │   从数据库2      │
│ MySQL Master   │    │ MySQL Slave1    │    │ MySQL Slave2    │
│ (读写)          │    │ (只读)          │    │ (只读)          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐
│   Redis缓存     │
│ (会话/缓存)      │
└─────────────────┘
```

### 分库分表策略
- **垂直分库**: 按业务领域分离数据库
- **水平分表**: 大表按用户ID或时间分片
- **读写分离**: 主库写入，从库查询

## 📊 核心表设计

### 1. 用户管理域

#### users (用户表)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    phone VARCHAR(20) COMMENT '手机号',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    is_verified BOOLEAN DEFAULT FALSE COMMENT '是否已验证',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';
```

#### user_profiles (用户资料表)
```sql
CREATE TABLE user_profiles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '资料ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    first_name VARCHAR(50) COMMENT '名',
    last_name VARCHAR(50) COMMENT '姓',
    display_name VARCHAR(100) COMMENT '显示名称',
    bio TEXT COMMENT '个人简介',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    cover_url VARCHAR(500) COMMENT '封面图URL',
    birth_date DATE COMMENT '生日',
    gender ENUM('male', 'female', 'other', 'prefer_not_to_say') COMMENT '性别',
    location VARCHAR(100) COMMENT '所在地',
    website VARCHAR(200) COMMENT '个人网站',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_user_id (user_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户资料表';
```

#### user_preferences (用户偏好表)
```sql
CREATE TABLE user_preferences (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '偏好ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    language VARCHAR(10) DEFAULT 'zh-CN' COMMENT '语言偏好',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai' COMMENT '时区',
    theme ENUM('light', 'dark', 'auto') DEFAULT 'auto' COMMENT '主题偏好',
    notification_email BOOLEAN DEFAULT TRUE COMMENT '邮件通知',
    notification_push BOOLEAN DEFAULT TRUE COMMENT '推送通知',
    notification_sms BOOLEAN DEFAULT FALSE COMMENT '短信通知',
    privacy_profile ENUM('public', 'friends', 'private') DEFAULT 'public' COMMENT '资料隐私',
    privacy_posts ENUM('public', 'friends', 'private') DEFAULT 'public' COMMENT '动态隐私',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_user_id (user_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户偏好设置表';
```

### 2. 社交互动域

#### friendships (好友关系表)
```sql
CREATE TABLE friendships (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关系ID',
    requester_id BIGINT NOT NULL COMMENT '请求者ID',
    addressee_id BIGINT NOT NULL COMMENT '接收者ID',
    status ENUM('pending', 'accepted', 'declined', 'blocked') DEFAULT 'pending' COMMENT '关系状态',
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '请求时间',
    responded_at TIMESTAMP NULL COMMENT '响应时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_friendship (requester_id, addressee_id),
    INDEX idx_requester (requester_id),
    INDEX idx_addressee (addressee_id),
    INDEX idx_status (status),
    FOREIGN KEY (requester_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (addressee_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='好友关系表';
```

#### follows (关注关系表)
```sql
CREATE TABLE follows (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关注ID',
    follower_id BIGINT NOT NULL COMMENT '关注者ID',
    following_id BIGINT NOT NULL COMMENT '被关注者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间',
    
    UNIQUE KEY uk_follow (follower_id, following_id),
    INDEX idx_follower (follower_id),
    INDEX idx_following (following_id),
    FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (following_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关注关系表';
```

#### posts (动态表)
```sql
CREATE TABLE posts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '动态ID',
    author_id BIGINT NOT NULL COMMENT '作者ID',
    content TEXT NOT NULL COMMENT '动态内容',
    content_type ENUM('text', 'image', 'video', 'link') DEFAULT 'text' COMMENT '内容类型',
    media_urls JSON COMMENT '媒体文件URLs',
    location VARCHAR(100) COMMENT '位置信息',
    visibility ENUM('public', 'friends', 'private') DEFAULT 'public' COMMENT '可见性',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    comment_count INT DEFAULT 0 COMMENT '评论数',
    share_count INT DEFAULT 0 COMMENT '分享数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_author (author_id),
    INDEX idx_created_at (created_at),
    INDEX idx_visibility (visibility),
    INDEX idx_is_deleted (is_deleted),
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户动态表';
```

#### comments (评论表)
```sql
CREATE TABLE comments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '评论ID',
    post_id BIGINT NOT NULL COMMENT '动态ID',
    author_id BIGINT NOT NULL COMMENT '评论者ID',
    parent_id BIGINT NULL COMMENT '父评论ID',
    content TEXT NOT NULL COMMENT '评论内容',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    reply_count INT DEFAULT 0 COMMENT '回复数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_post (post_id),
    INDEX idx_author (author_id),
    INDEX idx_parent (parent_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';
```

#### likes (点赞表)
```sql
CREATE TABLE likes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '点赞ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    target_type ENUM('post', 'comment') NOT NULL COMMENT '目标类型',
    target_id BIGINT NOT NULL COMMENT '目标ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '点赞时间',
    
    UNIQUE KEY uk_like (user_id, target_type, target_id),
    INDEX idx_target (target_type, target_id),
    INDEX idx_user (user_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='点赞表';
```

### 3. 实时通信域

#### conversations (会话表)
```sql
CREATE TABLE conversations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '会话ID',
    type ENUM('private', 'group') NOT NULL COMMENT '会话类型',
    name VARCHAR(100) COMMENT '会话名称',
    description TEXT COMMENT '会话描述',
    avatar_url VARCHAR(500) COMMENT '会话头像',
    creator_id BIGINT NOT NULL COMMENT '创建者ID',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
    last_message_id BIGINT NULL COMMENT '最后一条消息ID',
    last_message_at TIMESTAMP NULL COMMENT '最后消息时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_type (type),
    INDEX idx_creator (creator_id),
    INDEX idx_last_message_at (last_message_at),
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会话表';
```

#### conversation_participants (会话参与者表)
```sql
CREATE TABLE conversation_participants (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '参与者ID',
    conversation_id BIGINT NOT NULL COMMENT '会话ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role ENUM('member', 'admin', 'owner') DEFAULT 'member' COMMENT '角色',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    left_at TIMESTAMP NULL COMMENT '离开时间',
    last_read_message_id BIGINT NULL COMMENT '最后已读消息ID',
    is_muted BOOLEAN DEFAULT FALSE COMMENT '是否静音',
    
    UNIQUE KEY uk_participant (conversation_id, user_id),
    INDEX idx_conversation (conversation_id),
    INDEX idx_user (user_id),
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会话参与者表';
```

#### messages (消息表)
```sql
CREATE TABLE messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '消息ID',
    conversation_id BIGINT NOT NULL COMMENT '会话ID',
    sender_id BIGINT NOT NULL COMMENT '发送者ID',
    message_type ENUM('text', 'image', 'file', 'voice', 'video', 'system') DEFAULT 'text' COMMENT '消息类型',
    content TEXT COMMENT '消息内容',
    media_url VARCHAR(500) COMMENT '媒体文件URL',
    file_name VARCHAR(255) COMMENT '文件名',
    file_size BIGINT COMMENT '文件大小',
    reply_to_id BIGINT NULL COMMENT '回复消息ID',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_conversation (conversation_id),
    INDEX idx_sender (sender_id),
    INDEX idx_created_at (created_at),
    INDEX idx_reply_to (reply_to_id),
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reply_to_id) REFERENCES messages(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息表';
```

### 4. 系统管理域

#### notifications (通知表)
```sql
CREATE TABLE notifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '通知ID',
    user_id BIGINT NOT NULL COMMENT '接收用户ID',
    type ENUM('like', 'comment', 'follow', 'friend_request', 'message', 'system') NOT NULL COMMENT '通知类型',
    title VARCHAR(200) NOT NULL COMMENT '通知标题',
    content TEXT COMMENT '通知内容',
    data JSON COMMENT '附加数据',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    read_at TIMESTAMP NULL COMMENT '阅读时间',
    
    INDEX idx_user (user_id),
    INDEX idx_type (type),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知表';
```

## 🔧 Redis缓存设计

### 缓存策略
```redis
# 用户会话缓存
session:user:{user_id} -> {session_data}
TTL: 7天

# 用户在线状态
online:user:{user_id} -> {last_active_time}
TTL: 5分钟

# 热门内容缓存
hot:posts -> [post_ids]
TTL: 1小时

# 用户关系缓存
friends:{user_id} -> [friend_ids]
TTL: 30分钟

# 消息队列
queue:notifications -> [notification_data]
queue:emails -> [email_data]

# 分布式锁
lock:user:{user_id} -> {lock_info}
TTL: 30秒

# 计数器缓存
count:post:{post_id}:likes -> {like_count}
count:user:{user_id}:followers -> {follower_count}
```

## 📈 性能优化

### 索引策略
- **主键索引**: 所有表都有自增主键
- **唯一索引**: 用户名、邮箱等唯一字段
- **复合索引**: 多字段查询的组合索引
- **覆盖索引**: 包含查询所需所有字段的索引

### 分区策略
```sql
-- 按时间分区的消息表
CREATE TABLE messages (
    -- 字段定义
) PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 查询优化
- **读写分离**: 查询走从库，写入走主库
- **连接池**: 使用数据库连接池
- **慢查询监控**: 监控和优化慢查询
- **查询缓存**: 缓存频繁查询的结果

## 🔒 数据安全

### 数据加密
- **密码加密**: 使用bcrypt加密用户密码
- **敏感数据**: 手机号、身份证等字段加密存储
- **传输加密**: 使用SSL/TLS加密数据传输

### 备份策略
- **全量备份**: 每日凌晨全量备份
- **增量备份**: 每小时增量备份
- **异地备份**: 备份文件存储到异地
- **恢复测试**: 定期测试备份恢复

### 权限控制
- **最小权限**: 应用只有必要的数据库权限
- **用户隔离**: 不同环境使用不同数据库用户
- **审计日志**: 记录所有数据库操作日志

---

**文档版本**: v1.0  
**最后更新**: 2025-08-05  
**维护人员**: 后端开发团队
