# 前端API接口文档

## 📋 接口概述

本文档定义了前端与后端API的交互规范，包括认证机制、请求格式、响应格式、错误处理等内容。

## 🔐 认证机制

### JWT Token认证
```typescript
// 请求头格式
headers: {
  'Authorization': 'Bearer <access_token>',
  'Content-Type': 'application/json'
}

// Token刷新机制
interface TokenResponse {
  access_token: string
  refresh_token: string
  expires_in: number
  token_type: 'Bearer'
}
```

### 认证流程
1. 用户登录获取access_token和refresh_token
2. 每次API请求携带access_token
3. access_token过期时使用refresh_token刷新
4. refresh_token过期时重新登录

## 📡 API基础规范

### 基础URL
```typescript
const API_BASE_URL = {
  development: 'http://localhost:8000/api/v1',
  production: 'https://api.soic.com/v1'
}
```

### 统一响应格式
```typescript
interface ApiResponse<T> {
  code: number          // 状态码
  message: string       // 响应消息
  data: T              // 响应数据
  timestamp: string    // 响应时间戳
  request_id: string   // 请求ID
}

// 成功响应示例
{
  "code": 200,
  "message": "操作成功",
  "data": { /* 具体数据 */ },
  "timestamp": "2025-08-05T10:30:00Z",
  "request_id": "req_123456789"
}

// 错误响应示例
{
  "code": 400,
  "message": "请求参数错误",
  "data": null,
  "timestamp": "2025-08-05T10:30:00Z",
  "request_id": "req_123456789"
}
```

### 分页响应格式
```typescript
interface PaginatedResponse<T> {
  code: number
  message: string
  data: {
    items: T[]           // 数据列表
    total: number        // 总数量
    page: number         // 当前页码
    page_size: number    // 每页大小
    total_pages: number  // 总页数
    has_next: boolean    // 是否有下一页
    has_prev: boolean    // 是否有上一页
  }
  timestamp: string
  request_id: string
}
```

## 👤 用户管理API

### 用户注册
```typescript
// POST /auth/register
interface RegisterRequest {
  username: string
  email: string
  password: string
  first_name: string
  last_name: string
}

interface RegisterResponse {
  user: User
  tokens: TokenResponse
}
```

### 用户登录
```typescript
// POST /auth/login
interface LoginRequest {
  email: string
  password: string
}

interface LoginResponse {
  user: User
  tokens: TokenResponse
}
```

### 获取用户信息
```typescript
// GET /users/profile
interface User {
  id: number
  username: string
  email: string
  first_name: string
  last_name: string
  avatar_url?: string
  bio?: string
  is_active: boolean
  created_at: string
  updated_at: string
}
```

### 更新用户资料
```typescript
// PUT /users/profile
interface UpdateProfileRequest {
  first_name?: string
  last_name?: string
  bio?: string
  avatar_url?: string
}
```

## 👥 社交功能API

### 好友关系管理
```typescript
// 发送好友请求
// POST /social/friends/request
interface FriendRequestData {
  user_id: number
}

// 获取好友列表
// GET /social/friends
interface Friend {
  id: number
  username: string
  display_name: string
  avatar_url?: string
  status: 'online' | 'offline' | 'away'
  last_seen: string
}

// 获取好友请求列表
// GET /social/friends/requests
interface FriendRequest {
  id: number
  requester: User
  status: 'pending' | 'accepted' | 'declined'
  created_at: string
}
```

### 关注功能
```typescript
// 关注用户
// POST /social/follow
interface FollowRequest {
  user_id: number
}

// 获取关注列表
// GET /social/following
interface Following {
  id: number
  user: User
  created_at: string
}

// 获取粉丝列表
// GET /social/followers
interface Follower {
  id: number
  user: User
  created_at: string
}
```

## 📝 内容管理API

### 动态发布
```typescript
// 发布动态
// POST /content/posts
interface CreatePostRequest {
  content: string
  media_urls?: string[]
  location?: string
  visibility: 'public' | 'friends' | 'private'
}

interface Post {
  id: number
  author: User
  content: string
  media_urls: string[]
  location?: string
  visibility: string
  like_count: number
  comment_count: number
  share_count: number
  is_liked: boolean
  created_at: string
  updated_at: string
}
```

### 获取动态列表
```typescript
// GET /content/posts
interface GetPostsParams {
  page?: number
  page_size?: number
  author_id?: number
  visibility?: string
}
```

### 点赞功能
```typescript
// 点赞动态
// POST /content/posts/{post_id}/like
interface LikeResponse {
  is_liked: boolean
  like_count: number
}

// 取消点赞
// DELETE /content/posts/{post_id}/like
```

### 评论功能
```typescript
// 发表评论
// POST /content/posts/{post_id}/comments
interface CreateCommentRequest {
  content: string
  parent_id?: number  // 回复评论时的父评论ID
}

interface Comment {
  id: number
  author: User
  content: string
  parent_id?: number
  like_count: number
  reply_count: number
  is_liked: boolean
  created_at: string
  updated_at: string
}
```

## 💬 实时通信API

### 会话管理
```typescript
// 创建私聊会话
// POST /chat/conversations
interface CreateConversationRequest {
  type: 'private' | 'group'
  participants: number[]
  name?: string  // 群聊名称
}

interface Conversation {
  id: number
  type: 'private' | 'group'
  name?: string
  participants: User[]
  last_message?: Message
  unread_count: number
  created_at: string
  updated_at: string
}
```

### 消息发送
```typescript
// 发送消息
// POST /chat/conversations/{conversation_id}/messages
interface SendMessageRequest {
  content: string
  message_type: 'text' | 'image' | 'file' | 'voice'
  media_url?: string
  reply_to_id?: number
}

interface Message {
  id: number
  conversation_id: number
  sender: User
  content: string
  message_type: string
  media_url?: string
  reply_to?: Message
  is_read: boolean
  created_at: string
}
```

## 🔍 搜索API

### 全局搜索
```typescript
// GET /search
interface SearchParams {
  query: string
  type?: 'users' | 'posts' | 'all'
  page?: number
  page_size?: number
}

interface SearchResult {
  users: User[]
  posts: Post[]
  total_count: number
}
```

## 📊 统计API

### 用户统计
```typescript
// GET /users/{user_id}/stats
interface UserStats {
  posts_count: number
  friends_count: number
  followers_count: number
  following_count: number
  likes_received: number
}
```

## 🚨 错误码定义

```typescript
enum ApiErrorCode {
  // 通用错误
  SUCCESS = 200,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  METHOD_NOT_ALLOWED = 405,
  CONFLICT = 409,
  INTERNAL_SERVER_ERROR = 500,
  
  // 业务错误
  INVALID_CREDENTIALS = 1001,
  USER_NOT_FOUND = 1002,
  EMAIL_ALREADY_EXISTS = 1003,
  USERNAME_ALREADY_EXISTS = 1004,
  FRIEND_REQUEST_ALREADY_SENT = 2001,
  CANNOT_FRIEND_YOURSELF = 2002,
  POST_NOT_FOUND = 3001,
  COMMENT_NOT_FOUND = 3002,
  CONVERSATION_NOT_FOUND = 4001,
  MESSAGE_NOT_FOUND = 4002
}
```

## 🛠️ API客户端封装

### HTTP客户端
```typescript
// utils/request.ts
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios'
import { useAuthStore } from '@/stores/auth'

class ApiClient {
  private instance: AxiosInstance

  constructor() {
    this.instance = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const authStore = useAuthStore()
        if (authStore.token) {
          config.headers.Authorization = `Bearer ${authStore.token}`
        }
        return config
      },
      (error) => Promise.reject(error)
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => response.data,
      async (error) => {
        if (error.response?.status === 401) {
          const authStore = useAuthStore()
          await authStore.logout()
        }
        return Promise.reject(error)
      }
    )
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.instance.get(url, config)
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.instance.post(url, data, config)
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.instance.put(url, data, config)
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.instance.delete(url, config)
  }
}

export const apiClient = new ApiClient()
```

### API服务封装
```typescript
// api/userApi.ts
import { apiClient } from '@/utils/request'
import type { User, UpdateProfileRequest } from '@/types/user'

export const userApi = {
  getProfile: (): Promise<ApiResponse<User>> =>
    apiClient.get('/users/profile'),

  updateProfile: (data: UpdateProfileRequest): Promise<ApiResponse<User>> =>
    apiClient.put('/users/profile', data),

  getUserStats: (userId: number): Promise<ApiResponse<UserStats>> =>
    apiClient.get(`/users/${userId}/stats`)
}
```

---

**文档版本**: v1.0  
**最后更新**: 2025-08-05  
**维护人员**: 前端开发团队
