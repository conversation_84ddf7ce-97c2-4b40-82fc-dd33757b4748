# 前端组件开发规范

## 📋 组件设计原则

### 核心原则
- **单一职责**: 每个组件只负责一个功能
- **高内聚低耦合**: 组件内部逻辑紧密，组件间依赖最小
- **可复用性**: 设计通用组件，支持多场景使用
- **可测试性**: 组件易于单元测试
- **可维护性**: 代码清晰，文档完善

### 设计模式
- **容器组件与展示组件分离**: 逻辑与UI分离
- **组合优于继承**: 使用组合模式构建复杂组件
- **Props向下，Events向上**: 明确的数据流向
- **插槽机制**: 提供灵活的内容定制能力

## 🏗️ 组件分类体系

### 1. 基础组件 (Base Components)
**特点**: 最小化、无业务逻辑、高复用性
**命名规范**: `Base` + 功能名称

```typescript
// BaseButton.vue - 基础按钮组件
<template>
  <button 
    :class="buttonClasses" 
    :disabled="disabled"
    @click="handleClick"
  >
    <slot />
  </button>
</template>

<script setup lang="ts">
interface Props {
  type?: 'primary' | 'secondary' | 'danger'
  size?: 'small' | 'medium' | 'large'
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  size: 'medium',
  disabled: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()
</script>
```

### 2. 通用组件 (Common Components)
**特点**: 跨业务场景使用，包含基础交互逻辑
**命名规范**: 功能名称 + `Component`

```typescript
// SearchComponent.vue - 搜索组件
<template>
  <div class="search-component">
    <el-input
      v-model="searchValue"
      :placeholder="placeholder"
      @input="handleInput"
      @keyup.enter="handleSearch"
    >
      <template #suffix>
        <el-button @click="handleSearch">搜索</el-button>
      </template>
    </el-input>
  </div>
</template>

<script setup lang="ts">
interface Props {
  placeholder?: string
  debounceTime?: number
}

interface Emits {
  search: [value: string]
  input: [value: string]
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入搜索关键词',
  debounceTime: 300
})

const emit = defineEmits<Emits>()
</script>
```

### 3. 业务组件 (Business Components)
**特点**: 包含特定业务逻辑，与业务场景强相关
**命名规范**: 业务模块 + 功能名称

```typescript
// UserProfileCard.vue - 用户资料卡片
<template>
  <el-card class="user-profile-card">
    <div class="user-avatar">
      <el-avatar :src="user.avatar" :size="80" />
    </div>
    <div class="user-info">
      <h3>{{ user.displayName }}</h3>
      <p>{{ user.bio }}</p>
    </div>
    <div class="user-actions">
      <el-button @click="handleFollow">
        {{ isFollowing ? '取消关注' : '关注' }}
      </el-button>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import type { User } from '@/types/user'

interface Props {
  user: User
  isFollowing?: boolean
}

interface Emits {
  follow: [userId: number]
  unfollow: [userId: number]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
</script>
```

### 4. 布局组件 (Layout Components)
**特点**: 负责页面结构和布局，不包含业务逻辑
**命名规范**: `Layout` + 布局类型

```typescript
// LayoutMain.vue - 主布局组件
<template>
  <div class="layout-main">
    <header class="layout-header">
      <slot name="header" />
    </header>
    <aside class="layout-sidebar" v-if="showSidebar">
      <slot name="sidebar" />
    </aside>
    <main class="layout-content">
      <slot />
    </main>
    <footer class="layout-footer" v-if="showFooter">
      <slot name="footer" />
    </footer>
  </div>
</template>

<script setup lang="ts">
interface Props {
  showSidebar?: boolean
  showFooter?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showSidebar: true,
  showFooter: true
})
</script>
```

## 📝 组件开发规范

### 1. 文件命名规范
- **组件文件**: PascalCase命名 (UserProfile.vue)
- **组件目录**: kebab-case命名 (user-profile/)
- **样式文件**: 与组件同名 (UserProfile.module.css)
- **测试文件**: 组件名 + .test.ts (UserProfile.test.ts)

### 2. 组件结构规范
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入依赖
import { ref, computed, onMounted } from 'vue'
import type { ComponentProps } from './types'

// 2. 类型定义
interface Props {
  // props定义
}

interface Emits {
  // events定义
}

// 3. Props和Emits声明
const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 4. 响应式数据
const state = ref()

// 5. 计算属性
const computedValue = computed(() => {
  // 计算逻辑
})

// 6. 方法定义
const handleAction = () => {
  // 方法实现
}

// 7. 生命周期
onMounted(() => {
  // 初始化逻辑
})

// 8. 暴露给父组件的方法
defineExpose({
  handleAction
})
</script>

<style scoped>
/* 组件样式 */
</style>
```

### 3. Props设计规范
```typescript
// 良好的Props设计
interface Props {
  // 必需属性
  id: number
  title: string
  
  // 可选属性，提供默认值
  size?: 'small' | 'medium' | 'large'
  disabled?: boolean
  
  // 复杂类型
  user?: User
  options?: SelectOption[]
  
  // 函数类型
  validator?: (value: string) => boolean
  formatter?: (value: any) => string
}

// 使用withDefaults提供默认值
const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  disabled: false,
  options: () => []
})
```

### 4. Events设计规范
```typescript
// 明确的事件定义
interface Emits {
  // 基础事件
  click: [event: MouseEvent]
  change: [value: string]
  
  // 业务事件
  submit: [formData: FormData]
  delete: [id: number]
  
  // 状态变更事件
  'update:modelValue': [value: string]
  'update:visible': [visible: boolean]
}

const emit = defineEmits<Emits>()

// 事件触发
const handleSubmit = (data: FormData) => {
  emit('submit', data)
}
```

### 5. 插槽设计规范
```vue
<template>
  <div class="card-component">
    <!-- 默认插槽 -->
    <div class="card-content">
      <slot />
    </div>
    
    <!-- 具名插槽 -->
    <header class="card-header" v-if="$slots.header">
      <slot name="header" />
    </header>
    
    <!-- 作用域插槽 -->
    <div class="card-list">
      <slot 
        name="item" 
        v-for="item in items" 
        :item="item" 
        :index="index"
      />
    </div>
    
    <!-- 条件插槽 -->
    <footer class="card-footer" v-if="$slots.footer">
      <slot name="footer" />
    </footer>
  </div>
</template>
```

## 🎨 样式规范

### 1. CSS类命名规范
- **BEM方法论**: Block__Element--Modifier
- **组件前缀**: 使用组件名作为前缀
- **状态类**: 使用is-前缀表示状态

```scss
// UserCard.vue
.user-card {
  // Block
  &__avatar {
    // Element
  }
  
  &__name {
    // Element
    &--highlighted {
      // Modifier
    }
  }
  
  &.is-loading {
    // State
  }
}
```

### 2. CSS变量使用
```scss
// 定义CSS变量
.user-card {
  --card-padding: 16px;
  --card-border-radius: 8px;
  --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  padding: var(--card-padding);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-shadow);
}
```

### 3. 响应式设计
```scss
.user-card {
  // 移动端
  @media (max-width: 768px) {
    padding: 12px;
  }
  
  // 平板端
  @media (min-width: 768px) and (max-width: 1024px) {
    padding: 16px;
  }
  
  // 桌面端
  @media (min-width: 1024px) {
    padding: 20px;
  }
}
```

## 🧪 组件测试规范

### 1. 单元测试结构
```typescript
// UserCard.test.ts
import { mount } from '@vue/test-utils'
import UserCard from './UserCard.vue'
import type { User } from '@/types/user'

describe('UserCard', () => {
  const mockUser: User = {
    id: 1,
    name: '测试用户',
    avatar: 'avatar.jpg'
  }

  it('应该正确渲染用户信息', () => {
    const wrapper = mount(UserCard, {
      props: { user: mockUser }
    })
    
    expect(wrapper.find('.user-card__name').text()).toBe('测试用户')
    expect(wrapper.find('img').attributes('src')).toBe('avatar.jpg')
  })

  it('应该触发关注事件', async () => {
    const wrapper = mount(UserCard, {
      props: { user: mockUser }
    })
    
    await wrapper.find('.follow-button').trigger('click')
    
    expect(wrapper.emitted('follow')).toBeTruthy()
    expect(wrapper.emitted('follow')[0]).toEqual([mockUser.id])
  })
})
```

### 2. 测试覆盖要求
- **Props测试**: 验证所有props的正确处理
- **Events测试**: 验证所有事件的正确触发
- **插槽测试**: 验证插槽内容的正确渲染
- **状态测试**: 验证组件状态变化的正确性
- **边界测试**: 验证异常情况的处理

## 📚 组件文档规范

### 1. 组件注释
```vue
<script setup lang="ts">
/**
 * 用户资料卡片组件
 * 
 * @description 展示用户基本信息，支持关注/取消关注操作
 * <AUTHOR>
 * @since 2025-08-05
 * @version 1.0.0
 */

interface Props {
  /** 用户信息对象 */
  user: User
  /** 是否显示关注按钮 */
  showFollowButton?: boolean
  /** 卡片大小 */
  size?: 'small' | 'medium' | 'large'
}
</script>
```

### 2. README文档
每个复杂组件应包含README.md文档：
- 组件功能描述
- 使用示例
- API文档（Props、Events、Slots）
- 样式定制说明
- 注意事项

---

**文档版本**: v1.0  
**最后更新**: 2025-08-05  
**维护人员**: 前端开发团队
