# 元宇宙社交空间 - 开发指南

## 📋 开发环境准备

### 系统要求
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **内存**: 8GB以上（推荐16GB）
- **硬盘**: 20GB可用空间
- **网络**: 稳定的互联网连接

### 必需软件安装

#### 1. 开发工具
```bash
# Git版本控制
git --version  # 确认已安装

# VS Code编辑器（推荐）
# 下载地址：https://code.visualstudio.com/

# PyCharm（可选，适合Python开发）
# 下载地址：https://www.jetbrains.com/pycharm/
```

#### 2. 前端环境
```bash
# Node.js 18+
node --version
npm --version

# pnpm包管理器（推荐）
npm install -g pnpm
pnpm --version
```

#### 3. 后端环境
```bash
# Python 3.11+
python --version

# pip包管理器
pip --version

# 虚拟环境工具
python -m venv --help
```

#### 4. 数据库环境
```bash
# MySQL 8.0+
mysql --version

# Redis 7.0+
redis-server --version

# 或使用Docker
docker --version
docker-compose --version
```

## 🚀 项目初始化

### 1. 克隆项目
```bash
git clone <repository-url>
cd soic
```

### 2. 后端环境搭建
```bash
# 进入后端目录
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 升级pip
python -m pip install --upgrade pip

# 安装依赖
pip install -r requirements/development.txt

# 配置环境变量
cp .env.example .env
# 编辑.env文件，配置数据库连接等信息

# 数据库迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 启动开发服务器
python manage.py runserver
```

### 3. 前端环境搭建
```bash
# 进入前端目录
cd frontend

# 安装依赖
pnpm install

# 配置环境变量
cp .env.example .env.local
# 编辑.env.local文件，配置API地址等信息

# 启动开发服务器
pnpm dev
```

### 4. 数据库环境搭建

#### 使用Docker（推荐）
```bash
# 启动MySQL和Redis
docker-compose up -d mysql redis

# 查看服务状态
docker-compose ps
```

#### 本地安装
```bash
# MySQL配置
mysql -u root -p
CREATE DATABASE soic CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'soic_user'@'localhost' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON soic.* TO 'soic_user'@'localhost';
FLUSH PRIVILEGES;

# Redis配置（使用默认配置即可）
redis-server
```

## 🛠️ 开发工作流程

### 1. 分支管理策略
```bash
# 主分支
main        # 生产环境代码
develop     # 开发环境代码

# 功能分支
feature/user-management     # 用户管理功能
feature/social-interaction  # 社交互动功能
feature/real-time-chat      # 实时聊天功能

# 修复分支
hotfix/critical-bug-fix     # 紧急修复

# 发布分支
release/v1.0.0             # 版本发布
```

### 2. 开发流程
```bash
# 1. 从develop分支创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/new-feature

# 2. 开发功能
# 编写代码...

# 3. 提交代码
git add .
git commit -m "feat: 添加新功能"

# 4. 推送到远程仓库
git push origin feature/new-feature

# 5. 创建Pull Request
# 在GitHub/GitLab上创建PR，请求合并到develop分支

# 6. 代码审查通过后合并
# 删除功能分支
git checkout develop
git pull origin develop
git branch -d feature/new-feature
```

### 3. 提交信息规范
```bash
# 提交类型
feat:     # 新功能
fix:      # 修复bug
docs:     # 文档更新
style:    # 代码格式调整
refactor: # 代码重构
test:     # 测试相关
chore:    # 构建过程或辅助工具的变动

# 提交示例
git commit -m "feat: 添加用户注册功能"
git commit -m "fix: 修复登录验证bug"
git commit -m "docs: 更新API文档"
```

## 🧪 测试指南

### 1. 后端测试
```bash
# 运行所有测试
python manage.py test

# 运行特定应用的测试
python manage.py test apps.users

# 运行特定测试类
python manage.py test apps.users.tests.test_views.UserViewSetTestCase

# 生成测试覆盖率报告
coverage run --source='.' manage.py test
coverage report
coverage html  # 生成HTML报告
```

### 2. 前端测试
```bash
# 运行单元测试
pnpm test

# 运行测试并生成覆盖率报告
pnpm test:coverage

# 运行E2E测试
pnpm test:e2e

# 监听模式运行测试
pnpm test:watch
```

### 3. API测试
```bash
# 使用Postman或Insomnia测试API
# 导入API文档：http://localhost:8000/api/docs/

# 使用curl测试
curl -X GET http://localhost:8000/api/v1/users/ \
  -H "Authorization: Bearer <token>"
```

## 📝 代码规范

### 1. Python代码规范
```bash
# 安装代码格式化工具
pip install black isort flake8

# 格式化代码
black .
isort .

# 检查代码质量
flake8 .

# 配置pre-commit钩子
pip install pre-commit
pre-commit install
```

### 2. JavaScript/TypeScript代码规范
```bash
# 格式化代码
pnpm lint:fix

# 检查代码质量
pnpm lint

# 类型检查
pnpm type-check
```

### 3. 代码审查清单
- [ ] 代码功能是否正确实现
- [ ] 是否遵循项目代码规范
- [ ] 是否包含必要的测试
- [ ] 是否更新了相关文档
- [ ] 是否考虑了安全性问题
- [ ] 是否考虑了性能影响
- [ ] 是否处理了错误情况

## 🐛 调试指南

### 1. 后端调试
```python
# 使用Django调试工具栏
pip install django-debug-toolbar

# 在settings.py中配置
INSTALLED_APPS = [
    'debug_toolbar',
    # ...
]

MIDDLEWARE = [
    'debug_toolbar.middleware.DebugToolbarMiddleware',
    # ...
]

# 使用pdb调试
import pdb; pdb.set_trace()

# 使用logging记录日志
import logging
logger = logging.getLogger(__name__)
logger.debug("调试信息")
logger.error("错误信息")
```

### 2. 前端调试
```javascript
// 使用Vue DevTools
// 安装浏览器扩展：Vue.js devtools

// 使用console调试
console.log('调试信息')
console.error('错误信息')

// 使用debugger断点
debugger;

// 使用Vue的调试功能
import { getCurrentInstance } from 'vue'
const instance = getCurrentInstance()
console.log(instance)
```

### 3. 数据库调试
```bash
# 查看Django SQL查询
python manage.py shell
from django.db import connection
print(connection.queries)

# 使用MySQL查询日志
SET GLOBAL general_log = 'ON';
SET GLOBAL general_log_file = '/var/log/mysql/general.log';

# 使用Redis监控
redis-cli monitor
```

## 📊 性能监控

### 1. 后端性能监控
```python
# 使用Django性能分析
pip install django-silk

# 配置Silk
INSTALLED_APPS = [
    'silk',
    # ...
]

MIDDLEWARE = [
    'silk.middleware.SilkyMiddleware',
    # ...
]

# 访问性能报告：http://localhost:8000/silk/
```

### 2. 前端性能监控
```javascript
// 使用Performance API
const start = performance.now()
// 执行代码...
const end = performance.now()
console.log(`执行时间: ${end - start}ms`)

// 使用Vue性能分析
app.config.performance = true
```

## 🚀 部署指南

### 1. 开发环境部署
```bash
# 使用Docker Compose
docker-compose -f docker-compose.dev.yml up -d
```

### 2. 生产环境部署
```bash
# 构建生产镜像
docker build -t soic-backend:latest ./backend
docker build -t soic-frontend:latest ./frontend

# 部署到生产环境
docker-compose -f docker-compose.prod.yml up -d
```

## 📚 学习资源

### 技术文档
- [Django官方文档](https://docs.djangoproject.com/)
- [Vue 3官方文档](https://vuejs.org/)
- [Element Plus文档](https://element-plus.org/)
- [Tailwind CSS文档](https://tailwindcss.com/)

### 项目文档
- [技术选型与架构设计](./技术选型与架构设计.md)
- [前端架构设计](./frontend/架构设计.md)
- [后端DDD设计](./backend/DDD设计文档.md)
- [数据库设计](./backend/数据库设计.md)

## 🤝 团队协作

### 1. 沟通渠道
- **技术讨论**: GitHub Issues/Discussions
- **代码审查**: Pull Request
- **文档协作**: 项目Wiki
- **即时沟通**: 团队聊天工具

### 2. 会议安排
- **每日站会**: 同步开发进度
- **周会**: 回顾和计划
- **代码审查会**: 技术分享和改进

### 3. 知识分享
- **技术分享**: 定期技术分享会
- **文档维护**: 及时更新项目文档
- **最佳实践**: 总结和分享开发经验

---

**文档版本**: v1.0  
**最后更新**: 2025-08-05  
**维护人员**: 开发团队
