# 元宇宙社交空间 - 前端环境变量配置示例
# 复制此文件为 .env.local 并根据实际环境修改配置值

# =============================================================================
# 应用基础配置
# =============================================================================

# 应用标题
VITE_APP_TITLE=元宇宙社交空间

# 应用环境
VITE_APP_ENV=development

# 应用版本（自动从package.json读取）
VITE_APP_VERSION=1.0.0

# =============================================================================
# API服务配置
# =============================================================================

# API基础URL（开发环境）
VITE_API_BASE_URL=http://localhost:8000/api/v1

# WebSocket基础URL（开发环境）
VITE_WS_BASE_URL=ws://localhost:8000/ws

# =============================================================================
# 功能开关配置
# =============================================================================

# 是否启用Mock数据（开发环境可启用）
VITE_ENABLE_MOCK=false

# 是否启用调试模式
VITE_ENABLE_DEBUG=true

# 是否启用PWA功能
VITE_ENABLE_PWA=false

# 是否启用国际化
VITE_ENABLE_I18N=true

# 是否启用主题切换
VITE_ENABLE_THEME_SWITCH=true

# =============================================================================
# 文件上传配置
# =============================================================================

# 上传文件大小限制（MB）
VITE_UPLOAD_SIZE_LIMIT=10

# 支持的图片格式
VITE_SUPPORTED_IMAGE_FORMATS=jpg,jpeg,png,gif,webp

# 支持的视频格式
VITE_SUPPORTED_VIDEO_FORMATS=mp4,avi,mov,wmv

# 支持的音频格式
VITE_SUPPORTED_AUDIO_FORMATS=mp3,wav,ogg,aac

# =============================================================================
# CDN和静态资源配置
# =============================================================================

# CDN基础URL（生产环境使用）
VITE_CDN_BASE_URL=

# 静态资源版本号
VITE_STATIC_VERSION=1.0.0

# =============================================================================
# 第三方服务配置
# =============================================================================

# Sentry错误监控DSN
VITE_SENTRY_DSN=

# 百度统计ID
VITE_BAIDU_ANALYTICS_ID=

# 谷歌分析ID
VITE_GOOGLE_ANALYTICS_ID=

# 微信AppID（用于微信分享等功能）
VITE_WECHAT_APP_ID=

# =============================================================================
# 地图服务配置
# =============================================================================

# 高德地图API Key
VITE_AMAP_API_KEY=

# 百度地图API Key
VITE_BAIDU_MAP_API_KEY=

# =============================================================================
# 支付配置
# =============================================================================

# 支付宝AppID
VITE_ALIPAY_APP_ID=

# 微信支付商户号
VITE_WECHAT_PAY_MERCHANT_ID=

# =============================================================================
# 开发环境专用配置
# =============================================================================

# 是否显示开发工具
VITE_SHOW_DEV_TOOLS=true

# 是否启用热重载
VITE_ENABLE_HMR=true

# 是否显示构建信息
VITE_SHOW_BUILD_INFO=true

# API请求超时时间（毫秒）
VITE_API_TIMEOUT=10000

# =============================================================================
# 性能监控配置
# =============================================================================

# 是否启用性能监控
VITE_ENABLE_PERFORMANCE_MONITOR=false

# 性能监控采样率（0-1）
VITE_PERFORMANCE_SAMPLE_RATE=0.1

# =============================================================================
# 安全配置
# =============================================================================

# 是否启用CSP（内容安全策略）
VITE_ENABLE_CSP=false

# 是否启用HTTPS重定向
VITE_FORCE_HTTPS=false
