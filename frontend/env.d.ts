/**
 * 元宇宙社交空间 - 环境变量类型声明
 * 
 * 这个文件定义了环境变量的TypeScript类型，确保类型安全
 */

/// <reference types="vite/client" />

// Vue组件类型声明
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 环境变量接口定义
interface ImportMetaEnv {
  /** 应用标题 */
  readonly VITE_APP_TITLE: string
  
  /** API基础URL */
  readonly VITE_API_BASE_URL: string
  
  /** WebSocket基础URL */
  readonly VITE_WS_BASE_URL: string
  
  /** 应用环境 */
  readonly VITE_APP_ENV: 'development' | 'production' | 'testing'
  
  /** 是否启用Mock数据 */
  readonly VITE_ENABLE_MOCK: string
  
  /** 是否启用调试模式 */
  readonly VITE_ENABLE_DEBUG: string
  
  /** 上传文件大小限制（MB） */
  readonly VITE_UPLOAD_SIZE_LIMIT: string
  
  /** 支持的图片格式 */
  readonly VITE_SUPPORTED_IMAGE_FORMATS: string
  
  /** CDN基础URL */
  readonly VITE_CDN_BASE_URL: string
  
  /** 是否启用PWA */
  readonly VITE_ENABLE_PWA: string
  
  /** Sentry DSN（错误监控） */
  readonly VITE_SENTRY_DSN: string
  
  /** 百度统计ID */
  readonly VITE_BAIDU_ANALYTICS_ID: string
  
  /** 谷歌分析ID */
  readonly VITE_GOOGLE_ANALYTICS_ID: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// 全局类型声明
declare global {
  /** 窗口对象扩展 */
  interface Window {
    /** 百度统计 */
    _hmt?: any[]
    /** 谷歌分析 */
    gtag?: (...args: any[]) => void
    /** 微信JS-SDK */
    wx?: any
    /** 支付宝JS-SDK */
    ap?: any
  }
  
  /** 环境变量 */
  const __APP_VERSION__: string
  const __BUILD_TIME__: string
}

// 模块声明
declare module 'nprogress' {
  interface NProgress {
    start(): NProgress
    done(): NProgress
    set(n: number): NProgress
    inc(n?: number): NProgress
    configure(options: Partial<NProgressOptions>): NProgress
  }
  
  interface NProgressOptions {
    minimum: number
    template: string
    easing: string
    speed: number
    trickle: boolean
    trickleSpeed: number
    showSpinner: boolean
    barSelector: string
    spinnerSelector: string
    parent: string
  }
  
  const nprogress: NProgress
  export default nprogress
}

declare module 'lodash-es' {
  export * from 'lodash'
}

// Element Plus图标类型声明
declare module '@element-plus/icons-vue' {
  import type { Component } from 'vue'
  const icons: Record<string, Component>
  export default icons
  export * from '@element-plus/icons-vue'
}

export {}
