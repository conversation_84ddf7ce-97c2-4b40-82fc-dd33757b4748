<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  
  <!-- 基础SEO信息 -->
  <title>元宇宙社交空间</title>
  <meta name="description" content="元宇宙社交空间 - 连接虚拟与现实的社交平台，体验沉浸式的3D虚拟社交" />
  <meta name="keywords" content="元宇宙,社交平台,虚拟现实,3D社交,在线聊天,虚拟空间" />
  <meta name="author" content="元宇宙社交空间开发团队" />
  
  <!-- Open Graph标签 -->
  <meta property="og:title" content="元宇宙社交空间" />
  <meta property="og:description" content="连接虚拟与现实的社交平台，体验沉浸式的3D虚拟社交" />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://soic.com" />
  <meta property="og:image" content="/og-image.png" />
  <meta property="og:site_name" content="元宇宙社交空间" />
  <meta property="og:locale" content="zh_CN" />
  
  <!-- Twitter Card标签 -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="元宇宙社交空间" />
  <meta name="twitter:description" content="连接虚拟与现实的社交平台，体验沉浸式的3D虚拟社交" />
  <meta name="twitter:image" content="/twitter-image.png" />
  
  <!-- 移动端优化 -->
  <meta name="format-detection" content="telephone=no" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
  <meta name="apple-mobile-web-app-title" content="元宇宙社交空间" />
  
  <!-- 图标配置 -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <link rel="icon" type="image/png" href="/favicon.png" />
  <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
  <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#3b82f6" />
  
  <!-- PWA配置 -->
  <link rel="manifest" href="/manifest.json" />
  <meta name="theme-color" content="#3b82f6" />
  <meta name="msapplication-TileColor" content="#3b82f6" />
  <meta name="msapplication-config" content="/browserconfig.xml" />
  
  <!-- 预连接优化 -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  
  <!-- 字体加载 -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  
  <!-- 预加载关键资源 -->
  <link rel="preload" href="/src/main.ts" as="script" />
  
  <!-- 样式重置和基础样式 -->
  <style>
    /* 基础重置样式 */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    html {
      font-family: 'Inter', 'PingFang SC', 'Microsoft YaHei', sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: optimizeLegibility;
    }
    
    body {
      margin: 0;
      background-color: #ffffff;
      color: #1f2937;
      line-height: 1.6;
    }
    
    /* 加载动画样式 */
    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      transition: opacity 0.3s ease-out;
    }
    
    .loading-logo {
      width: 80px;
      height: 80px;
      margin-bottom: 24px;
      animation: pulse 2s infinite;
    }
    
    .loading-text {
      color: white;
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 32px;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-top: 3px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.1); }
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* 隐藏加载动画 */
    .loading-container.hidden {
      opacity: 0;
      pointer-events: none;
    }
    
    /* 暗色模式支持 */
    @media (prefers-color-scheme: dark) {
      body {
        background-color: #111827;
        color: #f9fafb;
      }
    }
    
    /* 无障碍支持 */
    @media (prefers-reduced-motion: reduce) {
      .loading-logo,
      .loading-spinner {
        animation: none;
      }
    }
  </style>
</head>

<body>
  <!-- 应用根节点 -->
  <div id="app">
    <!-- 加载动画 -->
    <div class="loading-container" id="loading">
      <div class="loading-logo">
        <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="50" cy="50" r="45" stroke="white" stroke-width="3" fill="none" opacity="0.3"/>
          <circle cx="50" cy="50" r="30" stroke="white" stroke-width="2" fill="none" opacity="0.6"/>
          <circle cx="50" cy="50" r="15" fill="white"/>
        </svg>
      </div>
      <div class="loading-text">元宇宙社交空间</div>
      <div class="loading-spinner"></div>
    </div>
  </div>
  
  <!-- 应用脚本 -->
  <script type="module" src="/src/main.ts"></script>
  
  <!-- 加载完成后隐藏加载动画 -->
  <script>
    // 监听应用加载完成事件
    window.addEventListener('load', function() {
      setTimeout(function() {
        const loading = document.getElementById('loading');
        if (loading) {
          loading.classList.add('hidden');
          setTimeout(function() {
            loading.remove();
          }, 300);
        }
      }, 500);
    });
    
    // 错误处理
    window.addEventListener('error', function(e) {
      console.error('应用加载错误:', e.error);
    });
    
    // 未处理的Promise拒绝
    window.addEventListener('unhandledrejection', function(e) {
      console.error('未处理的Promise拒绝:', e.reason);
    });
  </script>
</body>
</html>
