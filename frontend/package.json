{"name": "soic-frontend", "version": "1.0.0", "description": "元宇宙社交空间前端应用", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --ignore-path .gitignore", "format": "prettier --write src/", "test": "vitest", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "socket.io-client": "^4.7.4", "vue-i18n": "^9.8.0"}, "devDependencies": {"@types/node": "^20.10.4", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "jsdom": "^23.0.1", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "typescript": "~5.3.3", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2", "vite": "^5.0.10", "vitest": "^1.0.4", "vue-tsc": "^1.8.25"}, "keywords": ["vue3", "typescript", "element-plus", "tailwindcss", "pinia", "vite", "社交平台", "元宇宙"], "author": "元宇宙社交空间开发团队", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/soic-frontend.git"}, "bugs": {"url": "https://github.com/your-org/soic-frontend/issues"}, "homepage": "https://soic.com"}