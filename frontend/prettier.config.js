/**
 * 元宇宙社交空间 - Prettier配置
 * 
 * 这个文件配置了Prettier代码格式化规则，确保代码风格一致性
 */

export default {
  // 基础配置
  printWidth: 100, // 每行最大字符数
  tabWidth: 2, // 缩进空格数
  useTabs: false, // 使用空格而不是制表符
  semi: false, // 不使用分号
  singleQuote: true, // 使用单引号
  quoteProps: 'as-needed', // 仅在需要时为对象属性添加引号
  
  // JSX配置
  jsxSingleQuote: true, // JSX中使用单引号
  
  // 尾随逗号配置
  trailingComma: 'all', // 在多行结构中添加尾随逗号
  
  // 括号配置
  bracketSpacing: true, // 对象字面量的括号间添加空格
  bracketSameLine: false, // 将多行HTML元素的>放在下一行
  
  // 箭头函数参数括号
  arrowParens: 'avoid', // 单参数箭头函数省略括号
  
  // 换行符配置
  endOfLine: 'lf', // 使用LF换行符
  
  // 嵌入语言格式化
  embeddedLanguageFormatting: 'auto',
  
  // HTML配置
  htmlWhitespaceSensitivity: 'css',
  
  // Vue文件配置
  vueIndentScriptAndStyle: false, // Vue文件中script和style标签不缩进
  
  // 插件配置
  plugins: [
    '@trivago/prettier-plugin-sort-imports',
  ],
  
  // 导入排序配置
  importOrder: [
    '^vue$',
    '^vue-router$',
    '^pinia$',
    '^@vue/(.*)$',
    '^element-plus$',
    '^@element-plus/(.*)$',
    '<THIRD_PARTY_MODULES>',
    '^@/types/(.*)$',
    '^@/utils/(.*)$',
    '^@/api/(.*)$',
    '^@/stores/(.*)$',
    '^@/components/(.*)$',
    '^@/views/(.*)$',
    '^@/(.*)$',
    '^[./]',
  ],
  importOrderSeparation: true,
  importOrderSortSpecifiers: true,
  
  // 文件覆盖配置
  overrides: [
    {
      files: '*.vue',
      options: {
        parser: 'vue',
      },
    },
    {
      files: '*.json',
      options: {
        parser: 'json',
        trailingComma: 'none',
      },
    },
    {
      files: '*.md',
      options: {
        parser: 'markdown',
        printWidth: 80,
        proseWrap: 'always',
      },
    },
    {
      files: '*.yaml',
      options: {
        parser: 'yaml',
        tabWidth: 2,
      },
    },
    {
      files: '*.yml',
      options: {
        parser: 'yaml',
        tabWidth: 2,
      },
    },
  ],
}
