<script setup lang="ts">
/**
 * 元宇宙社交空间 - 根组件
 * 
 * 这是应用的根组件，负责：
 * - 提供全局布局结构
 * - 管理全局状态
 * - 处理路由切换
 * - 提供主题切换功能
 */

import { onMounted, provide, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElConfigProvider } from 'element-plus'
import zhCN from 'element-plus/es/locale/lang/zh-cn'

// 导入stores
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { useThemeStore } from '@/stores/theme'

// 导入组件
import AppLoading from '@/components/common/AppLoading.vue'
import AppNotification from '@/components/common/AppNotification.vue'

const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()
const themeStore = useThemeStore()

// 提供全局配置
provide('appConfig', {
  name: '元宇宙社交空间',
  version: '1.0.0',
})

// 监听路由变化
watch(
  () => router.currentRoute.value,
  (to) => {
    // 更新页面标题
    document.title = to.meta.title 
      ? `${to.meta.title} - 元宇宙社交空间` 
      : '元宇宙社交空间'
    
    // 更新当前路由信息
    appStore.setCurrentRoute(to)
  },
  { immediate: true }
)

// 监听主题变化
watch(
  () => themeStore.isDark,
  (isDark) => {
    // 切换HTML类名
    if (isDark) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  },
  { immediate: true }
)

// 组件挂载时的初始化
onMounted(async () => {
  try {
    // 初始化应用
    await appStore.initialize()
    
    // 尝试自动登录
    if (userStore.hasToken) {
      await userStore.getCurrentUser()
    }
    
    // 初始化主题
    themeStore.initializeTheme()
    
    console.log('✅ 应用初始化完成')
  } catch (error) {
    console.error('❌ 应用初始化失败:', error)
  }
})

// 处理全局键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + K 打开搜索
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    // TODO: 打开全局搜索
  }
  
  // ESC 关闭模态框
  if (event.key === 'Escape') {
    // TODO: 关闭当前模态框
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<template>
  <ElConfigProvider :locale="zhCN">
    <div 
      id="app" 
      class="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200"
    >
      <!-- 全局加载状态 -->
      <AppLoading v-if="appStore.isLoading" />
      
      <!-- 主要内容区域 -->
      <div v-else class="app-container">
        <!-- 路由视图 -->
        <RouterView v-slot="{ Component, route }">
          <Transition
            name="page"
            mode="out-in"
            appear
          >
            <KeepAlive :include="appStore.cachedViews">
              <component 
                :is="Component" 
                :key="route.fullPath"
              />
            </KeepAlive>
          </Transition>
        </RouterView>
      </div>
      
      <!-- 全局通知组件 -->
      <AppNotification />
      
      <!-- 全局模态框容器 -->
      <div id="modal-container" />
      
      <!-- 全局提示容器 -->
      <div id="tooltip-container" />
    </div>
  </ElConfigProvider>
</template>

<style scoped>
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 页面切换动画 */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 0;
  }
}

/* 暗色模式适配 */
.dark .app-container {
  background-color: theme('colors.gray.900');
  color: theme('colors.gray.100');
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .page-enter-active,
  .page-leave-active {
    transition: none;
  }
}

/* 打印样式 */
@media print {
  .app-container {
    background: white !important;
    color: black !important;
  }
}
</style>
