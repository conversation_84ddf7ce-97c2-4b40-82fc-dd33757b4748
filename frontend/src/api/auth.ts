/**
 * 元宇宙社交空间 - 认证API
 */

import { api } from './index'
import type { LoginCredentials, RegisterData, User } from '@/types/user'
import type { TokenResponse } from '@/types/api'

export const authApi = {
  // 登录
  login: (credentials: LoginCredentials) =>
    api.post<{ user: User; tokens: TokenResponse }>('/auth/login/', credentials),

  // 注册
  register: (data: RegisterData) =>
    api.post<{ user: User; tokens: TokenResponse }>('/auth/register/', data),

  // 登出
  logout: (refreshToken: string) =>
    api.post('/auth/logout/', { refresh_token: refreshToken }),

  // 刷新token
  refreshToken: (refreshToken: string) =>
    api.post<TokenResponse>('/auth/token/refresh/', { refresh: refreshToken }),

  // 验证token
  verifyToken: (token: string) =>
    api.post('/auth/token/verify/', { token }),

  // 忘记密码
  forgotPassword: (email: string) =>
    api.post('/auth/forgot-password/', { email }),

  // 重置密码
  resetPassword: (token: string, password: string, passwordConfirm: string) =>
    api.post('/auth/reset-password/', {
      token,
      new_password: password,
      new_password_confirm: passwordConfirm
    }),

  // 邮箱验证
  verifyEmail: (token: string) =>
    api.post('/auth/verify-email/', { token }),
}
