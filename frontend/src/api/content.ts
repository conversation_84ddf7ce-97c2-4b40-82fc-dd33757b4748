/**
 * 元宇宙社交空间 - 内容管理API
 */

import { api } from './index'
import type { Post, Comment, Topic } from '@/types'

export const contentApi = {
  // 动态管理
  getPosts: (params?: any) =>
    api.get<{ results: Post[]; next?: string; previous?: string }>('/content/posts/', { params }),

  getPostDetail: (postId: number) =>
    api.get<Post>(`/content/posts/${postId}/`),

  createPost: (data: any) =>
    api.post<Post>('/content/posts/', data),

  updatePost: (postId: number, data: any) =>
    api.put<Post>(`/content/posts/${postId}/`, data),

  deletePost: (postId: number) =>
    api.delete(`/content/posts/${postId}/`),

  // 动态流和推荐
  getFeed: (params?: any) =>
    api.get<{ results: Post[]; next?: string; previous?: string }>('/content/posts/feed/', { params }),

  getRecommendedPosts: (params?: any) =>
    api.get<{ results: Post[]; next?: string; previous?: string }>('/content/posts/recommended/', { params }),

  searchPosts: (query: string, params?: any) =>
    api.get<{ results: Post[]; next?: string; previous?: string }>('/content/posts/search/', { 
      params: { q: query, ...params } 
    }),

  // 用户动态
  getUserPosts: (userId: number, params?: any) =>
    api.get<{ results: Post[]; next?: string; previous?: string }>(`/content/users/${userId}/posts/`, { params }),

  // 动态互动
  likePost: (postId: number) =>
    api.post<{ message: string; is_liked: boolean; like_count: number }>(`/content/posts/${postId}/like/`),

  sharePost: (postId: number, comment?: string) =>
    api.post(`/content/posts/${postId}/share/`, { comment }),

  // 评论管理
  getComments: (postId: number, params?: any) =>
    api.get<{ results: Comment[]; next?: string; previous?: string }>(`/content/posts/${postId}/comments/`, { params }),

  createComment: (postId: number, data: { content: string; parent?: number }) =>
    api.post<Comment>(`/content/posts/${postId}/comments/`, data),

  updateComment: (postId: number, commentId: number, data: any) =>
    api.put<Comment>(`/content/posts/${postId}/comments/${commentId}/`, data),

  deleteComment: (postId: number, commentId: number) =>
    api.delete(`/content/posts/${postId}/comments/${commentId}/`),

  likeComment: (commentId: number, postId: number) =>
    api.post<{ message: string; is_liked: boolean; like_count: number }>(`/content/posts/${postId}/comments/${commentId}/like/`),

  // 话题管理
  getTopics: (params?: any) =>
    api.get<Topic[]>('/content/topics/', { params }),

  getTrendingTopics: () =>
    api.get<Topic[]>('/content/topics/trending/'),

  searchTopics: (query: string) =>
    api.get<Topic[]>('/content/topics/search/', { params: { q: query } }),

  getTopicDetail: (topicId: number) =>
    api.get<Topic>(`/content/topics/${topicId}/`),

  // 媒体文件上传
  uploadMedia: (file: File, mediaType: string) => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('media_type', mediaType)
    
    return api.post<{
      file_url: string
      thumbnail_url: string
      filename: string
      file_size: number
      media_type: string
    }>('/content/media/upload/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
}
