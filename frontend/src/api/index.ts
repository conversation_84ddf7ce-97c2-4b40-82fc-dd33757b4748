/**
 * 元宇宙社交空间 - API客户端基础封装
 * 
 * 这个文件提供了HTTP客户端的基础封装，包括：
 * - Axios实例配置
 * - 请求/响应拦截器
 * - 错误处理
 * - 认证管理
 * - 请求重试机制
 */

import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import NProgress from 'nprogress'

import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import type { ApiResponse } from '@/types/api'

// API基础配置
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api/v1',
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000,
  retryCount: 3,
  retryDelay: 1000,
}

// 创建Axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
})

// 请求计数器（用于控制loading状态）
let requestCount = 0

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 增加请求计数
    requestCount++
    
    // 显示进度条
    if (requestCount === 1) {
      NProgress.start()
    }
    
    // 添加认证token
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }
    
    // 添加请求ID（用于追踪）
    config.headers['X-Request-ID'] = generateRequestId()
    
    // 添加时间戳（防止缓存）
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      }
    }
    
    // 开发环境日志
    if (import.meta.env.DEV) {
      console.log(`🚀 API请求: ${config.method?.toUpperCase()} ${config.url}`, config)
    }
    
    return config
  },
  (error) => {
    requestCount--
    if (requestCount === 0) {
      NProgress.done()
    }
    
    console.error('❌ 请求配置错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse<ApiResponse<any>>) => {
    // 减少请求计数
    requestCount--
    if (requestCount === 0) {
      NProgress.done()
    }
    
    // 开发环境日志
    if (import.meta.env.DEV) {
      console.log(`✅ API响应: ${response.config.method?.toUpperCase()} ${response.config.url}`, response.data)
    }
    
    // 检查业务状态码
    const { code, message } = response.data
    if (code !== 200) {
      // 业务错误
      ElMessage.error(message || '请求失败')
      return Promise.reject(new Error(message || '请求失败'))
    }
    
    return response
  },
  async (error) => {
    // 减少请求计数
    requestCount--
    if (requestCount === 0) {
      NProgress.done()
    }
    
    const { config, response } = error
    
    // 开发环境日志
    if (import.meta.env.DEV) {
      console.error(`❌ API错误: ${config?.method?.toUpperCase()} ${config?.url}`, error)
    }
    
    // 处理网络错误
    if (!response) {
      ElMessage.error('网络连接失败，请检查网络设置')
      return Promise.reject(error)
    }
    
    const { status, data } = response
    
    // 处理不同的HTTP状态码
    switch (status) {
      case 401:
        // 未授权，尝试刷新token
        return handleUnauthorized(config)
        
      case 403:
        // 禁止访问
        ElMessage.error('访问被拒绝，权限不足')
        break
        
      case 404:
        // 资源不存在
        ElMessage.error('请求的资源不存在')
        break
        
      case 422:
        // 表单验证错误
        handleValidationError(data)
        break
        
      case 429:
        // 请求过于频繁
        ElMessage.error('请求过于频繁，请稍后再试')
        break
        
      case 500:
        // 服务器错误
        ElMessage.error('服务器内部错误，请稍后再试')
        break
        
      case 502:
      case 503:
      case 504:
        // 服务不可用
        ElMessage.error('服务暂时不可用，请稍后再试')
        break
        
      default:
        // 其他错误
        ElMessage.error(data?.message || '请求失败')
    }
    
    return Promise.reject(error)
  }
)

// 处理401未授权错误
async function handleUnauthorized(originalConfig: AxiosRequestConfig) {
  const userStore = useUserStore()

  // 如果已经在刷新token，避免重复刷新
  if (originalConfig._retry) {
    userStore.clearUserData()
    // 重定向到登录页
    if (typeof window !== 'undefined') {
      window.location.href = '/auth/login'
    }
    return Promise.reject(new Error('认证失败'))
  }

  originalConfig._retry = true

  try {
    // 尝试刷新token
    const newToken = await userStore.refreshAccessToken()

    // 更新请求头中的token
    if (originalConfig.headers) {
      originalConfig.headers.Authorization = `Bearer ${newToken}`
    }

    // 重新发送原始请求
    return apiClient(originalConfig)
  } catch (error) {
    // 刷新失败，清除用户数据并重定向到登录页
    userStore.clearUserData()

    if (typeof window !== 'undefined') {
      ElMessageBox.confirm(
        '登录状态已过期，请重新登录',
        '提示',
        {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        window.location.href = '/auth/login'
      }).catch(() => {
        // 用户取消，也重定向到登录页
        window.location.href = '/auth/login'
      })
    }

    return Promise.reject(error)
  }
}

// 处理表单验证错误
function handleValidationError(data: any) {
  if (data?.errors && typeof data.errors === 'object') {
    // 显示第一个验证错误
    const firstError = Object.values(data.errors)[0]
    if (Array.isArray(firstError) && firstError.length > 0) {
      ElMessage.error(firstError[0] as string)
    }
  } else {
    ElMessage.error(data?.message || '表单验证失败')
  }
}

// 生成请求ID
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// 请求重试函数
async function retryRequest(
  requestFn: () => Promise<any>,
  retryCount = API_CONFIG.retryCount,
  delay = API_CONFIG.retryDelay
): Promise<any> {
  try {
    return await requestFn()
  } catch (error) {
    if (retryCount > 0) {
      await new Promise(resolve => setTimeout(resolve, delay))
      return retryRequest(requestFn, retryCount - 1, delay * 2)
    }
    throw error
  }
}

// 导出API客户端实例
export default apiClient

// 导出便捷方法
export const api = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    apiClient.get(url, config),
    
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    apiClient.post(url, data, config),
    
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    apiClient.put(url, data, config),
    
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    apiClient.patch(url, data, config),
    
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    apiClient.delete(url, config),
}

// 导出重试方法
export { retryRequest }

// 声明模块扩展
declare module 'axios' {
  interface AxiosRequestConfig {
    _retry?: boolean
  }
}
