/**
 * 元宇宙社交空间 - 社交功能API
 */

import { api } from './index'
import type { Friend, FriendRequest, Follow, User } from '@/types'

export const socialApi = {
  // 好友管理
  getFriends: () =>
    api.get<Friend[]>('/social/friends/list/'),

  getFriendRequests: () =>
    api.get<FriendRequest[]>('/social/friends/pending/'),

  sendFriendRequest: (userId: number, message?: string) =>
    api.post<FriendRequest>('/social/friends/request/', {
      addressee_id: userId,
      message,
    }),

  acceptFriendRequest: (requestId: number) =>
    api.post<FriendRequest>(`/social/friends/${requestId}/accept/`),

  declineFriendRequest: (requestId: number) =>
    api.post<FriendRequest>(`/social/friends/${requestId}/decline/`),

  removeFriend: (friendshipId: number) =>
    api.delete(`/social/friends/${friendshipId}/`),

  // 关注功能
  followUser: (userId: number) =>
    api.post<Follow>(`/social/follows/follow/${userId}/`),

  unfollowUser: (userId: number) =>
    api.delete(`/social/follows/unfollow/${userId}/`),

  getFollowers: () =>
    api.get<Follow[]>('/social/follows/followers/'),

  getFollowing: () =>
    api.get<Follow[]>('/social/follows/following/'),

  // 用户搜索和推荐
  searchUsers: (query: string) =>
    api.get<User[]>('/social/users/search/', { params: { q: query } }),

  getRecommendedUsers: () =>
    api.get<User[]>('/social/users/search/recommended/'),

  getUserDetail: (userId: number) =>
    api.get<User>(`/social/users/${userId}/`),

  // 用户屏蔽
  blockUser: (userId: number, reason?: string) =>
    api.post(`/social/blocks/block/${userId}/`, { reason }),

  unblockUser: (userId: number) =>
    api.delete(`/social/blocks/unblock/${userId}/`),

  getBlockedUsers: () =>
    api.get('/social/blocks/'),
}
