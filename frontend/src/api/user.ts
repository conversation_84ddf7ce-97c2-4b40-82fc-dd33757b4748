/**
 * 元宇宙社交空间 - 用户API
 */

import { api } from './index'
import type { User, UserProfile, UserStats } from '@/types/user'

export const userApi = {
  // 获取当前用户信息
  getCurrentUser: () =>
    api.get<User>('/users/me'),

  // 更新用户资料
  updateProfile: (data: Partial<UserProfile>) =>
    api.put<User>('/users/profile', data),

  // 获取用户权限
  getUserPermissions: () =>
    api.get<string[]>('/users/permissions'),

  // 获取用户偏好
  getUserPreferences: () =>
    api.get('/users/preferences'),

  // 更新用户偏好
  updatePreferences: (preferences: any) =>
    api.put('/users/preferences', preferences),

  // 更新在线状态
  updateOnlineStatus: (status: string) =>
    api.patch('/users/status', { status }),

  // 获取用户统计
  getUserStats: (userId: number) =>
    api.get<UserStats>(`/users/${userId}/stats`),
}
