<script setup lang="ts">
/**
 * 元宇宙社交空间 - 全局加载组件
 */

interface Props {
  text?: string
  size?: 'small' | 'medium' | 'large'
}

withDefaults(defineProps<Props>(), {
  text: '加载中...',
  size: 'medium',
})
</script>

<template>
  <div class="app-loading" :class="`loading-${size}`">
    <div class="loading-spinner">
      <div class="spinner-ring"></div>
      <div class="spinner-ring"></div>
      <div class="spinner-ring"></div>
    </div>
    <p class="loading-text">{{ text }}</p>
  </div>
</template>

<style scoped>
.app-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.loading-spinner {
  position: relative;
  margin-bottom: 16px;
}

.spinner-ring {
  position: absolute;
  border: 3px solid transparent;
  border-top: 3px solid var(--el-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-small .spinner-ring {
  width: 24px;
  height: 24px;
}

.loading-medium .spinner-ring {
  width: 40px;
  height: 40px;
}

.loading-large .spinner-ring {
  width: 60px;
  height: 60px;
}

.spinner-ring:nth-child(1) {
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  animation-delay: 0.3s;
  border-top-color: var(--el-color-success);
}

.spinner-ring:nth-child(3) {
  animation-delay: 0.6s;
  border-top-color: var(--el-color-warning);
}

.loading-text {
  color: var(--el-text-color-regular);
  font-size: 14px;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
