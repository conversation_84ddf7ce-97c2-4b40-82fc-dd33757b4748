<script setup lang="ts">
/**
 * 元宇宙社交空间 - 全局通知组件
 */

import { ref, onMounted } from 'vue'

// 通知列表
const notifications = ref<any[]>([])

// 组件挂载时初始化
onMounted(() => {
  // TODO: 监听全局通知事件
})

// 关闭通知
const closeNotification = (id: string) => {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}
</script>

<template>
  <div class="app-notification">
    <transition-group name="notification" tag="div" class="notification-list">
      <div
        v-for="notification in notifications"
        :key="notification.id"
        class="notification-item"
        :class="`notification-${notification.type}`"
      >
        <div class="notification-content">
          <h4>{{ notification.title }}</h4>
          <p>{{ notification.message }}</p>
        </div>
        <el-button
          type="primary"
          link
          @click="closeNotification(notification.id)"
        >
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </transition-group>
  </div>
</template>

<style scoped>
.app-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
  pointer-events: none;
}

.notification-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: var(--el-box-shadow);
  border-left: 4px solid var(--el-color-primary);
  pointer-events: auto;
  min-width: 300px;
  max-width: 400px;
}

.notification-success {
  border-left-color: var(--el-color-success);
}

.notification-warning {
  border-left-color: var(--el-color-warning);
}

.notification-error {
  border-left-color: var(--el-color-danger);
}

.notification-content {
  flex: 1;
}

.notification-content h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.notification-content p {
  margin: 0;
  font-size: 13px;
  color: var(--el-text-color-regular);
  line-height: 1.4;
}

/* 动画效果 */
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}
</style>
