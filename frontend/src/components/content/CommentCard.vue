<script setup lang="ts">
/**
 * 元宇宙社交空间 - 评论卡片组件
 * 
 * 用于展示单个评论的卡片组件，包含：
 * - 用户信息展示
 * - 评论内容展示
 * - 交互按钮（点赞、回复）
 * - 回复列表展示
 */

import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

import { useContentStore } from '@/stores/content'
import { useUserStore } from '@/stores/user'
import type { Comment } from '@/types/content'

interface Props {
  comment: Comment
  postId: number
  level?: number
  showReplies?: boolean
  maxLevel?: number
}

const props = withDefaults(defineProps<Props>(), {
  level: 0,
  showReplies: true,
  maxLevel: 3
})

const emit = defineEmits<{
  reply: [comment: Comment]
  like: [commentId: number]
  userClick: [userId: number]
  delete: [commentId: number]
}>()

const contentStore = useContentStore()
const userStore = useUserStore()

// 状态管理
const liking = ref(false)
const showReplyInput = ref(false)
const replyContent = ref('')
const submittingReply = ref(false)
const showAllReplies = ref(false)

// 计算属性
const relativeTime = computed(() => {
  const date = new Date(props.comment.created_at)
  return formatDistanceToNow(date, { 
    addSuffix: true, 
    locale: zhCN 
  })
})

const isOwner = computed(() => {
  return userStore.user?.id === props.comment.author.id
})

const canReply = computed(() => {
  return props.level < props.maxLevel
})

const displayReplies = computed(() => {
  if (!props.comment.replies || !props.showReplies) return []
  
  if (showAllReplies.value) {
    return props.comment.replies
  }
  
  // 默认只显示前3条回复
  return props.comment.replies.slice(0, 3)
})

const hasMoreReplies = computed(() => {
  return props.comment.replies && props.comment.replies.length > 3 && !showAllReplies.value
})

// 处理点赞
const handleLike = async () => {
  if (liking.value) return
  
  try {
    liking.value = true
    await contentStore.likeComment(props.comment.id, props.postId)
    emit('like', props.comment.id)
  } catch (error) {
    console.error('点赞评论失败:', error)
  } finally {
    liking.value = false
  }
}

// 处理回复
const handleReply = () => {
  if (!canReply.value) return
  showReplyInput.value = !showReplyInput.value
  if (showReplyInput.value) {
    // 自动聚焦到输入框
    setTimeout(() => {
      const input = document.querySelector(`#reply-input-${props.comment.id}`) as HTMLTextAreaElement
      input?.focus()
    }, 100)
  }
}

// 提交回复
const submitReply = async () => {
  if (!replyContent.value.trim() || submittingReply.value) return
  
  try {
    submittingReply.value = true
    
    await contentStore.createComment(props.postId, {
      content: replyContent.value.trim(),
      parent: props.comment.id
    })
    
    // 清空输入框并隐藏
    replyContent.value = ''
    showReplyInput.value = false
    
    ElMessage.success('回复成功')
    emit('reply', props.comment)
    
  } catch (error) {
    console.error('回复失败:', error)
    ElMessage.error('回复失败')
  } finally {
    submittingReply.value = false
  }
}

// 取消回复
const cancelReply = () => {
  replyContent.value = ''
  showReplyInput.value = false
}

// 处理用户点击
const handleUserClick = () => {
  emit('userClick', props.comment.author.id)
}

// 处理删除
const handleDelete = () => {
  emit('delete', props.comment.id)
}

// 显示所有回复
const toggleAllReplies = () => {
  showAllReplies.value = !showAllReplies.value
}
</script>

<template>
  <div class="comment-card" :class="`level-${level}`">
    <!-- 评论主体 -->
    <div class="comment-main">
      <!-- 用户头像 -->
      <div class="comment-avatar">
        <el-avatar 
          :src="comment.author.avatar_url"
          :size="level === 0 ? 36 : 32"
          @click="handleUserClick"
        >
          {{ comment.author.display_name?.charAt(0) || 'U' }}
        </el-avatar>
      </div>
      
      <!-- 评论内容 -->
      <div class="comment-content">
        <!-- 用户信息和时间 -->
        <div class="comment-header">
          <span class="user-name" @click="handleUserClick">
            {{ comment.author.display_name || comment.author.username }}
            <el-tag v-if="comment.author.is_verified" type="success" size="small">
              <el-icon><Select /></el-icon>
            </el-tag>
          </span>
          <span class="comment-time">{{ relativeTime }}</span>
        </div>
        
        <!-- 评论文本 -->
        <div class="comment-text">
          {{ comment.content }}
        </div>
        
        <!-- 交互按钮 -->
        <div class="comment-actions">
          <el-button
            type="text"
            size="small"
            :class="{ 'is-liked': comment.is_liked }"
            :loading="liking"
            @click="handleLike"
          >
            <el-icon><Like /></el-icon>
            {{ comment.like_count > 0 ? comment.like_count : '点赞' }}
          </el-button>
          
          <el-button
            v-if="canReply"
            type="text"
            size="small"
            @click="handleReply"
          >
            <el-icon><ChatDotRound /></el-icon>
            回复
          </el-button>
          
          <el-dropdown v-if="isOwner" trigger="click">
            <el-button type="text" size="small">
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleDelete">删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
    
    <!-- 回复输入框 -->
    <div v-if="showReplyInput" class="reply-input">
      <el-input
        :id="`reply-input-${comment.id}`"
        v-model="replyContent"
        type="textarea"
        :rows="2"
        :placeholder="`回复 ${comment.author.display_name || comment.author.username}...`"
        resize="none"
        maxlength="500"
        show-word-limit
      />
      <div class="reply-actions">
        <el-button size="small" @click="cancelReply">取消</el-button>
        <el-button 
          type="primary" 
          size="small"
          :loading="submittingReply"
          :disabled="!replyContent.trim()"
          @click="submitReply"
        >
          {{ submittingReply ? '发送中...' : '发送' }}
        </el-button>
      </div>
    </div>
    
    <!-- 回复列表 -->
    <div v-if="displayReplies.length > 0" class="replies-list">
      <CommentCard
        v-for="reply in displayReplies"
        :key="reply.id"
        :comment="reply"
        :post-id="postId"
        :level="level + 1"
        :show-replies="false"
        @reply="$emit('reply', $event)"
        @like="$emit('like', $event)"
        @user-click="$emit('userClick', $event)"
        @delete="$emit('delete', $event)"
      />
      
      <!-- 显示更多回复 -->
      <div v-if="hasMoreReplies" class="show-more-replies">
        <el-button type="text" size="small" @click="toggleAllReplies">
          查看全部 {{ comment.reply_count }} 条回复
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.comment-card {
  margin-bottom: 16px;
}

.comment-card.level-0 {
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding-bottom: 16px;
}

.comment-card.level-1,
.comment-card.level-2,
.comment-card.level-3 {
  margin-left: 20px;
  margin-top: 12px;
  padding-left: 16px;
  border-left: 2px solid var(--el-border-color-lighter);
}

.comment-main {
  display: flex;
  gap: 12px;
}

.comment-avatar {
  flex-shrink: 0;
}

.comment-avatar .el-avatar {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.comment-avatar .el-avatar:hover {
  transform: scale(1.05);
}

.comment-content {
  flex: 1;
  min-width: 0;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 6px;
}

.user-name {
  font-weight: 600;
  color: var(--el-text-color-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: color 0.3s ease;
}

.user-name:hover {
  color: var(--el-color-primary);
}

.comment-time {
  font-size: 0.75rem;
  color: var(--el-text-color-secondary);
}

.comment-text {
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
  word-wrap: break-word;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.comment-actions .el-button {
  padding: 4px 8px;
  height: auto;
  font-size: 0.75rem;
}

.comment-actions .el-button.is-liked {
  color: var(--el-color-primary);
}

.reply-input {
  margin-top: 12px;
  margin-left: 48px;
}

.reply-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
}

.replies-list {
  margin-top: 8px;
}

.show-more-replies {
  margin-left: 48px;
  margin-top: 8px;
}

.show-more-replies .el-button {
  font-size: 0.75rem;
  color: var(--el-text-color-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comment-card.level-1,
  .comment-card.level-2,
  .comment-card.level-3 {
    margin-left: 12px;
    padding-left: 12px;
  }
  
  .reply-input {
    margin-left: 32px;
  }
  
  .show-more-replies {
    margin-left: 32px;
  }
  
  .comment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .comment-actions {
    flex-wrap: wrap;
  }
}
</style>
