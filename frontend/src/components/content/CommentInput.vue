<script setup lang="ts">
/**
 * 元宇宙社交空间 - 评论输入组件
 * 
 * 用于发表评论的输入组件，包含：
 * - 评论文本输入
 * - 表情选择器
 * - 提交按钮
 * - 字数限制
 */

import { ref, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

import { useContentStore } from '@/stores/content'
import { useUserStore } from '@/stores/user'

interface Props {
  postId: number
  parentId?: number
  placeholder?: string
  autoFocus?: boolean
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '写下你的评论...',
  autoFocus: false,
  compact: false
})

const emit = defineEmits<{
  submit: [comment: any]
  cancel: []
}>()

const contentStore = useContentStore()
const userStore = useUserStore()

// 状态管理
const content = ref('')
const submitting = ref(false)
const focused = ref(false)
const inputRef = ref<HTMLTextAreaElement>()

// 配置
const maxLength = 500
const minLength = 1

// 计算属性
const canSubmit = computed(() => {
  const trimmed = content.value.trim()
  return trimmed.length >= minLength && 
         trimmed.length <= maxLength && 
         !submitting.value
})

const characterCount = computed(() => content.value.length)

const isOverLimit = computed(() => characterCount.value > maxLength)

// 处理提交
const handleSubmit = async () => {
  if (!canSubmit.value) return
  
  try {
    submitting.value = true
    
    const commentData = {
      content: content.value.trim(),
      ...(props.parentId && { parent: props.parentId })
    }
    
    const response = await contentStore.createComment(props.postId, commentData)
    
    // 清空输入框
    content.value = ''
    focused.value = false
    
    ElMessage.success(props.parentId ? '回复成功' : '评论成功')
    emit('submit', response.data)
    
  } catch (error: any) {
    console.error('发表评论失败:', error)
    const errorMessage = error.response?.data?.message || '发表评论失败'
    ElMessage.error(errorMessage)
  } finally {
    submitting.value = false
  }
}

// 处理取消
const handleCancel = () => {
  content.value = ''
  focused.value = false
  emit('cancel')
}

// 处理焦点
const handleFocus = () => {
  focused.value = true
}

const handleBlur = () => {
  // 延迟失焦，避免点击按钮时立即失焦
  setTimeout(() => {
    if (!content.value.trim()) {
      focused.value = false
    }
  }, 200)
}

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + Enter 提交
  if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
    event.preventDefault()
    handleSubmit()
  }
  
  // Escape 取消
  if (event.key === 'Escape') {
    event.preventDefault()
    handleCancel()
  }
}

// 自动聚焦
if (props.autoFocus) {
  nextTick(() => {
    inputRef.value?.focus()
  })
}

// 暴露方法
defineExpose({
  focus: () => inputRef.value?.focus(),
  clear: () => {
    content.value = ''
    focused.value = false
  }
})
</script>

<template>
  <div class="comment-input" :class="{ 'is-compact': compact, 'is-focused': focused }">
    <!-- 用户头像 -->
    <div v-if="!compact" class="input-avatar">
      <el-avatar 
        :src="userStore.avatar"
        :size="36"
      >
        {{ userStore.displayName?.charAt(0) || 'U' }}
      </el-avatar>
    </div>
    
    <!-- 输入区域 -->
    <div class="input-container">
      <!-- 文本输入框 -->
      <el-input
        ref="inputRef"
        v-model="content"
        type="textarea"
        :placeholder="placeholder"
        :rows="focused || content ? 3 : 1"
        :maxlength="maxLength"
        resize="none"
        :class="{ 'is-over-limit': isOverLimit }"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
      />
      
      <!-- 工具栏 -->
      <div v-if="focused || content" class="input-toolbar">
        <div class="toolbar-left">
          <!-- 表情按钮 -->
          <el-button type="text" size="small">
            <el-icon><Smile /></el-icon>
          </el-button>
          
          <!-- 字数统计 -->
          <span class="char-count" :class="{ 'is-over-limit': isOverLimit }">
            {{ characterCount }}/{{ maxLength }}
          </span>
        </div>
        
        <div class="toolbar-right">
          <!-- 取消按钮 -->
          <el-button 
            v-if="content || focused"
            size="small" 
            @click="handleCancel"
          >
            取消
          </el-button>
          
          <!-- 提交按钮 -->
          <el-button
            type="primary"
            size="small"
            :loading="submitting"
            :disabled="!canSubmit"
            @click="handleSubmit"
          >
            {{ submitting ? '发送中...' : (parentId ? '回复' : '评论') }}
          </el-button>
        </div>
      </div>
      
      <!-- 快捷提示 -->
      <div v-if="focused && !compact" class="input-tips">
        <span class="tip-text">
          按 Ctrl+Enter 快速发送，按 Esc 取消
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.comment-input {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.comment-input.is-focused {
  background: white;
  box-shadow: var(--el-box-shadow-light);
}

.comment-input.is-compact {
  padding: 12px;
  background: transparent;
}

.input-avatar {
  flex-shrink: 0;
}

.input-container {
  flex: 1;
  min-width: 0;
}

.comment-input :deep(.el-textarea__inner) {
  border: none;
  background: transparent;
  box-shadow: none;
  padding: 8px 0;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: none;
  transition: all 0.3s ease;
}

.comment-input.is-focused :deep(.el-textarea__inner) {
  background: white;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  padding: 12px;
  box-shadow: 0 0 0 1px var(--el-color-primary-light-8);
}

.comment-input :deep(.el-textarea__inner):focus {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
}

.comment-input :deep(.el-textarea__inner.is-over-limit) {
  border-color: var(--el-color-danger);
}

.input-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding: 0 4px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.char-count {
  font-size: 0.75rem;
  color: var(--el-text-color-secondary);
  transition: color 0.3s ease;
}

.char-count.is-over-limit {
  color: var(--el-color-danger);
  font-weight: 600;
}

.input-tips {
  margin-top: 8px;
  padding: 0 4px;
}

.tip-text {
  font-size: 0.75rem;
  color: var(--el-text-color-placeholder);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comment-input {
    padding: 12px;
    gap: 8px;
  }
  
  .input-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: space-between;
  }
  
  .input-tips {
    display: none;
  }
}
</style>
