<script setup lang="ts">
/**
 * 元宇宙社交空间 - 评论列表组件
 * 
 * 评论列表容器组件，包含：
 * - 评论列表展示
 * - 分页加载
 * - 排序选择
 * - 空状态展示
 * - 加载状态展示
 */

import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

import { useContentStore } from '@/stores/content'
import CommentCard from './CommentCard.vue'
import CommentInput from './CommentInput.vue'
import type { Comment } from '@/types/content'

interface Props {
  postId: number
  autoLoad?: boolean
  showInput?: boolean
  sortBy?: 'latest' | 'popular'
}

const props = withDefaults(defineProps<Props>(), {
  autoLoad: true,
  showInput: true,
  sortBy: 'latest'
})

const emit = defineEmits<{
  commentSubmit: [comment: Comment]
  userClick: [userId: number]
}>()

const contentStore = useContentStore()

// 状态管理
const comments = ref<Comment[]>([])
const loading = ref(false)
const loadingMore = ref(false)
const hasMore = ref(true)
const page = ref(1)
const sortOrder = ref(props.sortBy)

// 计算属性
const sortedComments = computed(() => {
  const sorted = [...comments.value]
  
  if (sortOrder.value === 'popular') {
    return sorted.sort((a, b) => {
      // 按点赞数和回复数排序
      const scoreA = a.like_count * 2 + a.reply_count
      const scoreB = b.like_count * 2 + b.reply_count
      return scoreB - scoreA
    })
  }
  
  // 默认按时间排序（最新在前）
  return sorted.sort((a, b) => 
    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  )
})

// 加载评论列表
const loadComments = async (refresh = false) => {
  try {
    if (refresh) {
      page.value = 1
      hasMore.value = true
      loading.value = true
    } else {
      loadingMore.value = true
    }
    
    const response = await contentStore.getComments(props.postId, {
      page: page.value,
      ordering: sortOrder.value === 'popular' ? '-like_count' : '-created_at'
    })
    
    if (refresh) {
      comments.value = response.data.results
    } else {
      comments.value.push(...response.data.results)
    }
    
    hasMore.value = !!response.data.next
    if (hasMore.value) {
      page.value += 1
    }
    
  } catch (error) {
    console.error('加载评论失败:', error)
    ElMessage.error('加载评论失败')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 处理评论提交
const handleCommentSubmit = async (comment: Comment) => {
  // 添加到列表开头
  comments.value.unshift(comment)
  emit('commentSubmit', comment)
}

// 处理评论点赞
const handleCommentLike = async (commentId: number) => {
  try {
    const response = await contentStore.likeComment(commentId, props.postId)
    
    // 更新本地状态
    const updateComment = (commentList: Comment[]) => {
      for (const comment of commentList) {
        if (comment.id === commentId) {
          comment.is_liked = response.data.is_liked
          comment.like_count = response.data.like_count
          break
        }
        // 递归更新回复
        if (comment.replies) {
          updateComment(comment.replies)
        }
      }
    }
    
    updateComment(comments.value)
    
  } catch (error) {
    console.error('点赞评论失败:', error)
    ElMessage.error('操作失败')
  }
}

// 处理评论回复
const handleCommentReply = async (parentComment: Comment) => {
  // 刷新评论列表以获取最新回复
  await loadComments(true)
}

// 处理评论删除
const handleCommentDelete = async (commentId: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这条评论吗？', '确认删除', {
      type: 'warning'
    })
    
    await contentStore.deleteComment(props.postId, commentId)
    
    // 从列表中移除
    const removeComment = (commentList: Comment[]) => {
      for (let i = 0; i < commentList.length; i++) {
        if (commentList[i].id === commentId) {
          commentList.splice(i, 1)
          return true
        }
        // 递归删除回复
        if (commentList[i].replies && removeComment(commentList[i].replies!)) {
          return true
        }
      }
      return false
    }
    
    removeComment(comments.value)
    ElMessage.success('删除成功')
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除评论失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 处理用户点击
const handleUserClick = (userId: number) => {
  emit('userClick', userId)
}

// 处理排序变化
const handleSortChange = (newSort: string) => {
  sortOrder.value = newSort as 'latest' | 'popular'
  loadComments(true)
}

// 加载更多
const loadMore = () => {
  if (!loadingMore.value && hasMore.value) {
    loadComments(false)
  }
}

// 监听排序变化
watch(() => props.sortBy, (newSort) => {
  sortOrder.value = newSort
  loadComments(true)
})

// 生命周期
onMounted(() => {
  if (props.autoLoad) {
    loadComments(true)
  }
})

// 暴露方法
defineExpose({
  refresh: () => loadComments(true),
  loadMore
})
</script>

<template>
  <div class="comment-list">
    <!-- 评论输入框 -->
    <div v-if="showInput" class="comment-input-section">
      <CommentInput
        :post-id="postId"
        @submit="handleCommentSubmit"
      />
    </div>
    
    <!-- 评论头部 -->
    <div class="comment-header">
      <div class="comment-count">
        <span>{{ comments.length }} 条评论</span>
      </div>
      
      <div class="comment-sort">
        <el-select
          :model-value="sortOrder"
          size="small"
          style="width: 100px;"
          @change="handleSortChange"
        >
          <el-option label="最新" value="latest" />
          <el-option label="热门" value="popular" />
        </el-select>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="comment-skeleton" v-for="i in 3" :key="i">
        <el-skeleton animated>
          <template #template>
            <div class="skeleton-comment">
              <el-skeleton-item variant="circle" style="width: 36px; height: 36px;" />
              <div class="skeleton-content">
                <el-skeleton-item variant="text" style="width: 120px; margin-bottom: 8px;" />
                <el-skeleton-item variant="text" style="width: 100%; margin-bottom: 4px;" />
                <el-skeleton-item variant="text" style="width: 80%;" />
              </div>
            </div>
          </template>
        </el-skeleton>
      </div>
    </div>
    
    <!-- 评论列表 -->
    <div v-else-if="sortedComments.length > 0" class="comments-container">
      <CommentCard
        v-for="comment in sortedComments"
        :key="comment.id"
        :comment="comment"
        :post-id="postId"
        @reply="handleCommentReply"
        @like="handleCommentLike"
        @user-click="handleUserClick"
        @delete="handleCommentDelete"
      />
      
      <!-- 加载更多 -->
      <div v-if="hasMore" class="load-more">
        <el-button
          v-if="!loadingMore"
          type="text"
          @click="loadMore"
        >
          加载更多评论
        </el-button>
        <div v-else class="loading-more">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>加载中...</span>
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-empty description="暂无评论">
        <template #image>
          <el-icon size="64"><ChatDotRound /></el-icon>
        </template>
        <p>成为第一个评论的人吧！</p>
      </el-empty>
    </div>
  </div>
</template>

<style scoped>
.comment-list {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.comment-input-section {
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background: var(--el-fill-color-lighter);
}

.comment-count {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.loading-state {
  padding: 20px;
}

.comment-skeleton {
  margin-bottom: 20px;
}

.skeleton-comment {
  display: flex;
  gap: 12px;
}

.skeleton-content {
  flex: 1;
}

.comments-container {
  padding: 20px;
}

.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  border-top: 1px solid var(--el-border-color-lighter);
  margin-top: 16px;
}

.loading-more {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-secondary);
  font-size: 0.875rem;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

.empty-state p {
  margin-top: 16px;
  color: var(--el-text-color-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comment-header {
    padding: 12px 16px;
  }
  
  .comments-container {
    padding: 16px;
  }
  
  .empty-state {
    padding: 40px 16px;
  }
}
</style>
