<script setup lang="ts">
/**
 * 元宇宙社交空间 - 动态卡片组件
 * 
 * 用于展示单个动态的卡片组件，包含：
 * - 用户信息展示
 * - 动态内容展示
 * - 媒体文件展示
 * - 交互按钮（点赞、评论、分享）
 */

import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

import { useContentStore } from '@/stores/content'
import { useUserStore } from '@/stores/user'
import type { Post } from '@/types/content'

interface Props {
  post: Post
  showActions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showActions: true
})

const emit = defineEmits<{
  like: [postId: number]
  comment: [postId: number]
  share: [postId: number]
  userClick: [userId: number]
}>()

const contentStore = useContentStore()
const userStore = useUserStore()

// 状态管理
const liking = ref(false)
const sharing = ref(false)

// 计算属性
const relativeTime = computed(() => {
  const date = new Date(props.post.published_at || props.post.created_at)
  return formatDistanceToNow(date, { 
    addSuffix: true, 
    locale: zhCN 
  })
})

const isOwner = computed(() => {
  return userStore.user?.id === props.post.author.id
})

// 处理点赞
const handleLike = async () => {
  if (liking.value) return
  
  try {
    liking.value = true
    await contentStore.likePost(props.post.id)
    emit('like', props.post.id)
  } catch (error) {
    console.error('点赞失败:', error)
  } finally {
    liking.value = false
  }
}

// 处理评论
const handleComment = () => {
  emit('comment', props.post.id)
}

// 处理分享
const handleShare = async () => {
  if (sharing.value) return
  
  try {
    sharing.value = true
    // TODO: 实现分享功能
    emit('share', props.post.id)
    ElMessage.success('分享成功')
  } catch (error) {
    console.error('分享失败:', error)
  } finally {
    sharing.value = false
  }
}

// 处理用户点击
const handleUserClick = () => {
  emit('userClick', props.post.author.id)
}

// 处理媒体预览
const handleMediaPreview = (url: string, type: string) => {
  // TODO: 实现媒体预览功能
  console.log('预览媒体:', url, type)
}
</script>

<template>
  <div class="post-card">
    <!-- 用户信息头部 -->
    <div class="post-header">
      <div class="user-info" @click="handleUserClick">
        <el-avatar 
          :src="post.author.avatar_url"
          :size="40"
          class="user-avatar"
        >
          {{ post.author.display_name?.charAt(0) || 'U' }}
        </el-avatar>
        
        <div class="user-details">
          <div class="user-name">
            {{ post.author.display_name || post.author.username }}
            <el-tag v-if="post.author.is_verified" type="success" size="small">
              <el-icon><Select /></el-icon>
            </el-tag>
          </div>
          <div class="post-meta">
            <span class="post-time">{{ relativeTime }}</span>
            <span v-if="post.location" class="post-location">
              <el-icon><Location /></el-icon>
              {{ post.location }}
            </span>
            <el-tag 
              v-if="post.visibility !== 'public'" 
              size="small" 
              type="info"
            >
              {{ post.visibility === 'friends' ? '好友可见' : '私有' }}
            </el-tag>
          </div>
        </div>
      </div>
      
      <!-- 更多操作 -->
      <el-dropdown v-if="isOwner" trigger="click">
        <el-button type="text" size="small">
          <el-icon><MoreFilled /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>编辑</el-dropdown-item>
            <el-dropdown-item>删除</el-dropdown-item>
            <el-dropdown-item divided>举报</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    
    <!-- 动态内容 -->
    <div class="post-content">
      <div class="post-text">
        {{ post.content }}
      </div>
      
      <!-- 话题标签 -->
      <div v-if="post.topics && post.topics.length > 0" class="post-topics">
        <el-tag
          v-for="topic in post.topics"
          :key="topic.id"
          size="small"
          type="primary"
          effect="plain"
        >
          #{{ topic.display_name }}
        </el-tag>
      </div>
    </div>
    
    <!-- 媒体内容 -->
    <div v-if="post.media_files && post.media_files.length > 0" class="post-media">
      <div class="media-grid" :class="`grid-${Math.min(post.media_files.length, 4)}`">
        <div
          v-for="(media, index) in post.media_files.slice(0, 4)"
          :key="media.id"
          class="media-item"
          @click="handleMediaPreview(media.file_url, media.media_type)"
        >
          <img
            v-if="media.media_type === 'image'"
            :src="media.file_url"
            :alt="media.file_name"
            class="media-image"
          />
          <video
            v-else-if="media.media_type === 'video'"
            :src="media.file_url"
            class="media-video"
            controls
            preload="metadata"
          />
          
          <!-- 更多媒体提示 -->
          <div
            v-if="index === 3 && post.media_files.length > 4"
            class="media-more"
          >
            <span>+{{ post.media_files.length - 4 }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 交互统计 -->
    <div class="post-stats">
      <div class="stats-left">
        <span v-if="post.like_count > 0" class="stat-item">
          <el-icon><Like /></el-icon>
          {{ post.like_count }}
        </span>
        <span v-if="post.comment_count > 0" class="stat-item">
          {{ post.comment_count }} 条评论
        </span>
        <span v-if="post.share_count > 0" class="stat-item">
          {{ post.share_count }} 次分享
        </span>
      </div>
      <div class="stats-right">
        <span v-if="post.view_count > 0" class="stat-item">
          {{ post.view_count }} 次浏览
        </span>
      </div>
    </div>
    
    <!-- 交互按钮 -->
    <div v-if="showActions" class="post-actions">
      <el-button
        type="text"
        :class="{ 'is-liked': post.is_liked }"
        :loading="liking"
        @click="handleLike"
      >
        <el-icon><Like /></el-icon>
        {{ post.is_liked ? '已赞' : '点赞' }}
      </el-button>
      
      <el-button type="text" @click="handleComment">
        <el-icon><ChatDotRound /></el-icon>
        评论
      </el-button>
      
      <el-button 
        type="text" 
        :loading="sharing"
        @click="handleShare"
      >
        <el-icon><Share /></el-icon>
        分享
      </el-button>
    </div>
  </div>
</template>

<style scoped>
.post-card {
  background: white;
  border-radius: 12px;
  box-shadow: var(--el-box-shadow-light);
  margin-bottom: 16px;
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.post-card:hover {
  box-shadow: var(--el-box-shadow);
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 20px 12px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  flex: 1;
}

.user-info:hover .user-name {
  color: var(--el-color-primary);
}

.user-avatar {
  flex-shrink: 0;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 600;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
  transition: color 0.3s ease;
}

.post-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.875rem;
  color: var(--el-text-color-secondary);
}

.post-location {
  display: flex;
  align-items: center;
  gap: 4px;
}

.post-content {
  padding: 0 20px 16px;
}

.post-text {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--el-text-color-primary);
  margin-bottom: 12px;
  word-wrap: break-word;
}

.post-topics {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.post-media {
  margin-bottom: 16px;
}

.media-grid {
  display: grid;
  gap: 4px;
  border-radius: 8px;
  overflow: hidden;
}

.grid-1 {
  grid-template-columns: 1fr;
}

.grid-2 {
  grid-template-columns: 1fr 1fr;
}

.grid-3 {
  grid-template-columns: 2fr 1fr 1fr;
}

.grid-4 {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.media-item {
  position: relative;
  cursor: pointer;
  background: var(--el-fill-color-light);
  aspect-ratio: 16/9;
  overflow: hidden;
}

.media-image,
.media-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.media-item:hover .media-image,
.media-item:hover .media-video {
  transform: scale(1.05);
}

.media-more {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
}

.post-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px 12px;
  font-size: 0.875rem;
  color: var(--el-text-color-secondary);
}

.stats-left,
.stats-right {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.post-actions {
  display: flex;
  border-top: 1px solid var(--el-border-color-lighter);
  padding: 8px 12px;
}

.post-actions .el-button {
  flex: 1;
  margin: 0 4px;
  border-radius: 8px;
  height: 40px;
}

.post-actions .el-button.is-liked {
  color: var(--el-color-primary);
}

.post-actions .el-button:hover {
  background-color: var(--el-fill-color-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .post-header {
    padding: 12px 16px 8px;
  }
  
  .post-content {
    padding: 0 16px 12px;
  }
  
  .post-stats {
    padding: 0 16px 8px;
  }
  
  .post-actions {
    padding: 6px 8px;
  }
  
  .post-actions .el-button {
    margin: 0 2px;
    height: 36px;
    font-size: 0.875rem;
  }
  
  .media-grid.grid-3,
  .media-grid.grid-4 {
    grid-template-columns: 1fr 1fr;
  }
}
</style>
