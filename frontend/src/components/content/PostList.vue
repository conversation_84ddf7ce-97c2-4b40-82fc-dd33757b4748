<script setup lang="ts">
/**
 * 元宇宙社交空间 - 动态列表组件
 * 
 * 动态列表容器组件，包含：
 * - 动态列表展示
 * - 无限滚动加载
 * - 下拉刷新
 * - 空状态展示
 * - 加载状态展示
 */

import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

import { useContentStore } from '@/stores/content'
import PostCard from './PostCard.vue'
import type { Post } from '@/types/content'

interface Props {
  listType?: 'posts' | 'feed' | 'recommended'
  userId?: number
  autoLoad?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  listType: 'posts',
  autoLoad: true
})

const emit = defineEmits<{
  postClick: [post: Post]
  userClick: [userId: number]
}>()

const contentStore = useContentStore()

// 状态管理
const listRef = ref<HTMLElement>()
const refreshing = ref(false)
const loadingMore = ref(false)

// 计算属性
const posts = computed(() => {
  switch (props.listType) {
    case 'feed':
      return contentStore.feedPosts
    case 'recommended':
      return contentStore.recommendedPosts
    default:
      return contentStore.posts
  }
})

const loading = computed(() => {
  switch (props.listType) {
    case 'feed':
      return contentStore.loading.feed
    case 'recommended':
      return contentStore.loading.recommended
    default:
      return contentStore.loading.posts
  }
})

const hasMore = computed(() => {
  switch (props.listType) {
    case 'feed':
      return contentStore.pagination.feed.hasMore
    default:
      return contentStore.pagination.posts.hasMore
  }
})

// 加载数据
const loadPosts = async (refresh = false) => {
  try {
    switch (props.listType) {
      case 'feed':
        await contentStore.fetchFeed(refresh)
        break
      case 'recommended':
        await contentStore.fetchRecommendedPosts(refresh)
        break
      default:
        await contentStore.fetchPosts(refresh)
        break
    }
  } catch (error) {
    console.error('加载动态失败:', error)
  }
}

// 下拉刷新
const handleRefresh = async () => {
  if (refreshing.value) return
  
  try {
    refreshing.value = true
    await loadPosts(true)
    ElMessage.success('刷新成功')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    refreshing.value = false
  }
}

// 加载更多
const loadMore = async () => {
  if (loadingMore.value || !hasMore.value || loading.value) return
  
  try {
    loadingMore.value = true
    await loadPosts(false)
  } catch (error) {
    ElMessage.error('加载更多失败')
  } finally {
    loadingMore.value = false
  }
}

// 滚动监听
const handleScroll = () => {
  if (!listRef.value) return
  
  const { scrollTop, scrollHeight, clientHeight } = listRef.value
  const threshold = 200 // 距离底部200px时开始加载
  
  if (scrollHeight - scrollTop - clientHeight < threshold) {
    loadMore()
  }
}

// 事件处理
const handlePostLike = (postId: number) => {
  console.log('点赞动态:', postId)
}

const handlePostComment = (postId: number) => {
  const post = posts.value.find(p => p.id === postId)
  if (post) {
    emit('postClick', post)
  }
}

const handlePostShare = (postId: number) => {
  console.log('分享动态:', postId)
}

const handleUserClick = (userId: number) => {
  emit('userClick', userId)
}

// 生命周期
onMounted(() => {
  if (props.autoLoad) {
    loadPosts(true)
  }
  
  // 添加滚动监听
  if (listRef.value) {
    listRef.value.addEventListener('scroll', handleScroll, { passive: true })
  }
})

onUnmounted(() => {
  // 移除滚动监听
  if (listRef.value) {
    listRef.value.removeEventListener('scroll', handleScroll)
  }
})

// 暴露方法
defineExpose({
  refresh: handleRefresh,
  loadMore
})
</script>

<template>
  <div class="post-list" ref="listRef">
    <!-- 下拉刷新提示 -->
    <div v-if="refreshing" class="refresh-indicator">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>正在刷新...</span>
    </div>
    
    <!-- 动态列表 -->
    <div class="posts-container">
      <PostCard
        v-for="post in posts"
        :key="post.id"
        :post="post"
        @like="handlePostLike"
        @comment="handlePostComment"
        @share="handlePostShare"
        @user-click="handleUserClick"
      />
    </div>
    
    <!-- 加载更多指示器 -->
    <div v-if="loadingMore" class="load-more-indicator">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载更多...</span>
    </div>
    
    <!-- 没有更多数据 -->
    <div v-else-if="!hasMore && posts.length > 0" class="no-more-data">
      <el-divider>没有更多动态了</el-divider>
    </div>
    
    <!-- 空状态 -->
    <div v-if="!loading && posts.length === 0" class="empty-state">
      <el-empty description="暂无动态">
        <template #image>
          <el-icon size="64"><DocumentCopy /></el-icon>
        </template>
        <el-button type="primary" @click="handleRefresh">
          刷新试试
        </el-button>
      </el-empty>
    </div>
    
    <!-- 初始加载状态 -->
    <div v-if="loading && posts.length === 0" class="loading-state">
      <div class="loading-skeleton" v-for="i in 3" :key="i">
        <el-skeleton animated>
          <template #template>
            <div class="skeleton-header">
              <el-skeleton-item variant="circle" style="width: 40px; height: 40px;" />
              <div class="skeleton-user">
                <el-skeleton-item variant="text" style="width: 120px;" />
                <el-skeleton-item variant="text" style="width: 80px;" />
              </div>
            </div>
            <el-skeleton-item variant="text" style="width: 100%; margin: 16px 0;" />
            <el-skeleton-item variant="text" style="width: 80%; margin-bottom: 16px;" />
            <el-skeleton-item variant="rect" style="width: 100%; height: 200px; margin-bottom: 16px;" />
            <div class="skeleton-actions">
              <el-skeleton-item variant="button" style="width: 60px;" />
              <el-skeleton-item variant="button" style="width: 60px;" />
              <el-skeleton-item variant="button" style="width: 60px;" />
            </div>
          </template>
        </el-skeleton>
      </div>
    </div>
  </div>
</template>

<style scoped>
.post-list {
  height: 100%;
  overflow-y: auto;
  padding: 0 16px;
}

.refresh-indicator,
.load-more-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  color: var(--el-text-color-secondary);
  font-size: 0.875rem;
}

.posts-container {
  max-width: 600px;
  margin: 0 auto;
}

.no-more-data {
  margin: 20px 0;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px 20px;
}

.loading-state {
  max-width: 600px;
  margin: 0 auto;
}

.loading-skeleton {
  background: white;
  border-radius: 12px;
  box-shadow: var(--el-box-shadow-light);
  padding: 20px;
  margin-bottom: 16px;
}

.skeleton-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.skeleton-user {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-actions {
  display: flex;
  gap: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 滚动条样式 */
.post-list::-webkit-scrollbar {
  width: 6px;
}

.post-list::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

.post-list::-webkit-scrollbar-thumb {
  background: var(--el-fill-color-dark);
  border-radius: 3px;
}

.post-list::-webkit-scrollbar-thumb:hover {
  background: var(--el-fill-color-darker);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .post-list {
    padding: 0 8px;
  }
  
  .loading-skeleton {
    padding: 16px;
  }
  
  .empty-state {
    min-height: 300px;
    padding: 20px;
  }
}
</style>
