# 内容管理组件

本目录包含元宇宙社交空间的内容管理相关组件，包括动态展示和评论系统。

## 📦 组件列表

### 动态相关组件

### PostCard.vue
**动态卡片组件** - 用于展示单个动态的卡片组件

**功能特性：**
- ✅ 用户信息展示（头像、用户名、认证标识）
- ✅ 动态内容展示（文本、话题标签）
- ✅ 媒体文件展示（图片、视频，支持多媒体网格布局）
- ✅ 交互统计（点赞数、评论数、分享数、浏览数）
- ✅ 交互按钮（点赞、评论、分享）
- ✅ 时间显示（相对时间，如"2小时前"）
- ✅ 位置信息展示
- ✅ 可见性标识
- ✅ 响应式设计

**Props：**
- `post: Post` - 动态数据对象
- `showActions?: boolean` - 是否显示交互按钮（默认true）

**Events：**
- `like: [postId: number]` - 点赞事件
- `comment: [postId: number]` - 评论事件
- `share: [postId: number]` - 分享事件
- `userClick: [userId: number]` - 用户点击事件

### PostList.vue
**动态列表组件** - 动态列表容器组件

**功能特性：**
- ✅ 动态列表展示
- ✅ 无限滚动加载
- ✅ 下拉刷新
- ✅ 空状态展示
- ✅ 加载状态展示（骨架屏）
- ✅ 支持多种列表类型（posts/feed/recommended）
- ✅ 响应式设计

**Props：**
- `listType?: 'posts' | 'feed' | 'recommended'` - 列表类型（默认'posts'）
- `userId?: number` - 用户ID（用于获取特定用户的动态）
- `autoLoad?: boolean` - 是否自动加载（默认true）

**Events：**
- `postClick: [post: Post]` - 动态点击事件
- `userClick: [userId: number]` - 用户点击事件

**暴露方法：**
- `refresh()` - 刷新列表
- `loadMore()` - 加载更多

### 评论相关组件

### CommentCard.vue
**评论卡片组件** - 用于展示单个评论的卡片组件

**功能特性：**
- ✅ 用户信息展示（头像、用户名、认证标识）
- ✅ 评论内容展示
- ✅ 交互统计（点赞数）
- ✅ 交互按钮（点赞、回复）
- ✅ 回复功能（支持嵌套回复，最大3层）
- ✅ 时间显示（相对时间）
- ✅ 评论删除（仅作者可见）
- ✅ 响应式设计

**Props：**
- `comment: Comment` - 评论数据对象
- `postId: number` - 动态ID
- `level?: number` - 评论层级（默认0）
- `showReplies?: boolean` - 是否显示回复（默认true）
- `maxLevel?: number` - 最大回复层级（默认3）

**Events：**
- `reply: [comment: Comment]` - 回复事件
- `like: [commentId: number]` - 点赞事件
- `userClick: [userId: number]` - 用户点击事件
- `delete: [commentId: number]` - 删除事件

### CommentInput.vue
**评论输入组件** - 用于发表评论的输入组件

**功能特性：**
- ✅ 评论文本输入
- ✅ 字数限制和统计
- ✅ 快捷键支持（Ctrl+Enter提交，Esc取消）
- ✅ 自动聚焦
- ✅ 紧凑模式
- ✅ 表情选择器（框架已搭建）
- ✅ 响应式设计

**Props：**
- `postId: number` - 动态ID
- `parentId?: number` - 父评论ID（用于回复）
- `placeholder?: string` - 输入框占位符
- `autoFocus?: boolean` - 是否自动聚焦
- `compact?: boolean` - 是否紧凑模式

**Events：**
- `submit: [comment: Comment]` - 提交事件
- `cancel: []` - 取消事件

**暴露方法：**
- `focus()` - 聚焦输入框
- `clear()` - 清空输入框

### CommentList.vue
**评论列表组件** - 评论列表容器组件

**功能特性：**
- ✅ 评论列表展示
- ✅ 分页加载
- ✅ 排序选择（最新、热门）
- ✅ 空状态展示
- ✅ 加载状态展示（骨架屏）
- ✅ 评论输入框集成
- ✅ 评论互动（点赞、回复、删除）
- ✅ 响应式设计

**Props：**
- `postId: number` - 动态ID
- `autoLoad?: boolean` - 是否自动加载（默认true）
- `showInput?: boolean` - 是否显示输入框（默认true）
- `sortBy?: 'latest' | 'popular'` - 排序方式（默认'latest'）

**Events：**
- `commentSubmit: [comment: Comment]` - 评论提交事件
- `userClick: [userId: number]` - 用户点击事件

**暴露方法：**
- `refresh()` - 刷新列表
- `loadMore()` - 加载更多

## 🎯 使用示例

### 基础使用

#### 动态展示
```vue
<template>
  <!-- 单个动态卡片 -->
  <PostCard
    :post="post"
    @like="handleLike"
    @comment="handleComment"
    @user-click="handleUserClick"
  />

  <!-- 动态列表 -->
  <PostList
    list-type="feed"
    @post-click="handlePostClick"
    @user-click="handleUserClick"
  />
</template>

<script setup>
import { PostCard, PostList } from '@/components/content'

const handleLike = (postId) => {
  console.log('点赞动态:', postId)
}

const handleComment = (postId) => {
  console.log('评论动态:', postId)
}

const handlePostClick = (post) => {
  console.log('点击动态:', post)
}

const handleUserClick = (userId) => {
  console.log('点击用户:', userId)
}
</script>
```

#### 评论系统
```vue
<template>
  <!-- 评论列表（包含输入框） -->
  <CommentList
    :post-id="postId"
    @comment-submit="handleCommentSubmit"
    @user-click="handleUserClick"
  />

  <!-- 单独的评论输入框 -->
  <CommentInput
    :post-id="postId"
    :parent-id="parentCommentId"
    @submit="handleCommentSubmit"
    @cancel="handleCancel"
  />

  <!-- 单个评论卡片 -->
  <CommentCard
    :comment="comment"
    :post-id="postId"
    @reply="handleReply"
    @like="handleLike"
    @user-click="handleUserClick"
  />
</template>

<script setup>
import { CommentList, CommentInput, CommentCard } from '@/components/content'

const handleCommentSubmit = (comment) => {
  console.log('评论提交:', comment)
}

const handleReply = (comment) => {
  console.log('回复评论:', comment)
}

const handleLike = (commentId) => {
  console.log('点赞评论:', commentId)
}
</script>
```

### 高级使用
```vue
<template>
  <PostList 
    ref="postListRef"
    list-type="recommended"
    :auto-load="false"
    @post-click="handlePostClick"
  />
  
  <el-button @click="refreshList">刷新</el-button>
</template>

<script setup>
import { ref } from 'vue'

const postListRef = ref()

const refreshList = () => {
  postListRef.value?.refresh()
}
</script>
```

## 🔧 技术实现

### 状态管理
组件使用 Pinia store 进行状态管理：
- `useContentStore()` - 内容相关状态和方法
- `useUserStore()` - 用户相关状态和方法

### API集成
组件通过 content store 调用后端API：

**动态相关：**
- `fetchPosts()` - 获取动态列表
- `fetchFeed()` - 获取个人动态流
- `fetchRecommendedPosts()` - 获取推荐动态
- `likePost()` - 点赞动态

**评论相关：**
- `getComments()` - 获取评论列表
- `createComment()` - 创建评论
- `likeComment()` - 点赞评论
- `deleteComment()` - 删除评论

### 性能优化
- 使用虚拟滚动（计划中）
- 图片懒加载（计划中）
- 组件缓存
- 骨架屏加载状态

## 🧪 测试

组件包含完整的单元测试：
```bash
# 运行测试
npm run test

# 运行测试覆盖率
npm run test:coverage
```

测试文件位置：
- `__tests__/PostCard.test.ts` - PostCard组件测试
- `__tests__/CommentList.test.ts` - CommentList组件测试

## 📱 响应式设计

组件完全支持响应式设计：
- **桌面端**：完整功能展示
- **平板端**：适配中等屏幕
- **移动端**：优化触摸交互，简化布局

## 🎨 样式定制

组件使用Element Plus设计系统：
- 支持主题定制
- 使用CSS变量
- 遵循设计规范

## 🚀 后续计划

### 动态功能
- [ ] 添加动态编辑功能
- [ ] 实现动态删除功能
- [ ] 添加举报功能
- [ ] 添加媒体预览功能
- [ ] 实现虚拟滚动优化
- [ ] 添加图片懒加载
- [ ] 实现动态搜索功能
- [ ] 添加动态筛选功能

### 评论功能
- [x] ✅ 评论展示和发表
- [x] ✅ 评论点赞功能
- [x] ✅ 评论回复功能
- [x] ✅ 评论删除功能
- [ ] 添加评论编辑功能
- [ ] 实现评论举报功能
- [ ] 添加评论表情选择器
- [ ] 实现评论@用户功能
- [ ] 添加评论图片上传
- [ ] 实现评论实时更新
