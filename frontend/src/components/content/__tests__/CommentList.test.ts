/**
 * CommentList 组件测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElButton, ElSelect, ElEmpty } from 'element-plus'
import CommentList from '../CommentList.vue'
import CommentCard from '../CommentCard.vue'
import CommentInput from '../CommentInput.vue'
import type { Comment } from '@/types/content'

// Mock stores
const mockContentStore = {
  getComments: vi.fn(),
  createComment: vi.fn(),
  likeComment: vi.fn(),
  deleteComment: vi.fn()
}

vi.mock('@/stores/content', () => ({
  useContentStore: () => mockContentStore
}))

vi.mock('@/stores/user', () => ({
  useUserStore: () => ({
    user: { id: 1 },
    avatar: 'https://example.com/avatar.jpg',
    displayName: '测试用户'
  })
}))

// Mock date-fns
vi.mock('date-fns', () => ({
  formatDistanceToNow: () => '2小时前'
}))

vi.mock('date-fns/locale', () => ({
  zhCN: {}
}))

const mockComments: Comment[] = [
  {
    id: 1,
    post: 1,
    author: {
      id: 2,
      username: 'user1',
      display_name: '用户1',
      avatar_url: 'https://example.com/avatar1.jpg',
      is_verified: false,
      is_active: true
    },
    content: '这是第一条评论',
    like_count: 5,
    reply_count: 2,
    is_liked: false,
    replies: [
      {
        id: 2,
        post: 1,
        author: {
          id: 3,
          username: 'user2',
          display_name: '用户2',
          avatar_url: 'https://example.com/avatar2.jpg',
          is_verified: true,
          is_active: true
        },
        parent: 1,
        content: '这是一条回复',
        like_count: 1,
        reply_count: 0,
        is_liked: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }
    ],
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z'
  }
]

describe('CommentList', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockContentStore.getComments.mockResolvedValue({
      data: {
        results: mockComments,
        next: null,
        previous: null
      }
    })
  })

  it('应该正确渲染评论列表', async () => {
    const wrapper = mount(CommentList, {
      props: {
        postId: 1
      },
      global: {
        components: {
          ElButton,
          ElSelect,
          ElEmpty,
          CommentCard,
          CommentInput
        }
      }
    })

    // 等待异步加载完成
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))

    // 检查是否显示评论输入框
    expect(wrapper.findComponent(CommentInput).exists()).toBe(true)
    
    // 检查是否显示评论数量
    expect(wrapper.text()).toContain('条评论')
    
    // 检查是否显示排序选择器
    expect(wrapper.findComponent(ElSelect).exists()).toBe(true)
  })

  it('应该正确处理评论提交', async () => {
    const mockComment = {
      id: 3,
      post: 1,
      author: {
        id: 1,
        username: 'currentuser',
        display_name: '当前用户',
        avatar_url: 'https://example.com/avatar.jpg',
        is_verified: false,
        is_active: true
      },
      content: '新评论',
      like_count: 0,
      reply_count: 0,
      is_liked: false,
      created_at: '2023-01-01T01:00:00Z',
      updated_at: '2023-01-01T01:00:00Z'
    }

    mockContentStore.createComment.mockResolvedValue({
      data: mockComment
    })

    const wrapper = mount(CommentList, {
      props: {
        postId: 1
      },
      global: {
        components: {
          ElButton,
          ElSelect,
          ElEmpty,
          CommentCard,
          CommentInput
        }
      }
    })

    // 模拟评论提交
    const commentInput = wrapper.findComponent(CommentInput)
    await commentInput.vm.$emit('submit', mockComment)

    // 检查是否触发了commentSubmit事件
    expect(wrapper.emitted('commentSubmit')).toBeTruthy()
    expect(wrapper.emitted('commentSubmit')?.[0]).toEqual([mockComment])
  })

  it('应该正确处理排序变化', async () => {
    const wrapper = mount(CommentList, {
      props: {
        postId: 1
      },
      global: {
        components: {
          ElButton,
          ElSelect,
          ElEmpty,
          CommentCard,
          CommentInput
        }
      }
    })

    // 等待初始加载
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))

    // 模拟排序变化
    const select = wrapper.findComponent(ElSelect)
    await select.vm.$emit('change', 'popular')

    // 检查是否调用了getComments
    expect(mockContentStore.getComments).toHaveBeenCalledWith(1, {
      page: 1,
      ordering: '-like_count'
    })
  })

  it('应该正确显示空状态', async () => {
    mockContentStore.getComments.mockResolvedValue({
      data: {
        results: [],
        next: null,
        previous: null
      }
    })

    const wrapper = mount(CommentList, {
      props: {
        postId: 1
      },
      global: {
        components: {
          ElButton,
          ElSelect,
          ElEmpty,
          CommentCard,
          CommentInput
        }
      }
    })

    // 等待异步加载完成
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))

    // 检查是否显示空状态
    expect(wrapper.findComponent(ElEmpty).exists()).toBe(true)
    expect(wrapper.text()).toContain('暂无评论')
  })

  it('应该正确处理加载更多', async () => {
    mockContentStore.getComments.mockResolvedValue({
      data: {
        results: mockComments,
        next: 'http://example.com/api/comments/?page=2',
        previous: null
      }
    })

    const wrapper = mount(CommentList, {
      props: {
        postId: 1
      },
      global: {
        components: {
          ElButton,
          ElSelect,
          ElEmpty,
          CommentCard,
          CommentInput
        }
      }
    })

    // 等待初始加载
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))

    // 检查是否显示加载更多按钮
    const loadMoreButton = wrapper.find('.load-more .el-button')
    expect(loadMoreButton.exists()).toBe(true)
    expect(loadMoreButton.text()).toContain('加载更多评论')

    // 模拟点击加载更多
    await loadMoreButton.trigger('click')

    // 检查是否调用了getComments
    expect(mockContentStore.getComments).toHaveBeenCalledTimes(2)
  })
})
