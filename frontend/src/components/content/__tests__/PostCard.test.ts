/**
 * PostCard 组件测试
 */

import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElButton, ElAvatar, ElTag } from 'element-plus'
import PostCard from '../PostCard.vue'
import type { Post } from '@/types/content'

// Mock stores
vi.mock('@/stores/content', () => ({
  useContentStore: () => ({
    likePost: vi.fn()
  })
}))

vi.mock('@/stores/user', () => ({
  useUserStore: () => ({
    user: { id: 1 }
  })
}))

// Mock date-fns
vi.mock('date-fns', () => ({
  formatDistanceToNow: () => '2小时前'
}))

vi.mock('date-fns/locale', () => ({
  zhCN: {}
}))

const mockPost: Post = {
  id: 1,
  author: {
    id: 2,
    username: 'testuser',
    display_name: '测试用户',
    avatar_url: 'https://example.com/avatar.jpg',
    is_verified: true,
    is_active: true
  },
  content: '这是一条测试动态',
  content_type: 'text',
  media_urls: [],
  location: '北京市',
  visibility: 'public',
  status: 'published',
  topics: [
    {
      id: 1,
      name: 'test',
      display_name: '测试',
      description: '测试话题',
      post_count: 10,
      is_active: true,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z'
    }
  ],
  media_files: [],
  like_count: 5,
  comment_count: 3,
  share_count: 1,
  view_count: 100,
  is_pinned: false,
  is_liked: false,
  is_shared: false,
  published_at: '2023-01-01T00:00:00Z',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z'
}

describe('PostCard', () => {
  it('应该正确渲染动态内容', () => {
    const wrapper = mount(PostCard, {
      props: {
        post: mockPost
      },
      global: {
        components: {
          ElButton,
          ElAvatar,
          ElTag
        }
      }
    })

    // 检查用户名是否正确显示
    expect(wrapper.text()).toContain('测试用户')
    
    // 检查动态内容是否正确显示
    expect(wrapper.text()).toContain('这是一条测试动态')
    
    // 检查统计数据是否正确显示
    expect(wrapper.text()).toContain('5') // 点赞数
    expect(wrapper.text()).toContain('3 条评论')
    expect(wrapper.text()).toContain('1 次分享')
  })

  it('应该正确处理点赞事件', async () => {
    const wrapper = mount(PostCard, {
      props: {
        post: mockPost
      },
      global: {
        components: {
          ElButton,
          ElAvatar,
          ElTag
        }
      }
    })

    // 查找点赞按钮并点击
    const likeButton = wrapper.find('.post-actions .el-button')
    await likeButton.trigger('click')

    // 检查是否触发了like事件
    expect(wrapper.emitted('like')).toBeTruthy()
    expect(wrapper.emitted('like')?.[0]).toEqual([1])
  })

  it('应该正确显示话题标签', () => {
    const wrapper = mount(PostCard, {
      props: {
        post: mockPost
      },
      global: {
        components: {
          ElButton,
          ElAvatar,
          ElTag
        }
      }
    })

    // 检查话题标签是否正确显示
    expect(wrapper.text()).toContain('#测试')
  })

  it('应该正确处理用户点击事件', async () => {
    const wrapper = mount(PostCard, {
      props: {
        post: mockPost
      },
      global: {
        components: {
          ElButton,
          ElAvatar,
          ElTag
        }
      }
    })

    // 查找用户信息区域并点击
    const userInfo = wrapper.find('.user-info')
    await userInfo.trigger('click')

    // 检查是否触发了userClick事件
    expect(wrapper.emitted('userClick')).toBeTruthy()
    expect(wrapper.emitted('userClick')?.[0]).toEqual([2])
  })
})
