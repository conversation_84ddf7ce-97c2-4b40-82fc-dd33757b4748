/**
 * 元宇宙社交空间 - 全局组件注册
 * 
 * 这个文件负责注册全局组件
 */

import type { App } from 'vue'

// 导入全局组件
// import AppLoading from './common/AppLoading.vue'
// import AppNotification from './common/AppNotification.vue'

// 内容组件
import PostCard from './content/PostCard.vue'
import PostList from './content/PostList.vue'
import CommentCard from './content/CommentCard.vue'
import CommentList from './content/CommentList.vue'
import CommentInput from './content/CommentInput.vue'

// 用户组件
import UserProfileCard from './user/UserProfileCard.vue'
import UserStatsCard from './user/UserStatsCard.vue'
import AvatarUpload from './user/AvatarUpload.vue'

export function setupGlobalComponents(app: App) {
  // 注册全局组件
  // app.component('AppLoading', AppLoading)
  // app.component('AppNotification', AppNotification)

  // 注册内容组件
  app.component('PostCard', PostCard)
  app.component('PostList', PostList)
  app.component('CommentCard', CommentCard)
  app.component('CommentList', CommentList)
  app.component('CommentInput', CommentInput)

  // 注册用户组件
  app.component('UserProfileCard', UserProfileCard)
  app.component('UserStatsCard', UserStatsCard)
  app.component('AvatarUpload', AvatarUpload)

  console.log('✅ 全局组件注册完成')
}

// 导出组件
export {
  PostCard,
  PostList,
  CommentCard,
  CommentList,
  CommentInput,
  UserProfileCard,
  UserStatsCard,
  AvatarUpload
}
