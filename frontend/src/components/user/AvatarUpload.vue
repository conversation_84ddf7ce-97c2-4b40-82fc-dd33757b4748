<script setup lang="ts">
/**
 * 元宇宙社交空间 - 头像上传组件
 * 
 * 用于上传和编辑用户头像的组件，包含：
 * - 头像预览
 * - 文件选择和上传
 * - 图片裁剪（可选）
 * - 上传进度显示
 */

import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { UploadProps, UploadUserFile } from 'element-plus'

interface Props {
  currentAvatar?: string
  size?: number
  disabled?: boolean
  showCrop?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 120,
  disabled: false,
  showCrop: false
})

const emit = defineEmits<{
  upload: [file: File]
  change: [url: string]
  remove: []
}>()

// 状态管理
const uploading = ref(false)
const uploadProgress = ref(0)
const previewUrl = ref<string>('')
const fileList = ref<UploadUserFile[]>([])

// 计算属性
const avatarUrl = computed(() => {
  return previewUrl.value || props.currentAvatar || ''
})

const uploadDisabled = computed(() => {
  return props.disabled || uploading.value
})

// 文件上传前的检查
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  // 检查文件类型
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  
  // 检查文件大小 (5MB)
  const isLt5M = file.size / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  
  return true
}

// 文件选择变化
const handleChange: UploadProps['onChange'] = (uploadFile) => {
  if (uploadFile.raw) {
    // 创建预览URL
    const url = URL.createObjectURL(uploadFile.raw)
    previewUrl.value = url
    
    // 触发上传
    handleUpload(uploadFile.raw)
  }
}

// 处理上传
const handleUpload = async (file: File) => {
  try {
    uploading.value = true
    uploadProgress.value = 0
    
    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10
      }
    }, 100)
    
    // 触发上传事件
    emit('upload', file)
    
    // 清除进度定时器
    clearInterval(progressInterval)
    uploadProgress.value = 100
    
    // 延迟一下显示完成状态
    setTimeout(() => {
      uploading.value = false
      uploadProgress.value = 0
      ElMessage.success('头像上传成功')
      
      // 触发变化事件
      if (previewUrl.value) {
        emit('change', previewUrl.value)
      }
    }, 500)
    
  } catch (error) {
    console.error('头像上传失败:', error)
    ElMessage.error('头像上传失败')
    
    // 重置状态
    uploading.value = false
    uploadProgress.value = 0
    previewUrl.value = ''
  }
}

// 移除头像
const handleRemove = async () => {
  try {
    await ElMessageBox.confirm('确定要移除头像吗？', '确认操作', {
      type: 'warning'
    })
    
    previewUrl.value = ''
    fileList.value = []
    emit('remove')
    
    ElMessage.success('头像已移除')
  } catch (error) {
    // 用户取消操作
  }
}

// 处理上传错误
const handleError = () => {
  uploading.value = false
  uploadProgress.value = 0
  previewUrl.value = ''
  ElMessage.error('头像上传失败')
}

// 清理预览URL
const cleanup = () => {
  if (previewUrl.value && previewUrl.value.startsWith('blob:')) {
    URL.revokeObjectURL(previewUrl.value)
  }
}

// 组件卸载时清理
onUnmounted(() => {
  cleanup()
})
</script>

<template>
  <div class="avatar-upload">
    <div class="upload-container">
      <!-- 头像预览 -->
      <div class="avatar-preview" :style="{ width: size + 'px', height: size + 'px' }">
        <el-avatar 
          :src="avatarUrl"
          :size="size"
          class="avatar-image"
        >
          <el-icon size="32"><User /></el-icon>
        </el-avatar>
        
        <!-- 上传进度 -->
        <div v-if="uploading" class="upload-progress">
          <el-progress 
            type="circle" 
            :percentage="uploadProgress"
            :width="size - 20"
            :stroke-width="4"
          />
        </div>
        
        <!-- 操作遮罩 -->
        <div v-if="!uploading && !disabled" class="avatar-overlay">
          <div class="overlay-content">
            <el-icon size="24"><Camera /></el-icon>
            <span>更换头像</span>
          </div>
        </div>
      </div>
      
      <!-- 文件上传 -->
      <el-upload
        v-model:file-list="fileList"
        class="avatar-uploader"
        :show-file-list="false"
        :before-upload="beforeUpload"
        :on-change="handleChange"
        :on-error="handleError"
        :disabled="uploadDisabled"
        accept="image/*"
      >
        <div class="upload-trigger" :style="{ width: size + 'px', height: size + 'px' }">
          <!-- 透明的触发区域 -->
        </div>
      </el-upload>
    </div>
    
    <!-- 操作按钮 -->
    <div class="upload-actions">
      <el-button 
        v-if="avatarUrl && !disabled"
        type="danger" 
        size="small" 
        text
        @click="handleRemove"
      >
        <el-icon><Delete /></el-icon>
        移除头像
      </el-button>
      
      <div class="upload-tips">
        <p>支持 JPG、PNG 格式</p>
        <p>文件大小不超过 5MB</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.avatar-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.upload-container {
  position: relative;
}

.avatar-preview {
  position: relative;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid var(--el-border-color-lighter);
  transition: border-color 0.3s ease;
}

.avatar-preview:hover {
  border-color: var(--el-color-primary);
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.upload-progress {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
  border-radius: 50%;
}

.upload-container:hover .avatar-overlay {
  opacity: 1;
}

.overlay-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: white;
  font-size: 0.75rem;
  text-align: center;
}

.avatar-uploader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.upload-trigger {
  position: absolute;
  top: 0;
  left: 0;
  cursor: pointer;
  border-radius: 50%;
}

.upload-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.upload-tips {
  text-align: center;
  color: var(--el-text-color-secondary);
  font-size: 0.75rem;
  line-height: 1.4;
}

.upload-tips p {
  margin: 0;
}

/* 禁用状态 */
.avatar-upload :deep(.el-upload.is-disabled) {
  cursor: not-allowed;
}

.avatar-upload :deep(.el-upload.is-disabled) .upload-trigger {
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .avatar-upload {
    gap: 12px;
  }
  
  .upload-tips {
    font-size: 0.7rem;
  }
}
</style>
