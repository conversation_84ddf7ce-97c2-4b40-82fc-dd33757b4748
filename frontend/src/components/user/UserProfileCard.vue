<script setup lang="ts">
/**
 * 元宇宙社交空间 - 用户资料卡片组件
 * 
 * 用于展示用户基本资料信息的卡片组件，包含：
 * - 用户头像和封面图
 * - 基本信息展示
 * - 统计数据展示
 * - 操作按钮（编辑、关注等）
 */

import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

import { useUserStore } from '@/stores/user'
import type { User } from '@/types/user'

interface Props {
  user: User
  showActions?: boolean
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showActions: true,
  compact: false
})

const emit = defineEmits<{
  edit: []
  follow: [userId: number]
  unfollow: [userId: number]
  message: [userId: number]
}>()

const router = useRouter()
const userStore = useUserStore()

// 状态管理
const following = ref(false)

// 计算属性
const isCurrentUser = computed(() => {
  return userStore.currentUser?.id === props.user.id
})

const joinedTime = computed(() => {
  if (!props.user.createdAt) return ''
  const date = new Date(props.user.createdAt)
  return formatDistanceToNow(date, { 
    addSuffix: true, 
    locale: zhCN 
  })
})

const displayName = computed(() => {
  return props.user.displayName || 
         `${props.user.firstName} ${props.user.lastName}`.trim() ||
         props.user.username
})

const fullName = computed(() => {
  const name = `${props.user.firstName} ${props.user.lastName}`.trim()
  return name || props.user.username
})

const userAge = computed(() => {
  if (!props.user.birthDate) return null
  const today = new Date()
  const birthDate = new Date(props.user.birthDate)
  let age = today.getFullYear() - birthDate.getFullYear()
  const monthDiff = today.getMonth() - birthDate.getMonth()
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--
  }
  
  return age
})

const genderText = computed(() => {
  const genderMap = {
    'male': '男',
    'female': '女',
    'other': '其他',
    'prefer_not_to_say': '不愿透露'
  }
  return props.user.gender ? genderMap[props.user.gender] : null
})

// 处理编辑
const handleEdit = () => {
  emit('edit')
}

// 处理关注
const handleFollow = async () => {
  if (following.value) return
  
  try {
    following.value = true
    // TODO: 实现关注/取消关注逻辑
    emit('follow', props.user.id)
    ElMessage.success('关注成功')
  } catch (error) {
    console.error('关注失败:', error)
    ElMessage.error('关注失败')
  } finally {
    following.value = false
  }
}

// 处理发消息
const handleMessage = () => {
  emit('message', props.user.id)
}

// 处理头像点击
const handleAvatarClick = () => {
  // TODO: 实现头像预览功能
  console.log('预览头像')
}

// 处理封面图点击
const handleCoverClick = () => {
  // TODO: 实现封面图预览功能
  console.log('预览封面图')
}
</script>

<template>
  <div class="user-profile-card" :class="{ 'is-compact': compact }">
    <!-- 封面图 -->
    <div class="cover-section" @click="handleCoverClick">
      <div 
        class="cover-image"
        :style="{ 
          backgroundImage: user.coverUrl ? `url(${user.coverUrl})` : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }"
      >
        <div class="cover-overlay"></div>
      </div>
    </div>
    
    <!-- 用户信息 -->
    <div class="profile-section">
      <!-- 头像 -->
      <div class="avatar-container">
        <el-avatar 
          :src="user.avatarUrl"
          :size="compact ? 80 : 120"
          class="user-avatar"
          @click="handleAvatarClick"
        >
          {{ displayName.charAt(0) }}
        </el-avatar>
        
        <!-- 在线状态指示器 -->
        <div class="online-indicator" v-if="!compact">
          <el-badge is-dot type="success" />
        </div>
      </div>
      
      <!-- 基本信息 -->
      <div class="user-info">
        <div class="user-header">
          <h2 class="user-name">
            {{ displayName }}
            <el-tag v-if="user.isVerified" type="success" size="small">
              <el-icon><Select /></el-icon>
              已认证
            </el-tag>
          </h2>
          
          <div class="user-meta">
            <span class="username">@{{ user.username }}</span>
            <span v-if="!compact && joinedTime" class="join-time">
              <el-icon><Calendar /></el-icon>
              {{ joinedTime }}加入
            </span>
          </div>
        </div>
        
        <!-- 个人简介 -->
        <div v-if="user.bio" class="user-bio">
          {{ user.bio }}
        </div>
        
        <!-- 详细信息 -->
        <div v-if="!compact" class="user-details">
          <div v-if="fullName !== user.username" class="detail-item">
            <el-icon><User /></el-icon>
            <span>{{ fullName }}</span>
          </div>
          
          <div v-if="userAge" class="detail-item">
            <el-icon><Calendar /></el-icon>
            <span>{{ userAge }}岁</span>
          </div>
          
          <div v-if="genderText" class="detail-item">
            <el-icon><Avatar /></el-icon>
            <span>{{ genderText }}</span>
          </div>
          
          <div v-if="user.location" class="detail-item">
            <el-icon><Location /></el-icon>
            <span>{{ user.location }}</span>
          </div>
          
          <div v-if="user.website" class="detail-item">
            <el-icon><Link /></el-icon>
            <a :href="user.website" target="_blank" class="website-link">
              {{ user.website }}
            </a>
          </div>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div v-if="showActions" class="action-buttons">
        <template v-if="isCurrentUser">
          <el-button type="primary" @click="handleEdit">
            <el-icon><Edit /></el-icon>
            编辑资料
          </el-button>
        </template>
        
        <template v-else>
          <el-button 
            type="primary"
            :loading="following"
            @click="handleFollow"
          >
            <el-icon><Plus /></el-icon>
            关注
          </el-button>
          
          <el-button @click="handleMessage">
            <el-icon><ChatDotRound /></el-icon>
            发消息
          </el-button>
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped>
.user-profile-card {
  background: white;
  border-radius: 16px;
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.user-profile-card:hover {
  box-shadow: var(--el-box-shadow);
}

.user-profile-card.is-compact {
  border-radius: 12px;
}

.cover-section {
  position: relative;
  height: 200px;
  cursor: pointer;
  overflow: hidden;
}

.user-profile-card.is-compact .cover-section {
  height: 120px;
}

.cover-image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transition: transform 0.3s ease;
}

.cover-section:hover .cover-image {
  transform: scale(1.05);
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
}

.profile-section {
  position: relative;
  padding: 0 24px 24px;
}

.user-profile-card.is-compact .profile-section {
  padding: 0 16px 16px;
}

.avatar-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-top: -60px;
  margin-bottom: 16px;
}

.user-profile-card.is-compact .avatar-container {
  margin-top: -40px;
  margin-bottom: 12px;
}

.user-avatar {
  border: 4px solid white;
  box-shadow: var(--el-box-shadow);
  cursor: pointer;
  transition: transform 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
}

.online-indicator {
  position: absolute;
  bottom: 8px;
  right: 8px;
}

.user-info {
  text-align: center;
}

.user-header {
  margin-bottom: 16px;
}

.user-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--el-text-color-primary);
  margin: 0 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.user-profile-card.is-compact .user-name {
  font-size: 1.25rem;
  margin-bottom: 4px;
}

.user-meta {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  color: var(--el-text-color-secondary);
  font-size: 0.875rem;
}

.username {
  font-weight: 500;
}

.join-time {
  display: flex;
  align-items: center;
  gap: 4px;
}

.user-bio {
  color: var(--el-text-color-regular);
  line-height: 1.6;
  margin-bottom: 16px;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.user-details {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 16px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--el-text-color-regular);
  font-size: 0.875rem;
}

.website-link {
  color: var(--el-color-primary);
  text-decoration: none;
  transition: color 0.3s ease;
}

.website-link:hover {
  color: var(--el-color-primary-dark-2);
  text-decoration: underline;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cover-section {
    height: 150px;
  }
  
  .user-profile-card.is-compact .cover-section {
    height: 100px;
  }
  
  .profile-section {
    padding: 0 16px 16px;
  }
  
  .user-name {
    font-size: 1.25rem;
    flex-direction: column;
    gap: 4px;
  }
  
  .user-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .user-details {
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
</style>
