<script setup lang="ts">
/**
 * 元宇宙社交空间 - 用户统计卡片组件
 * 
 * 用于展示用户统计信息的卡片组件，包含：
 * - 动态数量
 * - 关注数量
 * - 粉丝数量
 * - 获赞数量
 */

import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

import { userApi } from '@/api/user'
import type { UserStats } from '@/types/user'

interface Props {
  userId: number
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  compact: false
})

const emit = defineEmits<{
  followingClick: []
  followersClick: []
  postsClick: []
}>()

// 状态管理
const stats = ref<UserStats | null>(null)
const loading = ref(false)

// 计算属性
const statsItems = computed(() => [
  {
    key: 'posts',
    label: '动态',
    value: stats.value?.postsCount || 0,
    icon: 'DocumentCopy',
    color: '#409eff',
    clickable: true
  },
  {
    key: 'following',
    label: '关注',
    value: stats.value?.followingCount || 0,
    icon: 'User',
    color: '#67c23a',
    clickable: true
  },
  {
    key: 'followers',
    label: '粉丝',
    value: stats.value?.followersCount || 0,
    icon: 'UserFilled',
    color: '#e6a23c',
    clickable: true
  },
  {
    key: 'likes',
    label: '获赞',
    value: stats.value?.likesCount || 0,
    icon: 'Like',
    color: '#f56c6c',
    clickable: false
  }
])

// 格式化数字
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 获取用户统计
const fetchUserStats = async () => {
  try {
    loading.value = true
    const response = await userApi.getUserStats(props.userId)
    stats.value = response.data
  } catch (error) {
    console.error('获取用户统计失败:', error)
    ElMessage.error('获取统计信息失败')
  } finally {
    loading.value = false
  }
}

// 处理统计项点击
const handleStatsClick = (key: string) => {
  switch (key) {
    case 'posts':
      emit('postsClick')
      break
    case 'following':
      emit('followingClick')
      break
    case 'followers':
      emit('followersClick')
      break
  }
}

// 生命周期
onMounted(() => {
  fetchUserStats()
})

// 暴露方法
defineExpose({
  refresh: fetchUserStats
})
</script>

<template>
  <div class="user-stats-card" :class="{ 'is-compact': compact }">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <el-skeleton animated>
        <template #template>
          <div class="stats-skeleton">
            <div v-for="i in 4" :key="i" class="stat-skeleton">
              <el-skeleton-item variant="circle" style="width: 40px; height: 40px;" />
              <div class="stat-text">
                <el-skeleton-item variant="text" style="width: 60px;" />
                <el-skeleton-item variant="text" style="width: 40px;" />
              </div>
            </div>
          </div>
        </template>
      </el-skeleton>
    </div>
    
    <!-- 统计内容 -->
    <div v-else class="stats-content">
      <div
        v-for="item in statsItems"
        :key="item.key"
        class="stat-item"
        :class="{ 'is-clickable': item.clickable }"
        @click="item.clickable ? handleStatsClick(item.key) : null"
      >
        <div class="stat-icon" :style="{ backgroundColor: item.color }">
          <el-icon>
            <component :is="item.icon" />
          </el-icon>
        </div>
        
        <div class="stat-content">
          <div class="stat-value">
            {{ formatNumber(item.value) }}
          </div>
          <div class="stat-label">
            {{ item.label }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.user-stats-card {
  background: white;
  border-radius: 12px;
  box-shadow: var(--el-box-shadow-light);
  padding: 20px;
  transition: box-shadow 0.3s ease;
}

.user-stats-card:hover {
  box-shadow: var(--el-box-shadow);
}

.user-stats-card.is-compact {
  padding: 16px;
}

.loading-state {
  min-height: 120px;
}

.stats-skeleton {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.stat-skeleton {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stats-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
}

.user-stats-card.is-compact .stats-content {
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-item.is-clickable {
  cursor: pointer;
}

.stat-item.is-clickable:hover {
  background-color: var(--el-fill-color-light);
  transform: translateY(-2px);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  flex-shrink: 0;
}

.user-stats-card.is-compact .stat-icon {
  width: 32px;
  height: 32px;
  font-size: 16px;
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--el-text-color-primary);
  line-height: 1.2;
}

.user-stats-card.is-compact .stat-value {
  font-size: 1.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--el-text-color-secondary);
  margin-top: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-content {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .stat-item {
    flex-direction: column;
    text-align: center;
    gap: 8px;
    padding: 16px 8px;
  }
  
  .stat-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .stat-value {
    font-size: 1.25rem;
  }
  
  .stat-label {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .user-stats-card {
    padding: 16px 12px;
  }
  
  .stats-content {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
  
  .stat-item {
    padding: 12px 6px;
  }
  
  .stat-icon {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  
  .stat-value {
    font-size: 1.125rem;
  }
}
</style>
