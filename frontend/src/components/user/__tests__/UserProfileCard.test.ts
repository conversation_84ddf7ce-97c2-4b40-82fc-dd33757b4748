/**
 * UserProfileCard 组件测试
 */

import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElButton, ElAvatar, ElTag } from 'element-plus'
import UserProfileCard from '../UserProfileCard.vue'
import type { User } from '@/types/user'

// Mock stores
vi.mock('@/stores/user', () => ({
  useUserStore: () => ({
    currentUser: { id: 1 }
  })
}))

// Mock date-fns
vi.mock('date-fns', () => ({
  formatDistanceToNow: () => '2个月前'
}))

vi.mock('date-fns/locale', () => ({
  zhCN: {}
}))

const mockUser: User = {
  id: 2,
  username: 'testuser',
  email: '<EMAIL>',
  firstName: '测试',
  lastName: '用户',
  displayName: '测试用户',
  bio: '这是一个测试用户的个人简介',
  avatarUrl: 'https://example.com/avatar.jpg',
  coverUrl: 'https://example.com/cover.jpg',
  birthDate: '1990-01-01',
  gender: 'male',
  location: '北京市',
  website: 'https://example.com',
  isActive: true,
  isVerified: true,
  lastLoginAt: '2023-01-01T00:00:00Z',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z'
}

describe('UserProfileCard', () => {
  it('应该正确渲染用户基本信息', () => {
    const wrapper = mount(UserProfileCard, {
      props: {
        user: mockUser
      },
      global: {
        components: {
          ElButton,
          ElAvatar,
          ElTag
        }
      }
    })

    // 检查用户名是否正确显示
    expect(wrapper.text()).toContain('测试用户')
    
    // 检查用户名是否正确显示
    expect(wrapper.text()).toContain('@testuser')
    
    // 检查个人简介是否正确显示
    expect(wrapper.text()).toContain('这是一个测试用户的个人简介')
    
    // 检查认证标识是否显示
    expect(wrapper.text()).toContain('已认证')
  })

  it('应该正确显示用户详细信息', () => {
    const wrapper = mount(UserProfileCard, {
      props: {
        user: mockUser
      },
      global: {
        components: {
          ElButton,
          ElAvatar,
          ElTag
        }
      }
    })

    // 检查位置信息
    expect(wrapper.text()).toContain('北京市')
    
    // 检查网站信息
    expect(wrapper.text()).toContain('https://example.com')
    
    // 检查加入时间
    expect(wrapper.text()).toContain('2个月前加入')
  })

  it('应该为当前用户显示编辑按钮', () => {
    const currentUserMock = { ...mockUser, id: 1 }
    
    const wrapper = mount(UserProfileCard, {
      props: {
        user: currentUserMock
      },
      global: {
        components: {
          ElButton,
          ElAvatar,
          ElTag
        }
      }
    })

    // 检查是否显示编辑按钮
    expect(wrapper.text()).toContain('编辑资料')
  })

  it('应该为其他用户显示关注和发消息按钮', () => {
    const wrapper = mount(UserProfileCard, {
      props: {
        user: mockUser
      },
      global: {
        components: {
          ElButton,
          ElAvatar,
          ElTag
        }
      }
    })

    // 检查是否显示关注按钮
    expect(wrapper.text()).toContain('关注')
    
    // 检查是否显示发消息按钮
    expect(wrapper.text()).toContain('发消息')
  })

  it('应该正确处理编辑事件', async () => {
    const currentUserMock = { ...mockUser, id: 1 }
    
    const wrapper = mount(UserProfileCard, {
      props: {
        user: currentUserMock
      },
      global: {
        components: {
          ElButton,
          ElAvatar,
          ElTag
        }
      }
    })

    // 查找编辑按钮并点击
    const editButton = wrapper.find('.el-button')
    await editButton.trigger('click')

    // 检查是否触发了edit事件
    expect(wrapper.emitted('edit')).toBeTruthy()
  })

  it('应该在紧凑模式下正确渲染', () => {
    const wrapper = mount(UserProfileCard, {
      props: {
        user: mockUser,
        compact: true
      },
      global: {
        components: {
          ElButton,
          ElAvatar,
          ElTag
        }
      }
    })

    // 检查是否应用了紧凑模式样式
    expect(wrapper.classes()).toContain('is-compact')
    
    // 检查基本信息仍然显示
    expect(wrapper.text()).toContain('测试用户')
  })

  it('应该正确计算用户年龄', () => {
    const userWithBirthDate = {
      ...mockUser,
      birthDate: '1990-01-01'
    }
    
    const wrapper = mount(UserProfileCard, {
      props: {
        user: userWithBirthDate
      },
      global: {
        components: {
          ElButton,
          ElAvatar,
          ElTag
        }
      }
    })

    // 检查是否显示年龄（具体年龄会根据当前日期计算）
    expect(wrapper.text()).toMatch(/\d+岁/)
  })

  it('应该正确显示性别信息', () => {
    const wrapper = mount(UserProfileCard, {
      props: {
        user: mockUser
      },
      global: {
        components: {
          ElButton,
          ElAvatar,
          ElTag
        }
      }
    })

    // 检查性别显示
    expect(wrapper.text()).toContain('男')
  })
})
