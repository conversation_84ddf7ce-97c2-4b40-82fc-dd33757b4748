<script setup lang="ts">
/**
 * 元宇宙社交空间 - 认证布局组件
 * 
 * 用于登录、注册等认证相关页面的布局
 */

import { onMounted } from 'vue'
import { useThemeStore } from '@/stores/theme'

const themeStore = useThemeStore()

// 组件挂载时初始化主题
onMounted(() => {
  themeStore.initializeTheme()
})
</script>

<template>
  <div class="auth-layout">
    <!-- 认证页面内容 -->
    <main class="auth-main">
      <RouterView />
    </main>
    
    <!-- 背景装饰 -->
    <div class="auth-background">
      <div class="bg-shape bg-shape-1"></div>
      <div class="bg-shape bg-shape-2"></div>
      <div class="bg-shape bg-shape-3"></div>
    </div>
  </div>
</template>

<style scoped>
.auth-layout {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
}

.auth-main {
  position: relative;
  z-index: 10;
  min-height: 100vh;
}

.auth-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.bg-shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  animation: float 6s ease-in-out infinite;
}

.bg-shape-1 {
  width: 300px;
  height: 300px;
  top: 10%;
  left: -10%;
  animation-delay: 0s;
}

.bg-shape-2 {
  width: 200px;
  height: 200px;
  top: 60%;
  right: -5%;
  animation-delay: 2s;
}

.bg-shape-3 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 暗色模式适配 */
.dark .bg-shape {
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bg-shape-1 {
    width: 200px;
    height: 200px;
  }
  
  .bg-shape-2 {
    width: 150px;
    height: 150px;
  }
  
  .bg-shape-3 {
    width: 100px;
    height: 100px;
  }
}

/* 减少动画（无障碍支持） */
@media (prefers-reduced-motion: reduce) {
  .bg-shape {
    animation: none;
  }
}
</style>
