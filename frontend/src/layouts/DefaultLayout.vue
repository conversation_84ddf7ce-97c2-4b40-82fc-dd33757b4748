<script setup lang="ts">
/**
 * 元宇宙社交空间 - 默认布局组件
 * 
 * 用于主要应用页面的布局，包含导航栏、侧边栏等
 */

import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { useThemeStore } from '@/stores/theme'

const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()
const themeStore = useThemeStore()

// 侧边栏折叠状态
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed)

// 用户菜单显示状态
const userMenuVisible = ref(false)

// 切换侧边栏
const toggleSidebar = () => {
  appStore.toggleSidebar()
}

// 切换主题
const toggleTheme = () => {
  themeStore.toggleTheme()
}

// 处理用户登出
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await userStore.logout()
    ElMessage.success('已退出登录')
    await router.push('/auth/login')
    
  } catch (error) {
    // 用户取消登出
  }
}

// 跳转到用户资料页面
const goToProfile = () => {
  router.push('/user/profile')
  userMenuVisible.value = false
}

// 跳转到设置页面
const goToSettings = () => {
  router.push('/user/settings')
  userMenuVisible.value = false
}
</script>

<template>
  <div class="default-layout">
    <!-- 顶部导航栏 -->
    <header class="layout-header">
      <div class="header-left">
        <el-button
          type="primary"
          :icon="sidebarCollapsed ? 'Expand' : 'Fold'"
          circle
          @click="toggleSidebar"
        />
        <h1 class="app-title">元宇宙社交空间</h1>
      </div>
      
      <div class="header-right">
        <!-- 主题切换 -->
        <el-button
          :icon="themeStore.isDark ? 'Sunny' : 'Moon'"
          circle
          @click="toggleTheme"
        />
        
        <!-- 通知 -->
        <el-badge :value="5" class="notification-badge">
          <el-button icon="Bell" circle />
        </el-badge>
        
        <!-- 用户菜单 -->
        <el-dropdown
          v-model:visible="userMenuVisible"
          trigger="click"
          placement="bottom-end"
        >
          <div class="user-avatar">
            <el-avatar
              :src="userStore.avatar"
              :size="36"
            >
              {{ userStore.displayName?.charAt(0) || 'U' }}
            </el-avatar>
          </div>
          
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="goToProfile">
                <el-icon><User /></el-icon>
                个人资料
              </el-dropdown-item>
              <el-dropdown-item @click="goToSettings">
                <el-icon><Setting /></el-icon>
                账户设置
              </el-dropdown-item>
              <el-dropdown-item divided @click="handleLogout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>
    
    <!-- 主要内容区域 -->
    <div class="layout-body">
      <!-- 侧边栏 -->
      <aside 
        class="layout-sidebar"
        :class="{ collapsed: sidebarCollapsed }"
      >
        <nav class="sidebar-nav">
          <el-menu
            :default-active="$route.path"
            :collapse="sidebarCollapsed"
            router
          >
            <el-menu-item index="/home">
              <el-icon><House /></el-icon>
              <span>首页</span>
            </el-menu-item>
            
            <el-menu-item index="/content/explore">
              <el-icon><Compass /></el-icon>
              <span>发现</span>
            </el-menu-item>
            
            <el-menu-item index="/social/friends">
              <el-icon><UserFilled /></el-icon>
              <span>好友</span>
            </el-menu-item>
            
            <el-menu-item index="/social/messages">
              <el-icon><ChatDotRound /></el-icon>
              <span>消息</span>
            </el-menu-item>
            
            <el-menu-item index="/content/create">
              <el-icon><Plus /></el-icon>
              <span>创建</span>
            </el-menu-item>
          </el-menu>
        </nav>
      </aside>
      
      <!-- 主内容区 -->
      <main class="layout-main">
        <div class="main-content">
          <RouterView />
        </div>
      </main>
    </div>
  </div>
</template>

<style scoped>
.default-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--el-bg-color-page);
}

.layout-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  padding: 0 20px;
  background-color: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.app-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.notification-badge {
  cursor: pointer;
}

.user-avatar {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
}

.layout-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.layout-sidebar {
  width: 200px;
  background-color: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  transition: width 0.3s ease;
  overflow: hidden;
}

.layout-sidebar.collapsed {
  width: 64px;
}

.sidebar-nav {
  height: 100%;
  padding: 20px 0;
}

.layout-main {
  flex: 1;
  overflow: auto;
}

.main-content {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-header {
    padding: 0 16px;
  }
  
  .header-left .app-title {
    display: none;
  }
  
  .layout-sidebar {
    position: fixed;
    top: 60px;
    left: 0;
    height: calc(100vh - 60px);
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .layout-sidebar:not(.collapsed) {
    transform: translateX(0);
  }
  
  .main-content {
    padding: 16px;
  }
}

/* 暗色模式适配 */
.dark .layout-header {
  background-color: var(--el-bg-color);
  border-bottom-color: var(--el-border-color);
}

.dark .layout-sidebar {
  background-color: var(--el-bg-color);
  border-right-color: var(--el-border-color);
}
</style>
