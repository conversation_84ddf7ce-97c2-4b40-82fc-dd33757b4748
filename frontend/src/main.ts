/**
 * 元宇宙社交空间 - 应用入口文件
 * 
 * 这个文件是Vue应用的入口点，负责：
 * - 创建Vue应用实例
 * - 注册全局插件和组件
 * - 配置路由和状态管理
 * - 挂载应用到DOM
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// 导入样式文件
import './assets/styles/main.css'

// 导入Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 导入国际化
import { createI18n } from 'vue-i18n'
import zhCN from 'element-plus/es/locale/lang/zh-cn'
import enUS from 'element-plus/es/locale/lang/en'

// 导入进度条
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 导入全局组件和指令
import { setupGlobalComponents } from './components'
import { setupGlobalDirectives } from './directives'

// 导入工具函数
import { setupErrorHandler } from './utils/errorHandler'
import { setupPerformanceMonitor } from './utils/performance'

// 创建Vue应用实例
const app = createApp(App)

// 创建Pinia状态管理
const pinia = createPinia()

// 创建国际化实例
const i18n = createI18n({
  legacy: false,
  locale: 'zh-CN',
  fallbackLocale: 'en-US',
  messages: {
    'zh-CN': {
      // 中文翻译
      app: {
        title: '元宇宙社交空间',
        loading: '加载中...',
        error: '出错了',
        retry: '重试',
      },
    },
    'en-US': {
      // 英文翻译
      app: {
        title: 'Metaverse Social Space',
        loading: 'Loading...',
        error: 'Something went wrong',
        retry: 'Retry',
      },
    },
  },
})

// 配置NProgress
NProgress.configure({
  showSpinner: false,
  trickleSpeed: 200,
  minimum: 0.3,
})

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册插件
app.use(pinia)
app.use(router)
app.use(i18n)
app.use(ElementPlus, {
  locale: zhCN,
})

// 设置全局组件
setupGlobalComponents(app)

// 设置全局指令
setupGlobalDirectives(app)

// 设置错误处理
setupErrorHandler(app)

// 设置性能监控
if (import.meta.env.VITE_ENABLE_PERFORMANCE_MONITOR === 'true') {
  setupPerformanceMonitor()
}

// 全局属性配置
app.config.globalProperties.$ELEMENT = {
  size: 'default',
  zIndex: 3000,
}

// 开发环境配置
if (import.meta.env.DEV) {
  app.config.performance = true
  
  // 开发工具
  if (import.meta.env.VITE_SHOW_DEV_TOOLS === 'true') {
    console.log('🚀 元宇宙社交空间开发环境')
    console.log('📦 Vue版本:', app.version)
    console.log('🔧 环境变量:', import.meta.env)
  }
}

// 生产环境配置
if (import.meta.env.PROD) {
  // 移除console.log
  console.log = () => {}
  console.warn = () => {}
  
  // 错误监控
  if (import.meta.env.VITE_SENTRY_DSN) {
    import('./utils/sentry').then(({ setupSentry }) => {
      setupSentry()
    })
  }
}

// 挂载应用
app.mount('#app')

// 导出应用实例（用于测试）
export default app
