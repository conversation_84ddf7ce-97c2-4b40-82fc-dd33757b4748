/**
 * 元宇宙社交空间 - 路由配置
 * 
 * 这个文件配置了应用的路由系统，包括：
 * - 路由定义
 * - 路由守卫
 * - 懒加载配置
 * - 权限控制
 */

import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import NProgress from 'nprogress'

// 导入stores
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'

// 导入布局组件
const DefaultLayout = () => import('@/layouts/DefaultLayout.vue')
const AuthLayout = () => import('@/layouts/AuthLayout.vue')
const EmptyLayout = () => import('@/layouts/EmptyLayout.vue')

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Root',
    redirect: '/home',
  },
  
  // 主要页面路由
  {
    path: '/home',
    component: DefaultLayout,
    children: [
      {
        path: '',
        name: 'Home',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '首页',
          requiresAuth: false,
          keepAlive: true,
        },
      },
    ],
  },
  
  // 用户相关路由
  {
    path: '/user',
    component: DefaultLayout,
    meta: {
      requiresAuth: true,
    },
    children: [
      {
        path: 'profile',
        name: 'UserProfile',
        component: () => import('@/views/user/ProfilePage.vue'),
        meta: {
          title: '个人资料',
          keepAlive: true,
        },
      },
      {
        path: 'settings',
        name: 'UserSettings',
        component: () => import('@/views/user/SettingsPage.vue'),
        meta: {
          title: '账户设置',
        },
      },
      {
        path: ':id',
        name: 'UserDetail',
        component: () => import('@/views/user/UserDetailPage.vue'),
        meta: {
          title: '用户详情',
        },
        props: true,
      },
    ],
  },
  
  // 社交功能路由
  {
    path: '/social',
    component: DefaultLayout,
    meta: {
      requiresAuth: true,
    },
    children: [
      {
        path: 'friends',
        name: 'Friends',
        component: () => import('@/views/social/FriendsPage.vue'),
        meta: {
          title: '好友',
          keepAlive: true,
        },
      },
      {
        path: 'messages',
        name: 'Messages',
        component: () => import('@/views/social/MessagesPage.vue'),
        meta: {
          title: '消息',
          keepAlive: true,
        },
      },
      {
        path: 'notifications',
        name: 'Notifications',
        component: () => import('@/views/social/NotificationsPage.vue'),
        meta: {
          title: '通知',
        },
      },
    ],
  },
  
  // 内容相关路由
  {
    path: '/content',
    component: DefaultLayout,
    children: [
      {
        path: 'explore',
        name: 'Explore',
        component: () => import('@/views/content/ExplorePage.vue'),
        meta: {
          title: '发现',
          keepAlive: true,
        },
      },
      {
        path: 'create',
        name: 'CreateContent',
        component: () => import('@/views/content/CreatePostPage.vue'),
        meta: {
          title: '创建内容',
          requiresAuth: true,
        },
      },
      {
        path: 'post/:id',
        name: 'PostDetail',
        component: () => import('@/views/content/PostDetailPage.vue'),
        meta: {
          title: '动态详情',
        },
        props: true,
      },
    ],
  },
  
  // 认证相关路由
  {
    path: '/auth',
    component: AuthLayout,
    meta: {
      requiresGuest: true,
    },
    children: [
      {
        path: 'login',
        name: 'Login',
        component: () => import('@/views/auth/LoginPage.vue'),
        meta: {
          title: '登录',
        },
      },
      {
        path: 'register',
        name: 'Register',
        component: () => import('@/views/auth/RegisterPage.vue'),
        meta: {
          title: '注册',
        },
      },
      {
        path: 'forgot-password',
        name: 'ForgotPassword',
        component: () => import('@/views/auth/ForgotPasswordPage.vue'),
        meta: {
          title: '忘记密码',
        },
      },
      {
        path: 'reset-password',
        name: 'ResetPassword',
        component: () => import('@/views/auth/ResetPasswordPage.vue'),
        meta: {
          title: '重置密码',
        },
      },
    ],
  },
  
  // 错误页面路由
  {
    path: '/error',
    component: EmptyLayout,
    children: [
      {
        path: '403',
        name: 'Error403',
        component: () => import('@/views/error/Error403Page.vue'),
        meta: {
          title: '访问被拒绝',
        },
      },
      {
        path: '404',
        name: 'Error404',
        component: () => import('@/views/error/Error404Page.vue'),
        meta: {
          title: '页面不存在',
        },
      },
      {
        path: '500',
        name: 'Error500',
        component: () => import('@/views/error/Error500Page.vue'),
        meta: {
          title: '服务器错误',
        },
      },
    ],
  },
  
  // 捕获所有未匹配的路由
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/error/404',
  },
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置，恢复到该位置
    if (savedPosition) {
      return savedPosition
    }
    
    // 如果有锚点，滚动到锚点
    if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth',
      }
    }
    
    // 否则滚动到顶部
    return { top: 0 }
  },
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 开始进度条
  NProgress.start()

  const userStore = useUserStore()
  const appStore = useAppStore()

  try {
    // 如果有token但未认证，尝试获取用户信息
    if (userStore.hasToken && !userStore.isAuthenticated) {
      try {
        await userStore.getCurrentUser()
      } catch (error) {
        // 获取用户信息失败，清除token
        userStore.clearUserData()
      }
    }

    // 检查认证要求
    if (to.meta.requiresAuth && !userStore.isAuthenticated) {
      // 需要登录但未登录，重定向到登录页
      next({
        name: 'Login',
        query: { redirect: to.fullPath },
      })
      return
    }

    // 检查访客要求（已登录用户不能访问的页面）
    if (to.meta.requiresGuest && userStore.isAuthenticated) {
      // 已登录用户访问登录页等，重定向到首页
      const redirectPath = (to.query.redirect as string) || '/home'
      next(redirectPath)
      return
    }

    // 检查权限
    if (to.meta.permissions && !userStore.hasPermissions(to.meta.permissions as string[])) {
      next({ name: 'Error403' })
      return
    }

    // 检查角色权限
    if (to.meta.roles && !userStore.hasRole(to.meta.roles as string[])) {
      next({ name: 'Error403' })
      return
    }

    // 更新面包屑
    appStore.setBreadcrumb(to)

    next()
  } catch (error) {
    console.error('路由守卫错误:', error)
    next({ name: 'Error500' })
  }
})

// 全局后置钩子
router.afterEach((to, from, failure) => {
  // 结束进度条
  NProgress.done()
  
  // 如果路由失败，记录错误
  if (failure) {
    console.error('路由跳转失败:', failure)
  }
  
  // 更新页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 元宇宙社交空间`
  }
  
  // 埋点统计
  if (import.meta.env.PROD) {
    // TODO: 发送页面访问统计
  }
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  NProgress.done()
})

export default router
