/**
 * 元宇宙社交空间 - 应用状态管理
 * 
 * 管理应用的全局状态，包括：
 * - 加载状态
 * - 当前路由信息
 * - 面包屑导航
 * - 缓存的视图
 * - 全局设置
 */

import { defineStore } from 'pinia'
import type { RouteLocationNormalized } from 'vue-router'

export interface AppState {
  // 加载状态
  isLoading: boolean
  loadingText: string
  
  // 路由信息
  currentRoute: RouteLocationNormalized | null
  breadcrumb: BreadcrumbItem[]
  
  // 缓存的视图
  cachedViews: string[]
  
  // 侧边栏状态
  sidebarCollapsed: boolean
  
  // 设备信息
  device: 'desktop' | 'tablet' | 'mobile'
  
  // 网络状态
  isOnline: boolean
  
  // 全局设置
  settings: AppSettings
}

export interface BreadcrumbItem {
  title: string
  path?: string
  icon?: string
}

export interface AppSettings {
  // 语言设置
  locale: string
  
  // 时区设置
  timezone: string
  
  // 通知设置
  notifications: {
    desktop: boolean
    sound: boolean
    vibration: boolean
  }
  
  // 隐私设置
  privacy: {
    showOnlineStatus: boolean
    allowFriendRequests: boolean
    showActivity: boolean
  }
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    isLoading: false,
    loadingText: '加载中...',
    currentRoute: null,
    breadcrumb: [],
    cachedViews: [],
    sidebarCollapsed: false,
    device: 'desktop',
    isOnline: navigator.onLine,
    settings: {
      locale: 'zh-CN',
      timezone: 'Asia/Shanghai',
      notifications: {
        desktop: true,
        sound: true,
        vibration: true,
      },
      privacy: {
        showOnlineStatus: true,
        allowFriendRequests: true,
        showActivity: true,
      },
    },
  }),

  getters: {
    // 是否为移动设备
    isMobile: (state) => state.device === 'mobile',
    
    // 是否为平板设备
    isTablet: (state) => state.device === 'tablet',
    
    // 是否为桌面设备
    isDesktop: (state) => state.device === 'desktop',
    
    // 当前页面标题
    currentPageTitle: (state) => {
      return state.currentRoute?.meta?.title || '元宇宙社交空间'
    },
  },

  actions: {
    // 设置加载状态
    setLoading(loading: boolean, text = '加载中...') {
      this.isLoading = loading
      this.loadingText = text
    },

    // 设置当前路由
    setCurrentRoute(route: RouteLocationNormalized) {
      this.currentRoute = route
    },

    // 设置面包屑
    setBreadcrumb(route: RouteLocationNormalized) {
      const breadcrumb: BreadcrumbItem[] = []
      
      // 根据路由生成面包屑
      const pathArray = route.path.split('/').filter(Boolean)
      let currentPath = ''
      
      pathArray.forEach((path, index) => {
        currentPath += `/${path}`
        const routeRecord = route.matched[index]
        
        if (routeRecord?.meta?.title) {
          breadcrumb.push({
            title: routeRecord.meta.title as string,
            path: index === pathArray.length - 1 ? undefined : currentPath,
          })
        }
      })
      
      this.breadcrumb = breadcrumb
    },

    // 添加缓存视图
    addCachedView(viewName: string) {
      if (!this.cachedViews.includes(viewName)) {
        this.cachedViews.push(viewName)
      }
    },

    // 移除缓存视图
    removeCachedView(viewName: string) {
      const index = this.cachedViews.indexOf(viewName)
      if (index > -1) {
        this.cachedViews.splice(index, 1)
      }
    },

    // 清空缓存视图
    clearCachedViews() {
      this.cachedViews = []
    },

    // 切换侧边栏状态
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
    },

    // 设置设备类型
    setDevice(device: 'desktop' | 'tablet' | 'mobile') {
      this.device = device
    },

    // 设置网络状态
    setOnlineStatus(isOnline: boolean) {
      this.isOnline = isOnline
    },

    // 更新设置
    updateSettings(settings: Partial<AppSettings>) {
      this.settings = { ...this.settings, ...settings }
      
      // 保存到本地存储
      localStorage.setItem('app-settings', JSON.stringify(this.settings))
    },

    // 初始化应用
    async initialize() {
      try {
        // 检测设备类型
        this.detectDevice()
        
        // 监听网络状态
        this.setupNetworkListener()
        
        // 加载本地设置
        this.loadLocalSettings()
        
        // 监听窗口大小变化
        this.setupResizeListener()
        
        console.log('✅ 应用状态初始化完成')
      } catch (error) {
        console.error('❌ 应用状态初始化失败:', error)
        throw error
      }
    },

    // 检测设备类型
    detectDevice() {
      const width = window.innerWidth
      
      if (width < 768) {
        this.device = 'mobile'
      } else if (width < 1024) {
        this.device = 'tablet'
      } else {
        this.device = 'desktop'
      }
    },

    // 设置网络状态监听
    setupNetworkListener() {
      window.addEventListener('online', () => {
        this.setOnlineStatus(true)
      })
      
      window.addEventListener('offline', () => {
        this.setOnlineStatus(false)
      })
    },

    // 加载本地设置
    loadLocalSettings() {
      try {
        const savedSettings = localStorage.getItem('app-settings')
        if (savedSettings) {
          const settings = JSON.parse(savedSettings)
          this.settings = { ...this.settings, ...settings }
        }
      } catch (error) {
        console.warn('加载本地设置失败:', error)
      }
    },

    // 设置窗口大小监听
    setupResizeListener() {
      window.addEventListener('resize', () => {
        this.detectDevice()
      })
    },
  },

  // 持久化配置
  persist: {
    key: 'app-store',
    storage: localStorage,
    paths: ['sidebarCollapsed', 'settings', 'cachedViews'],
  },
})
