/**
 * 元宇宙社交空间 - 内容管理状态管理
 */

import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'

import { contentApi } from '@/api/content'
import type { Post, Comment, Topic } from '@/types'

export interface ContentState {
  // 动态相关
  posts: Post[]
  currentPost: Post | null
  userPosts: Post[]
  feedPosts: Post[]
  recommendedPosts: Post[]

  // 评论相关
  comments: Comment[]

  // 话题相关
  topics: Topic[]
  trendingTopics: Topic[]

  // 加载状态
  loading: {
    posts: boolean
    feed: boolean
    recommended: boolean
    comments: boolean
    topics: boolean
    uploading: boolean
  }

  // 分页信息
  pagination: {
    posts: {
      page: number
      hasMore: boolean
    }
    feed: {
      page: number
      hasMore: boolean
    }
  }
}

export const useContentStore = defineStore('content', {
  state: (): ContentState => ({
    posts: [],
    currentPost: null,
    userPosts: [],
    feedPosts: [],
    recommendedPosts: [],
    comments: [],
    topics: [],
    trendingTopics: [],
    loading: {
      posts: false,
      feed: false,
      recommended: false,
      comments: false,
      topics: false,
      uploading: false,
    },
    pagination: {
      posts: {
        page: 1,
        hasMore: true,
      },
      feed: {
        page: 1,
        hasMore: true,
      },
    },
  }),

  getters: {
    // 获取当前动态的评论
    currentPostComments: (state) => {
      if (!state.currentPost) return []
      return state.comments.filter(comment => comment.post === state.currentPost!.id)
    },

    // 获取顶级评论
    topLevelComments: (state) => {
      if (!state.currentPost) return []
      return state.comments.filter(comment =>
        comment.post === state.currentPost!.id && !comment.parent
      )
    },
  },

  actions: {
    // 获取动态列表
    async fetchPosts(refresh = false) {
      try {
        if (refresh) {
          this.pagination.posts.page = 1
          this.pagination.posts.hasMore = true
        }

        this.loading.posts = true
        const response = await contentApi.getPosts({
          page: this.pagination.posts.page,
        })

        if (refresh) {
          this.posts = response.data.results
        } else {
          this.posts.push(...response.data.results)
        }

        this.pagination.posts.hasMore = !!response.data.next
        this.pagination.posts.page += 1

      } catch (error) {
        console.error('获取动态列表失败:', error)
        ElMessage.error('获取动态列表失败')
      } finally {
        this.loading.posts = false
      }
    },

    // 获取用户动态流
    async fetchFeed(refresh = false) {
      try {
        if (refresh) {
          this.pagination.feed.page = 1
          this.pagination.feed.hasMore = true
        }

        this.loading.feed = true
        const response = await contentApi.getFeed({
          page: this.pagination.feed.page,
        })

        if (refresh) {
          this.feedPosts = response.data.results
        } else {
          this.feedPosts.push(...response.data.results)
        }

        this.pagination.feed.hasMore = !!response.data.next
        this.pagination.feed.page += 1

      } catch (error) {
        console.error('获取动态流失败:', error)
        ElMessage.error('获取动态流失败')
      } finally {
        this.loading.feed = false
      }
    },

    // 获取推荐动态
    async fetchRecommendedPosts(refresh = false) {
      try {
        this.loading.recommended = true
        const response = await contentApi.getRecommendedPosts()

        if (refresh) {
          this.recommendedPosts = response.data.results
        } else {
          this.recommendedPosts.push(...response.data.results)
        }

      } catch (error) {
        console.error('获取推荐动态失败:', error)
        ElMessage.error('获取推荐动态失败')
      } finally {
        this.loading.recommended = false
      }
    },

    // 发布动态
    async createPost(postData: any) {
      try {
        const response = await contentApi.createPost(postData)

        // 添加到动态列表开头
        this.posts.unshift(response.data)
        this.feedPosts.unshift(response.data)

        ElMessage.success('动态发布成功')
        return response.data
      } catch (error: any) {
        console.error('发布动态失败:', error)
        const errorMessage = error.response?.data?.message || '发布动态失败'
        ElMessage.error(errorMessage)
        throw error
      }
    },

    // 获取动态详情
    async getPostDetail(postId: number) {
      try {
        const response = await contentApi.getPostDetail(postId)
        this.currentPost = response.data
        return response
      } catch (error) {
        console.error('获取动态详情失败:', error)
        ElMessage.error('获取动态详情失败')
        throw error
      }
    },

    // 获取评论列表
    async getComments(postId: number) {
      try {
        this.loading.comments = true
        const response = await contentApi.getComments(postId)
        this.comments = response.data.results
        return response
      } catch (error) {
        console.error('获取评论失败:', error)
        ElMessage.error('获取评论失败')
        throw error
      } finally {
        this.loading.comments = false
      }
    },

    // 创建评论
    async createComment(postId: number, commentData: { content: string; parent?: number }) {
      try {
        const response = await contentApi.createComment(postId, commentData)

        // 更新评论列表
        this.comments.unshift(response.data)

        // 更新动态的评论数
        this.updatePostInLists(postId, (post) => {
          post.comment_count += 1
        })

        ElMessage.success(commentData.parent ? '回复成功' : '评论成功')
        return response
      } catch (error: any) {
        console.error('发表评论失败:', error)
        const errorMessage = error.response?.data?.message || '发表评论失败'
        ElMessage.error(errorMessage)
        throw error
      }
    },

    // 点赞评论
    async likeComment(commentId: number, postId: number) {
      try {
        const response = await contentApi.likeComment(commentId, postId)

        // 更新本地评论状态
        const updateCommentLike = (comments: Comment[]) => {
          for (const comment of comments) {
            if (comment.id === commentId) {
              comment.is_liked = response.data.is_liked
              comment.like_count = response.data.like_count
              break
            }
            // 递归更新回复
            if (comment.replies) {
              updateCommentLike(comment.replies)
            }
          }
        }

        updateCommentLike(this.comments)
        return response
      } catch (error) {
        console.error('点赞评论失败:', error)
        ElMessage.error('操作失败')
        throw error
      }
    },

    // 删除评论
    async deleteComment(postId: number, commentId: number) {
      try {
        await contentApi.deleteComment(postId, commentId)

        // 从评论列表中移除
        const removeComment = (comments: Comment[]) => {
          for (let i = 0; i < comments.length; i++) {
            if (comments[i].id === commentId) {
              comments.splice(i, 1)
              return true
            }
            // 递归删除回复
            if (comments[i].replies && removeComment(comments[i].replies!)) {
              return true
            }
          }
          return false
        }

        removeComment(this.comments)

        // 更新动态的评论数
        this.updatePostInLists(postId, (post) => {
          post.comment_count = Math.max(0, post.comment_count - 1)
        })

        ElMessage.success('删除成功')
      } catch (error) {
        console.error('删除评论失败:', error)
        ElMessage.error('删除失败')
        throw error
      }
    },

    // 点赞动态
    async likePost(postId: number) {
      try {
        const response = await contentApi.likePost(postId)

        // 更新本地状态
        this.updatePostInLists(postId, (post) => {
          post.is_liked = response.data.is_liked
          post.like_count = response.data.like_count
        })

        return response.data
      } catch (error) {
        console.error('点赞动态失败:', error)
        ElMessage.error('操作失败')
        throw error
      }
    },

    // 更新动态在各个列表中的状态
    updatePostInLists(postId: number, updateFn: (post: Post) => void) {
      // 更新主列表
      const postIndex = this.posts.findIndex(p => p.id === postId)
      if (postIndex > -1) {
        updateFn(this.posts[postIndex])
      }

      // 更新动态流
      const feedIndex = this.feedPosts.findIndex(p => p.id === postId)
      if (feedIndex > -1) {
        updateFn(this.feedPosts[feedIndex])
      }

      // 更新当前动态
      if (this.currentPost && this.currentPost.id === postId) {
        updateFn(this.currentPost)
      }
    },

    // 清空数据
    clearPosts() {
      this.posts = []
      this.feedPosts = []
      this.pagination.posts.page = 1
      this.pagination.posts.hasMore = true
      this.pagination.feed.page = 1
      this.pagination.feed.hasMore = true
    },
  },

  // 持久化配置
  persist: {
    key: 'content-store',
    storage: localStorage,
    paths: ['trendingTopics'],
  },
})
