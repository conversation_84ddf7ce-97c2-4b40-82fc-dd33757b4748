/**
 * 元宇宙社交空间 - Pinia Store 统一导出
 * 
 * 这个文件统一导出所有的Pinia store，方便管理和使用
 */

// 导出所有store
export { useAppStore } from './app'
export { useUserStore } from './user'
export { useThemeStore } from './theme'
export { useSocialStore } from './social'
export { useMessageStore } from './message'
export { useContentStore } from './content'

// 导出类型定义
export type { AppState } from './app'
export type { UserState } from './user'
export type { ThemeState } from './theme'
export type { SocialState } from './social'
export type { MessageState } from './message'
export type { ContentState } from './content'
