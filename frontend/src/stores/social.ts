/**
 * 元宇宙社交空间 - 社交功能状态管理
 */

import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'

import { socialApi } from '@/api/social'
import type { Friend, FriendRequest, Follow, User } from '@/types'

export interface SocialState {
  // 好友相关
  friends: Friend[]
  friendRequests: FriendRequest[]

  // 关注相关
  followers: Follow[]
  following: Follow[]

  // 搜索和推荐
  searchResults: User[]
  recommendedUsers: User[]

  // 加载状态
  loading: {
    friends: boolean
    requests: boolean
    followers: boolean
    following: boolean
    search: boolean
    recommended: boolean
  }
}

export const useSocialStore = defineStore('social', {
  state: (): SocialState => ({
    friends: [],
    friendRequests: [],
    followers: [],
    following: [],
    searchResults: [],
    recommendedUsers: [],
    loading: {
      friends: false,
      requests: false,
      followers: false,
      following: false,
      search: false,
      recommended: false,
    },
  }),

  getters: {
    // 好友数量
    friendsCount: (state) => state.friends.length,

    // 待处理好友请求数量
    pendingRequestsCount: (state) =>
      state.friendRequests.filter(req => req.status === 'pending').length,

    // 粉丝数量
    followersCount: (state) => state.followers.length,

    // 关注数量
    followingCount: (state) => state.following.length,

    // 检查是否为好友
    isFriend: (state) => (userId: number) => {
      return state.friends.some(friend =>
        friend.requester.id === userId || friend.addressee.id === userId
      )
    },

    // 检查是否正在关注
    isFollowing: (state) => (userId: number) => {
      return state.following.some(follow => follow.following.id === userId)
    },
  },

  actions: {
    // 获取好友列表
    async fetchFriends() {
      try {
        this.loading.friends = true
        const response = await socialApi.getFriends()
        this.friends = response.data
      } catch (error) {
        console.error('获取好友列表失败:', error)
        ElMessage.error('获取好友列表失败')
      } finally {
        this.loading.friends = false
      }
    },

    // 获取好友请求
    async fetchFriendRequests() {
      try {
        this.loading.requests = true
        const response = await socialApi.getFriendRequests()
        this.friendRequests = response.data
      } catch (error) {
        console.error('获取好友请求失败:', error)
        ElMessage.error('获取好友请求失败')
      } finally {
        this.loading.requests = false
      }
    },

    // 发送好友请求
    async sendFriendRequest(userId: number, message?: string) {
      try {
        const response = await socialApi.sendFriendRequest(userId, message)
        ElMessage.success('好友请求已发送')
        return response.data
      } catch (error: any) {
        console.error('发送好友请求失败:', error)
        const errorMessage = error.response?.data?.message || '发送好友请求失败'
        ElMessage.error(errorMessage)
        throw error
      }
    },

    // 接受好友请求
    async acceptFriendRequest(requestId: number) {
      try {
        const response = await socialApi.acceptFriendRequest(requestId)

        // 更新本地状态
        const requestIndex = this.friendRequests.findIndex(req => req.id === requestId)
        if (requestIndex > -1) {
          this.friendRequests[requestIndex].status = 'accepted'
          this.friends.push(this.friendRequests[requestIndex])
        }

        ElMessage.success('已接受好友请求')
        return response.data
      } catch (error) {
        console.error('接受好友请求失败:', error)
        ElMessage.error('接受好友请求失败')
        throw error
      }
    },

    // 拒绝好友请求
    async declineFriendRequest(requestId: number) {
      try {
        const response = await socialApi.declineFriendRequest(requestId)

        // 更新本地状态
        const requestIndex = this.friendRequests.findIndex(req => req.id === requestId)
        if (requestIndex > -1) {
          this.friendRequests[requestIndex].status = 'declined'
        }

        ElMessage.success('已拒绝好友请求')
        return response.data
      } catch (error) {
        console.error('拒绝好友请求失败:', error)
        ElMessage.error('拒绝好友请求失败')
        throw error
      }
    },

    // 删除好友
    async removeFriend(friendshipId: number) {
      try {
        await socialApi.removeFriend(friendshipId)

        // 更新本地状态
        const friendIndex = this.friends.findIndex(friend => friend.id === friendshipId)
        if (friendIndex > -1) {
          this.friends.splice(friendIndex, 1)
        }

        ElMessage.success('已删除好友')
      } catch (error) {
        console.error('删除好友失败:', error)
        ElMessage.error('删除好友失败')
        throw error
      }
    },

    // 关注用户
    async followUser(userId: number) {
      try {
        const response = await socialApi.followUser(userId)

        // 更新本地状态
        this.following.push(response.data)

        ElMessage.success('关注成功')
        return response.data
      } catch (error: any) {
        console.error('关注用户失败:', error)
        const errorMessage = error.response?.data?.message || '关注失败'
        ElMessage.error(errorMessage)
        throw error
      }
    },

    // 取消关注
    async unfollowUser(userId: number) {
      try {
        await socialApi.unfollowUser(userId)

        // 更新本地状态
        const followIndex = this.following.findIndex(follow => follow.following.id === userId)
        if (followIndex > -1) {
          this.following.splice(followIndex, 1)
        }

        ElMessage.success('已取消关注')
      } catch (error) {
        console.error('取消关注失败:', error)
        ElMessage.error('取消关注失败')
        throw error
      }
    },

    // 获取关注列表
    async fetchFollowing() {
      try {
        this.loading.following = true
        const response = await socialApi.getFollowing()
        this.following = response.data
      } catch (error) {
        console.error('获取关注列表失败:', error)
        ElMessage.error('获取关注列表失败')
      } finally {
        this.loading.following = false
      }
    },

    // 获取粉丝列表
    async fetchFollowers() {
      try {
        this.loading.followers = true
        const response = await socialApi.getFollowers()
        this.followers = response.data
      } catch (error) {
        console.error('获取粉丝列表失败:', error)
        ElMessage.error('获取粉丝列表失败')
      } finally {
        this.loading.followers = false
      }
    },

    // 搜索用户
    async searchUsers(query: string) {
      try {
        this.loading.search = true
        const response = await socialApi.searchUsers(query)
        this.searchResults = response.data
        return response.data
      } catch (error) {
        console.error('搜索用户失败:', error)
        ElMessage.error('搜索用户失败')
        return []
      } finally {
        this.loading.search = false
      }
    },

    // 获取推荐用户
    async fetchRecommendedUsers() {
      try {
        this.loading.recommended = true
        const response = await socialApi.getRecommendedUsers()
        this.recommendedUsers = response.data
      } catch (error) {
        console.error('获取推荐用户失败:', error)
        ElMessage.error('获取推荐用户失败')
      } finally {
        this.loading.recommended = false
      }
    },

    // 清空搜索结果
    clearSearchResults() {
      this.searchResults = []
    },
  },

  // 持久化配置
  persist: {
    key: 'social-store',
    storage: localStorage,
    paths: ['friends', 'following'],
  },
})
