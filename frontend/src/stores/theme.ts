/**
 * 元宇宙社交空间 - 主题状态管理
 */

import { defineStore } from 'pinia'

export interface ThemeState {
  isDark: boolean
  primaryColor: string
}

export const useThemeStore = defineStore('theme', {
  state: (): ThemeState => ({
    isDark: false,
    primaryColor: '#3b82f6',
  }),

  actions: {
    initializeTheme() {
      // 初始化主题
      const savedTheme = localStorage.getItem('theme')
      if (savedTheme === 'dark') {
        this.isDark = true
      } else if (savedTheme === 'light') {
        this.isDark = false
      } else {
        // 跟随系统主题
        this.isDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      }
    },

    toggleTheme() {
      this.isDark = !this.isDark
      localStorage.setItem('theme', this.isDark ? 'dark' : 'light')
    },
  },
})
