/**
 * 元宇宙社交空间 - 用户状态管理
 * 
 * 管理用户相关的状态，包括：
 * - 用户信息
 * - 认证状态
 * - 权限管理
 * - 用户偏好设置
 */

import { defineStore } from 'pinia'
import type { User, LoginCredentials, RegisterData } from '@/types/user'
import { authApi } from '@/api/auth'
import { userApi } from '@/api/user'

export interface UserState {
  // 用户信息
  currentUser: User | null
  
  // 认证状态
  token: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  
  // 权限列表
  permissions: string[]
  
  // 用户偏好
  preferences: UserPreferences
  
  // 在线状态
  onlineStatus: 'online' | 'away' | 'busy' | 'offline'
}

export interface UserPreferences {
  // 通知偏好
  notifications: {
    email: boolean
    push: boolean
    sms: boolean
    friendRequests: boolean
    messages: boolean
    likes: boolean
    comments: boolean
  }
  
  // 隐私偏好
  privacy: {
    profileVisibility: 'public' | 'friends' | 'private'
    showOnlineStatus: boolean
    allowFriendRequests: boolean
    showActivity: boolean
  }
  
  // 界面偏好
  interface: {
    language: string
    theme: 'light' | 'dark' | 'auto'
    fontSize: 'small' | 'medium' | 'large'
    animations: boolean
  }
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    currentUser: null,
    token: localStorage.getItem('access_token'),
    refreshToken: localStorage.getItem('refresh_token'),
    isAuthenticated: false,
    permissions: [],
    preferences: {
      notifications: {
        email: true,
        push: true,
        sms: false,
        friendRequests: true,
        messages: true,
        likes: true,
        comments: true,
      },
      privacy: {
        profileVisibility: 'public',
        showOnlineStatus: true,
        allowFriendRequests: true,
        showActivity: true,
      },
      interface: {
        language: 'zh-CN',
        theme: 'auto',
        fontSize: 'medium',
        animations: true,
      },
    },
    onlineStatus: 'online',
  }),

  getters: {
    // 是否有token
    hasToken: (state) => !!state.token,
    
    // 用户显示名称
    displayName: (state) => {
      if (!state.currentUser) return ''
      return state.currentUser.displayName || 
             `${state.currentUser.firstName} ${state.currentUser.lastName}`.trim() ||
             state.currentUser.username
    },
    
    // 用户头像
    avatar: (state) => {
      return state.currentUser?.avatarUrl || '/default-avatar.png'
    },
    
    // 是否为管理员
    isAdmin: (state) => {
      return state.permissions.includes('admin')
    },
    
    // 检查是否有特定权限
    hasPermission: (state) => (permission: string) => {
      return state.permissions.includes(permission)
    },
    
    // 检查是否有多个权限
    hasPermissions: (state) => (permissions: string[]) => {
      return permissions.every(permission => state.permissions.includes(permission))
    },

    // 检查是否有特定角色
    hasRole: (state) => (roles: string | string[]) => {
      const roleArray = Array.isArray(roles) ? roles : [roles]
      return roleArray.some(role => state.permissions.includes(`role:${role}`))
    },
  },

  actions: {
    // 登录
    async login(credentials: LoginCredentials) {
      try {
        const response = await authApi.login(credentials)
        const { user, tokens } = response.data

        // 保存token
        this.setTokens(tokens.access, tokens.refresh)

        // 保存用户信息
        this.setCurrentUser(user)

        // 设置认证状态
        this.isAuthenticated = true

        // 获取用户权限
        await this.fetchUserPermissions()

        // 获取用户偏好
        await this.fetchUserPreferences()

        return user
      } catch (error) {
        console.error('登录失败:', error)
        throw error
      }
    },

    // 注册
    async register(data: RegisterData) {
      try {
        const response = await authApi.register(data)
        const { user, tokens } = response.data

        // 保存token
        this.setTokens(tokens.access, tokens.refresh)

        // 保存用户信息
        this.setCurrentUser(user)

        // 设置认证状态
        this.isAuthenticated = true

        return user
      } catch (error) {
        console.error('注册失败:', error)
        throw error
      }
    },

    // 登出
    async logout() {
      try {
        // 调用登出API
        if (this.refreshToken) {
          await authApi.logout(this.refreshToken)
        }
      } catch (error) {
        console.warn('登出API调用失败:', error)
      } finally {
        // 清除本地状态
        this.clearUserData()
      }
    },

    // 获取当前用户信息
    async getCurrentUser() {
      try {
        if (!this.token) {
          throw new Error('未找到访问令牌')
        }
        
        const response = await userApi.getCurrentUser()
        const user = response.data
        
        this.setCurrentUser(user)
        this.isAuthenticated = true
        
        // 获取用户权限和偏好
        await Promise.all([
          this.fetchUserPermissions(),
          this.fetchUserPreferences(),
        ])
        
        return user
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 如果获取失败，清除认证状态
        this.clearUserData()
        throw error
      }
    },

    // 更新用户信息
    async updateProfile(data: Partial<User>) {
      try {
        const response = await userApi.updateProfile(data)
        const user = response.data
        
        this.setCurrentUser(user)
        
        return user
      } catch (error) {
        console.error('更新用户信息失败:', error)
        throw error
      }
    },

    // 刷新token
    async refreshAccessToken() {
      try {
        if (!this.refreshToken) {
          throw new Error('未找到刷新令牌')
        }
        
        const response = await authApi.refreshToken(this.refreshToken)
        const { access_token, refresh_token } = response.data
        
        this.setTokens(access_token, refresh_token)
        
        return access_token
      } catch (error) {
        console.error('刷新token失败:', error)
        // 刷新失败，清除认证状态
        this.clearUserData()
        throw error
      }
    },

    // 获取用户权限
    async fetchUserPermissions() {
      try {
        const response = await userApi.getUserPermissions()
        this.permissions = response.data
      } catch (error) {
        console.error('获取用户权限失败:', error)
        this.permissions = []
      }
    },

    // 获取用户偏好
    async fetchUserPreferences() {
      try {
        const response = await userApi.getUserPreferences()
        this.preferences = { ...this.preferences, ...response.data }
      } catch (error) {
        console.error('获取用户偏好失败:', error)
      }
    },

    // 更新用户偏好
    async updatePreferences(preferences: Partial<UserPreferences>) {
      try {
        const response = await userApi.updatePreferences(preferences)
        this.preferences = { ...this.preferences, ...response.data }
        
        // 保存到本地存储
        localStorage.setItem('user-preferences', JSON.stringify(this.preferences))
      } catch (error) {
        console.error('更新用户偏好失败:', error)
        throw error
      }
    },

    // 设置在线状态
    async setOnlineStatus(status: 'online' | 'away' | 'busy' | 'offline') {
      try {
        await userApi.updateOnlineStatus(status)
        this.onlineStatus = status
      } catch (error) {
        console.error('设置在线状态失败:', error)
        throw error
      }
    },

    // 设置token
    setTokens(accessToken: string, refreshToken: string) {
      this.token = accessToken
      this.refreshToken = refreshToken
      
      // 保存到本地存储
      localStorage.setItem('access_token', accessToken)
      localStorage.setItem('refresh_token', refreshToken)
    },

    // 设置当前用户
    setCurrentUser(user: User) {
      this.currentUser = user
    },

    // 清除用户数据
    clearUserData() {
      this.currentUser = null
      this.token = null
      this.refreshToken = null
      this.isAuthenticated = false
      this.permissions = []
      this.onlineStatus = 'offline'
      
      // 清除本地存储
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('user-preferences')
    },

    // 初始化用户状态
    async initializeAuth() {
      try {
        if (this.token) {
          await this.getCurrentUser()
        }
      } catch (error) {
        console.error('初始化认证状态失败:', error)
        this.clearUserData()
      }
    },
  },

  // 持久化配置
  persist: {
    key: 'user-store',
    storage: localStorage,
    paths: ['preferences'],
  },
})
