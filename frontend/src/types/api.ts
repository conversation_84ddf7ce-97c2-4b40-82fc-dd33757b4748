/**
 * 元宇宙社交空间 - API相关类型定义
 */

// API响应基础结构
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
  request_id: string
}

// 分页响应结构
export interface PaginatedResponse<T = any> {
  code: number
  message: string
  data: {
    items: T[]
    total: number
    page: number
    page_size: number
    total_pages: number
    has_next: boolean
    has_prev: boolean
  }
  timestamp: string
  request_id: string
}

// API错误结构
export interface ApiError {
  code: number
  message: string
  errors?: Record<string, string[]>
  timestamp: string
  request_id: string
}

// 认证令牌响应
export interface TokenResponse {
  access_token: string
  refresh_token: string
  expires_in: number
  token_type: string
}
