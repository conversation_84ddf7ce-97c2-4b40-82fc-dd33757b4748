/**
 * 元宇宙社交空间 - 通用类型定义
 */

// 基础实体接口
export interface BaseEntity {
  id: number
  createdAt: string
  updatedAt: string
}

// 分页参数
export interface PaginationParams {
  page?: number
  pageSize?: number
}

// 排序参数
export interface SortParams {
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 搜索参数
export interface SearchParams {
  query?: string
}

// 过滤参数
export interface FilterParams {
  [key: string]: any
}
