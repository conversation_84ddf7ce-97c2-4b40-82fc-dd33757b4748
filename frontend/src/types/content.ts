/**
 * 元宇宙社交空间 - 内容相关类型定义
 */

import type { BaseEntity, User } from './index'

// 话题标签
export interface Topic extends BaseEntity {
  name: string
  display_name: string
  description?: string
  cover_url?: string
  is_official: boolean
  is_active: boolean
  post_count?: number
}

// 动态媒体文件
export interface PostMedia extends BaseEntity {
  post: number
  uploaded_by: User
  media_type: 'image' | 'video' | 'audio' | 'document'
  filename: string
  file_url: string
  thumbnail_url?: string
  file_size: number
  file_format: string
  width?: number
  height?: number
  duration?: number
  sort_order: number
}

// 动态/帖子
export interface Post extends BaseEntity {
  author: User
  content: string
  content_type: 'text' | 'image' | 'video' | 'link'
  media_urls: string[]
  location?: string
  visibility: 'public' | 'friends' | 'private'
  status: 'draft' | 'published' | 'reviewing' | 'rejected'
  topics: Topic[]
  media_files: PostMedia[]
  like_count: number
  comment_count: number
  share_count: number
  view_count: number
  is_pinned: boolean
  is_liked?: boolean
  is_shared?: boolean
  published_at?: string
}

// 评论
export interface Comment extends BaseEntity {
  post: number
  author: User
  parent?: number
  content: string
  like_count: number
  reply_count: number
  is_liked?: boolean
  replies?: Comment[]
}

// 点赞
export interface Like extends BaseEntity {
  user: User
  target_type: 'post' | 'comment'
  target_id: number
}

// 分享
export interface Share extends BaseEntity {
  user: User
  post: Post
  comment?: string
}

// 动态创建数据
export interface CreatePostData {
  content: string
  content_type?: string
  visibility?: string
  location?: string
  topic_names?: string[]
  media_urls?: string[]
}

// 评论创建数据
export interface CreateCommentData {
  content: string
  parent?: number
}
