/**
 * 元宇宙社交空间 - 类型定义统一导出
 * 
 * 这个文件统一导出所有的TypeScript类型定义
 */

// 导出API相关类型
export type { ApiResponse, PaginatedResponse, ApiError } from './api'

// 导出用户相关类型
export type { User, LoginCredentials, RegisterData, UserProfile } from './user'

// 导出社交相关类型
export type { Friend, FriendRequest, Follow } from './social'

// 导出内容相关类型
export type { Post, Comment, Like } from './content'

// 导出消息相关类型
export type { Message, Conversation, Notification } from './message'

// 导出通用类型
export type { BaseEntity, PaginationParams, SortParams } from './common'
