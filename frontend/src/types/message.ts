/**
 * 元宇宙社交空间 - 消息相关类型定义
 */

import type { BaseEntity, User } from './index'

// 消息
export interface Message extends BaseEntity {
  conversation: Conversation
  sender: User
  content: string
  messageType: 'text' | 'image' | 'file' | 'voice'
  mediaUrl?: string
  replyTo?: Message
  isRead: boolean
}

// 会话
export interface Conversation extends BaseEntity {
  type: 'private' | 'group'
  name?: string
  participants: User[]
  lastMessage?: Message
  unreadCount: number
}

// 通知
export interface Notification extends BaseEntity {
  user: User
  type: 'like' | 'comment' | 'follow' | 'friend_request' | 'message'
  title: string
  content: string
  data?: any
  isRead: boolean
  readAt?: string
}
