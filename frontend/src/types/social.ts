/**
 * 元宇宙社交空间 - 社交相关类型定义
 */

import type { BaseEntity, User } from './index'

// 好友关系
export interface Friend extends BaseEntity {
  requester: User
  addressee: User
  status: 'pending' | 'accepted' | 'declined' | 'blocked'
  requested_at: string
  responded_at?: string
}

// 好友请求
export interface FriendRequest extends BaseEntity {
  requester: User
  addressee: User
  status: 'pending' | 'accepted' | 'declined' | 'blocked'
  message?: string
  requested_at: string
  responded_at?: string
}

// 关注关系
export interface Follow extends BaseEntity {
  follower: User
  following: User
}

// 用户屏蔽
export interface UserBlock extends BaseEntity {
  blocker: User
  blocked: User
  reason?: string
}

// 用户访问记录
export interface UserVisit extends BaseEntity {
  visitor: User
  visited: User
  visit_count: number
}

// 用户统计信息
export interface UserStats {
  friends_count: number
  followers_count: number
  following_count: number
  visits_count: number
}
