/**
 * 元宇宙社交空间 - 用户相关类型定义
 */

import type { BaseEntity } from './common'

// 用户基础信息
export interface User extends BaseEntity {
  username: string
  email: string
  firstName: string
  lastName: string
  displayName?: string
  bio?: string
  avatarUrl?: string
  coverUrl?: string
  birthDate?: string
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say'
  location?: string
  website?: string
  isActive: boolean
  isVerified: boolean
  lastLoginAt?: string
}

// 用户资料
export interface UserProfile {
  firstName: string
  lastName: string
  bio?: string
  avatarUrl?: string
  coverUrl?: string
  birthDate?: string
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say'
  location?: string
  website?: string
}

// 登录凭据
export interface LoginCredentials {
  email: string
  password: string
  remember?: boolean
}

// 注册数据
export interface RegisterData {
  username: string
  email: string
  password: string
  passwordConfirm: string
  firstName: string
  lastName: string
  agreeToTerms: boolean
}

// 用户统计信息
export interface UserStats {
  postsCount: number
  friendsCount: number
  followersCount: number
  followingCount: number
  likesCount: number
  likesReceived: number
  commentsCount: number
  sharesCount: number
  visitsCount: number
}

// 扩展用户信息（包含社交状态）
export interface UserWithSocialStatus extends User {
  bio?: string
  location?: string
  website?: string
  is_friend?: boolean
  is_following?: boolean
  is_followed_by?: boolean
  stats?: UserStats
}
