/**
 * 元宇宙社交空间 - 性能监控工具
 */

export function setupPerformanceMonitor() {
  // 设置性能监控
  if ('performance' in window) {
    // 监控页面加载性能
    window.addEventListener('load', () => {
      setTimeout(() => {
        const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        console.log('页面加载性能:', {
          DNS解析: perfData.domainLookupEnd - perfData.domainLookupStart,
          TCP连接: perfData.connectEnd - perfData.connectStart,
          请求响应: perfData.responseEnd - perfData.requestStart,
          DOM解析: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
          页面加载: perfData.loadEventEnd - perfData.loadEventStart,
        })
      }, 0)
    })
  }
  
  console.log('✅ 性能监控设置完成')
}
