<script setup lang="ts">
/**
 * 元宇宙社交空间 - 忘记密码页面
 * 
 * 用户忘记密码时的邮箱验证界面
 */

import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

import { authApi } from '@/api/auth'

const router = useRouter()

// 表单引用
const forgotFormRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)
const emailSent = ref(false)

// 表单数据
const forgotForm = reactive({
  email: '',
})

// 表单验证规则
const forgotRules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
}

// 处理发送重置邮件
const handleSendEmail = async () => {
  if (!forgotFormRef.value) return
  
  try {
    // 验证表单
    await forgotFormRef.value.validate()
    
    loading.value = true
    
    // 调用忘记密码API
    await authApi.forgotPassword(forgotForm.email)
    
    emailSent.value = true
    ElMessage.success('密码重置邮件已发送，请查收邮箱')
    
  } catch (error: any) {
    console.error('发送邮件失败:', error)
    
    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error('发送失败，请检查邮箱地址')
    }
  } finally {
    loading.value = false
  }
}

// 重新发送邮件
const resendEmail = () => {
  emailSent.value = false
}

// 返回登录页面
const goToLogin = () => {
  router.push('/auth/login')
}
</script>

<template>
  <div class="forgot-password-page">
    <div class="forgot-container">
      <div class="form-container">
        <!-- 未发送邮件状态 -->
        <div v-if="!emailSent" class="form-content">
          <div class="form-header">
            <el-icon class="header-icon"><Lock /></el-icon>
            <h2>忘记密码？</h2>
            <p>请输入您的邮箱地址，我们将发送密码重置链接</p>
          </div>
          
          <el-form
            ref="forgotFormRef"
            :model="forgotForm"
            :rules="forgotRules"
            class="forgot-form"
            size="large"
            @submit.prevent="handleSendEmail"
          >
            <el-form-item prop="email">
              <el-input
                v-model="forgotForm.email"
                type="email"
                placeholder="请输入您的邮箱地址"
                prefix-icon="Message"
                clearable
              />
            </el-form-item>
            
            <el-form-item>
              <el-button
                type="primary"
                class="submit-button"
                :loading="loading"
                @click="handleSendEmail"
              >
                {{ loading ? '发送中...' : '发送重置邮件' }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 邮件已发送状态 -->
        <div v-else class="success-content">
          <div class="success-header">
            <el-icon class="success-icon"><SuccessFilled /></el-icon>
            <h2>邮件已发送</h2>
            <p>我们已向 <strong>{{ forgotForm.email }}</strong> 发送了密码重置邮件</p>
          </div>
          
          <div class="success-tips">
            <h3>接下来该怎么做？</h3>
            <ol>
              <li>检查您的邮箱收件箱</li>
              <li>点击邮件中的重置链接</li>
              <li>设置新密码</li>
              <li>使用新密码登录</li>
            </ol>
          </div>
          
          <div class="success-actions">
            <el-button @click="resendEmail">
              重新发送邮件
            </el-button>
            <el-button type="primary" @click="goToLogin">
              返回登录
            </el-button>
          </div>
        </div>
        
        <!-- 底部链接 -->
        <div class="form-footer">
          <el-button type="primary" link @click="goToLogin">
            <el-icon><ArrowLeft /></el-icon>
            返回登录
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.forgot-password-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.forgot-container {
  width: 100%;
  max-width: 500px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-container {
  padding: 60px 40px;
}

.form-content,
.success-content {
  text-align: center;
}

.form-header,
.success-header {
  margin-bottom: 40px;
}

.header-icon,
.success-icon {
  font-size: 4rem;
  color: #667eea;
  margin-bottom: 20px;
}

.success-icon {
  color: #22c55e;
}

.form-header h2,
.success-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
}

.form-header p,
.success-header p {
  color: #6b7280;
  font-size: 1rem;
  line-height: 1.6;
}

.forgot-form {
  margin-bottom: 30px;
}

.submit-button {
  width: 100%;
  height: 48px;
  font-size: 1rem;
  font-weight: 600;
}

.success-tips {
  text-align: left;
  background: #f8fafc;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 30px;
}

.success-tips h3 {
  color: #1f2937;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.success-tips ol {
  color: #4b5563;
  line-height: 1.8;
  padding-left: 20px;
}

.success-tips li {
  margin-bottom: 8px;
}

.success-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 30px;
}

.form-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-container {
    padding: 40px 20px;
  }
  
  .success-actions {
    flex-direction: column;
  }
  
  .success-actions .el-button {
    width: 100%;
  }
}
</style>
