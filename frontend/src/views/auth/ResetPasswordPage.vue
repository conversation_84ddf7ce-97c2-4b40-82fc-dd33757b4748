<script setup lang="ts">
/**
 * 元宇宙社交空间 - 重置密码页面
 * 
 * 用户通过邮件链接重置密码的界面
 */

import { reactive, ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

import { authApi } from '@/api/auth'

const router = useRouter()
const route = useRoute()

// 表单引用
const resetFormRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)
const tokenValid = ref(true)

// 重置token
const resetToken = ref('')

// 表单数据
const resetForm = reactive({
  password: '',
  passwordConfirm: '',
})

// 表单验证规则
const resetRules: FormRules = {
  password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, message: '密码长度不能少于8位', trigger: 'blur' },
    { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, message: '密码必须包含大小写字母和数字', trigger: 'blur' },
  ],
  passwordConfirm: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== resetForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
}

// 组件挂载时获取token
onMounted(() => {
  const token = route.query.token as string
  if (!token) {
    ElMessage.error('重置链接无效')
    tokenValid.value = false
    return
  }
  resetToken.value = token
})

// 处理重置密码
const handleResetPassword = async () => {
  if (!resetFormRef.value) return
  
  try {
    // 验证表单
    await resetFormRef.value.validate()
    
    loading.value = true
    
    // 调用重置密码API
    await authApi.resetPassword(
      resetToken.value,
      resetForm.password,
      resetForm.passwordConfirm
    )
    
    ElMessage.success('密码重置成功，请使用新密码登录')
    
    // 跳转到登录页面
    await router.push('/auth/login')
    
  } catch (error: any) {
    console.error('重置密码失败:', error)
    
    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error('重置失败，请重新尝试')
    }
  } finally {
    loading.value = false
  }
}

// 返回登录页面
const goToLogin = () => {
  router.push('/auth/login')
}

// 重新发送重置邮件
const goToForgotPassword = () => {
  router.push('/auth/forgot-password')
}
</script>

<template>
  <div class="reset-password-page">
    <div class="reset-container">
      <div class="form-container">
        <!-- Token有效时显示重置表单 -->
        <div v-if="tokenValid" class="form-content">
          <div class="form-header">
            <el-icon class="header-icon"><Key /></el-icon>
            <h2>重置密码</h2>
            <p>请设置您的新密码</p>
          </div>
          
          <el-form
            ref="resetFormRef"
            :model="resetForm"
            :rules="resetRules"
            class="reset-form"
            size="large"
            @submit.prevent="handleResetPassword"
          >
            <el-form-item prop="password">
              <el-input
                v-model="resetForm.password"
                type="password"
                placeholder="请输入新密码"
                prefix-icon="Lock"
                show-password
                clearable
              />
              <div class="password-tips">
                <small>密码必须包含大小写字母和数字，至少8位</small>
              </div>
            </el-form-item>
            
            <el-form-item prop="passwordConfirm">
              <el-input
                v-model="resetForm.passwordConfirm"
                type="password"
                placeholder="请确认新密码"
                prefix-icon="Lock"
                show-password
                clearable
              />
            </el-form-item>
            
            <el-form-item>
              <el-button
                type="primary"
                class="reset-button"
                :loading="loading"
                @click="handleResetPassword"
              >
                {{ loading ? '重置中...' : '重置密码' }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <!-- Token无效时显示错误信息 -->
        <div v-else class="error-content">
          <div class="error-header">
            <el-icon class="error-icon"><WarningFilled /></el-icon>
            <h2>链接无效</h2>
            <p>重置密码链接已过期或无效</p>
          </div>
          
          <div class="error-actions">
            <el-button @click="goToForgotPassword">
              重新发送重置邮件
            </el-button>
            <el-button type="primary" @click="goToLogin">
              返回登录
            </el-button>
          </div>
        </div>
        
        <!-- 底部链接 -->
        <div class="form-footer">
          <el-button type="primary" link @click="goToLogin">
            <el-icon><ArrowLeft /></el-icon>
            返回登录
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.reset-password-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.reset-container {
  width: 100%;
  max-width: 500px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-container {
  padding: 60px 40px;
}

.form-content,
.error-content {
  text-align: center;
}

.form-header,
.error-header {
  margin-bottom: 40px;
}

.header-icon {
  font-size: 4rem;
  color: #667eea;
  margin-bottom: 20px;
}

.error-icon {
  font-size: 4rem;
  color: #ef4444;
  margin-bottom: 20px;
}

.form-header h2,
.error-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
}

.form-header p,
.error-header p {
  color: #6b7280;
  font-size: 1rem;
}

.reset-form {
  margin-bottom: 30px;
}

.password-tips {
  margin-top: 8px;
  text-align: left;
}

.password-tips small {
  color: #6b7280;
  font-size: 0.875rem;
}

.reset-button {
  width: 100%;
  height: 48px;
  font-size: 1rem;
  font-weight: 600;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 30px;
}

.form-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-container {
    padding: 40px 20px;
  }
  
  .error-actions {
    flex-direction: column;
  }
  
  .error-actions .el-button {
    width: 100%;
  }
}
</style>
