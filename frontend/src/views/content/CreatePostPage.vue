<script setup lang="ts">
/**
 * 元宇宙社交空间 - 动态发布页面
 */

import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { UploadFile, UploadFiles } from 'element-plus'

import { useContentStore } from '@/stores/content'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const contentStore = useContentStore()
const userStore = useUserStore()

// 表单数据
const postForm = reactive({
  content: '',
  content_type: 'text',
  visibility: 'public',
  location: '',
  topic_names: [] as string[],
  media_urls: [] as string[],
})

// 状态管理
const loading = ref(false)
const uploadingFiles = ref<UploadFiles>([])

// 话题输入
const topicInput = ref('')
const topicSuggestions = ref<any[]>([])

// 计算属性
const canPublish = computed(() => {
  return postForm.content.trim().length > 0 && !loading.value
})

const contentLength = computed(() => postForm.content.length)
const maxContentLength = 2000

// 发布动态
const publishPost = async () => {
  if (!canPublish.value) return
  
  try {
    loading.value = true
    
    await contentStore.createPost({
      content: postForm.content,
      content_type: postForm.content_type,
      visibility: postForm.visibility,
      location: postForm.location,
      topic_names: postForm.topic_names,
      media_urls: postForm.media_urls,
    })
    
    // 重置表单
    resetForm()
    
    // 跳转到首页
    await router.push('/home')
    
  } catch (error) {
    console.error('发布动态失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  postForm.content = ''
  postForm.content_type = 'text'
  postForm.visibility = 'public'
  postForm.location = ''
  postForm.topic_names = []
  postForm.media_urls = []
  uploadingFiles.value = []
  topicInput.value = ''
}

// 添加话题
const addTopic = () => {
  const topic = topicInput.value.trim()
  if (topic && !postForm.topic_names.includes(topic)) {
    postForm.topic_names.push(topic)
    topicInput.value = ''
  }
}

// 删除话题
const removeTopic = (index: number) => {
  postForm.topic_names.splice(index, 1)
}

// 搜索话题建议
const searchTopics = async (query: string) => {
  if (query.length < 2) {
    topicSuggestions.value = []
    return
  }
  
  try {
    const topics = await contentStore.searchTopics(query)
    topicSuggestions.value = topics.slice(0, 10)
  } catch (error) {
    console.error('搜索话题失败:', error)
  }
}

// 文件上传处理
const handleFileUpload = async (file: UploadFile) => {
  if (!file.raw) return false
  
  try {
    // 检查文件类型
    const isImage = file.raw.type.startsWith('image/')
    const isVideo = file.raw.type.startsWith('video/')
    
    if (!isImage && !isVideo) {
      ElMessage.error('只支持图片和视频文件')
      return false
    }
    
    // 检查文件大小（50MB）
    const maxSize = 50 * 1024 * 1024
    if (file.raw.size > maxSize) {
      ElMessage.error('文件大小不能超过50MB')
      return false
    }
    
    // 上传文件
    const mediaType = isImage ? 'image' : 'video'
    const result = await contentStore.uploadMedia(file.raw, mediaType)
    
    // 添加到媒体URL列表
    postForm.media_urls.push(result.file_url)
    
    // 更新内容类型
    if (postForm.content_type === 'text') {
      postForm.content_type = mediaType
    }
    
    ElMessage.success('文件上传成功')
    return true
    
  } catch (error) {
    console.error('文件上传失败:', error)
    return false
  }
}

// 删除媒体文件
const removeMedia = (index: number) => {
  postForm.media_urls.splice(index, 1)
  
  // 如果没有媒体文件了，重置内容类型
  if (postForm.media_urls.length === 0) {
    postForm.content_type = 'text'
  }
}

// 获取位置信息
const getLocation = () => {
  if ('geolocation' in navigator) {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords
        postForm.location = `${latitude},${longitude}`
        ElMessage.success('位置信息已获取')
      },
      (error) => {
        console.error('获取位置失败:', error)
        ElMessage.error('获取位置信息失败')
      }
    )
  } else {
    ElMessage.error('浏览器不支持地理位置功能')
  }
}

// 清除位置
const clearLocation = () => {
  postForm.location = ''
}
</script>

<template>
  <div class="create-post-page">
    <div class="page-container">
      <div class="page-header">
        <h1>发布动态</h1>
        <div class="header-actions">
          <el-button @click="$router.back()">取消</el-button>
          <el-button 
            type="primary" 
            :loading="loading"
            :disabled="!canPublish"
            @click="publishPost"
          >
            {{ loading ? '发布中...' : '发布' }}
          </el-button>
        </div>
      </div>
      
      <div class="post-form">
        <!-- 用户信息 -->
        <div class="user-info">
          <el-avatar 
            :src="userStore.avatar"
            :size="50"
          >
            {{ userStore.displayName?.charAt(0) || 'U' }}
          </el-avatar>
          <div class="user-details">
            <div class="user-name">{{ userStore.displayName || userStore.username }}</div>
            <el-select v-model="postForm.visibility" size="small">
              <el-option label="公开" value="public" />
              <el-option label="好友可见" value="friends" />
              <el-option label="私有" value="private" />
            </el-select>
          </div>
        </div>
        
        <!-- 内容输入 -->
        <div class="content-input">
          <el-input
            v-model="postForm.content"
            type="textarea"
            :rows="6"
            placeholder="分享你的想法..."
            :maxlength="maxContentLength"
            show-word-limit
            resize="none"
          />
        </div>
        
        <!-- 媒体文件预览 -->
        <div v-if="postForm.media_urls.length > 0" class="media-preview">
          <div class="media-grid">
            <div 
              v-for="(url, index) in postForm.media_urls" 
              :key="index"
              class="media-item"
            >
              <img v-if="postForm.content_type === 'image'" :src="url" alt="预览图" />
              <video v-else-if="postForm.content_type === 'video'" :src="url" controls />
              <el-button 
                type="danger" 
                size="small" 
                circle
                class="remove-btn"
                @click="removeMedia(index)"
              >
                <el-icon><Close /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 话题标签 -->
        <div class="topics-section">
          <div class="topics-input">
            <el-input
              v-model="topicInput"
              placeholder="添加话题标签..."
              @keyup.enter="addTopic"
              @input="searchTopics"
            >
              <template #prefix>
                <el-icon><PriceTag /></el-icon>
              </template>
              <template #append>
                <el-button @click="addTopic">添加</el-button>
              </template>
            </el-input>
          </div>
          
          <!-- 话题建议 -->
          <div v-if="topicSuggestions.length > 0" class="topic-suggestions">
            <el-tag
              v-for="topic in topicSuggestions"
              :key="topic.id"
              class="suggestion-tag"
              @click="topicInput = topic.name"
            >
              #{{ topic.display_name }}
            </el-tag>
          </div>
          
          <!-- 已选话题 -->
          <div v-if="postForm.topic_names.length > 0" class="selected-topics">
            <el-tag
              v-for="(topic, index) in postForm.topic_names"
              :key="index"
              closable
              @close="removeTopic(index)"
            >
              #{{ topic }}
            </el-tag>
          </div>
        </div>
        
        <!-- 位置信息 -->
        <div v-if="postForm.location" class="location-info">
          <el-tag closable @close="clearLocation">
            <el-icon><Location /></el-icon>
            位置信息已添加
          </el-tag>
        </div>
        
        <!-- 工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <!-- 文件上传 -->
            <el-upload
              :file-list="uploadingFiles"
              :before-upload="handleFileUpload"
              :show-file-list="false"
              accept="image/*,video/*"
              multiple
            >
              <el-button type="primary" link>
                <el-icon><Picture /></el-icon>
                图片/视频
              </el-button>
            </el-upload>
            
            <!-- 位置 -->
            <el-button type="primary" link @click="getLocation">
              <el-icon><Location /></el-icon>
              位置
            </el-button>
          </div>
          
          <div class="toolbar-right">
            <span class="char-count" :class="{ 'over-limit': contentLength > maxContentLength }">
              {{ contentLength }}/{{ maxContentLength }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.create-post-page {
  min-height: 100vh;
  background-color: var(--el-bg-color-page);
  padding: 20px;
}

.page-container {
  max-width: 600px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.page-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.post-form {
  padding: 24px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.content-input {
  margin-bottom: 20px;
}

.media-preview {
  margin-bottom: 20px;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.media-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.media-item img,
.media-item video {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.remove-btn {
  position: absolute;
  top: 8px;
  right: 8px;
}

.topics-section {
  margin-bottom: 20px;
}

.topics-input {
  margin-bottom: 12px;
}

.topic-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.suggestion-tag {
  cursor: pointer;
}

.suggestion-tag:hover {
  background-color: var(--el-color-primary-light-9);
}

.selected-topics {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.location-info {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-light);
}

.toolbar-left {
  display: flex;
  gap: 16px;
}

.char-count {
  font-size: 0.9rem;
  color: var(--el-text-color-secondary);
}

.char-count.over-limit {
  color: var(--el-color-danger);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .create-post-page {
    padding: 0;
  }
  
  .page-container {
    border-radius: 0;
    min-height: 100vh;
  }
  
  .page-header {
    padding: 16px 20px;
  }
  
  .post-form {
    padding: 20px;
  }
  
  .media-grid {
    grid-template-columns: 1fr;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .toolbar-left {
    justify-content: center;
  }
  
  .toolbar-right {
    text-align: center;
  }
}
</style>
