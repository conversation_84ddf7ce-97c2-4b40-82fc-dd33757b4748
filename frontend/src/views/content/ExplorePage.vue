<script setup lang="ts">
/**
 * 元宇宙社交空间 - 内容发现页面
 */

import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useContentStore } from '@/stores/content'
import PostList from '@/components/content/PostList.vue'
import type { Post } from '@/types/content'

const router = useRouter()
const contentStore = useContentStore()

// 状态管理
const searchQuery = ref('')
const activeFilter = ref('all')
const showFilters = ref(false)

// 筛选选项
const filterOptions = [
  { label: '全部', value: 'all' },
  { label: '图片', value: 'image' },
  { label: '视频', value: 'video' },
  { label: '文本', value: 'text' },
]

// 处理搜索
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    // TODO: 实现搜索功能
    console.log('搜索:', searchQuery.value)
  }
}

// 处理筛选
const handleFilter = (filter: string) => {
  activeFilter.value = filter
  showFilters.value = false
  // TODO: 实现筛选功能
  console.log('筛选:', filter)
}

// 处理动态点击
const handlePostClick = (post: Post) => {
  router.push(`/content/post/${post.id}`)
}

// 处理用户点击
const handleUserClick = (userId: number) => {
  router.push(`/user/${userId}`)
}

onMounted(() => {
  console.log('发现页面加载完成')
})
</script>

<template>
  <div class="explore-page">
    <div class="page-container">
      <!-- 搜索和筛选栏 -->
      <div class="search-bar">
        <div class="search-input">
          <el-input
            v-model="searchQuery"
            placeholder="搜索动态、用户、话题..."
            size="large"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button @click="handleSearch">搜索</el-button>
            </template>
          </el-input>
        </div>
        
        <el-button 
          size="large"
          @click="showFilters = !showFilters"
        >
          <el-icon><Filter /></el-icon>
          筛选
        </el-button>
      </div>
      
      <!-- 筛选选项 -->
      <div v-if="showFilters" class="filter-options">
        <div class="filter-title">内容类型</div>
        <div class="filter-buttons">
          <el-button
            v-for="option in filterOptions"
            :key="option.value"
            :type="activeFilter === option.value ? 'primary' : 'default'"
            size="small"
            @click="handleFilter(option.value)"
          >
            {{ option.label }}
          </el-button>
        </div>
      </div>
      
      <!-- 热门话题 -->
      <div class="trending-section">
        <div class="section-header">
          <h3>热门话题</h3>
          <el-button type="text" size="small">查看更多</el-button>
        </div>
        
        <div class="trending-topics">
          <el-tag
            v-for="i in 8"
            :key="i"
            size="large"
            effect="plain"
            class="topic-tag"
          >
            #话题{{ i }}
          </el-tag>
        </div>
      </div>
      
      <!-- 内容列表 -->
      <div class="content-section">
        <div class="section-header">
          <h3>发现内容</h3>
          <div class="sort-options">
            <el-select v-model="activeFilter" size="small" style="width: 100px;">
              <el-option label="最新" value="latest" />
              <el-option label="热门" value="popular" />
              <el-option label="推荐" value="recommended" />
            </el-select>
          </div>
        </div>
        
        <PostList
          list-type="posts"
          @post-click="handlePostClick"
          @user-click="handleUserClick"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.explore-page {
  min-height: 100vh;
  background-color: var(--el-bg-color-page);
}

.page-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.search-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.search-input {
  flex: 1;
}

.filter-options {
  background: white;
  border-radius: 12px;
  box-shadow: var(--el-box-shadow-light);
  padding: 20px;
  margin-bottom: 20px;
}

.filter-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 12px;
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.trending-section {
  background: white;
  border-radius: 12px;
  box-shadow: var(--el-box-shadow-light);
  padding: 20px;
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.trending-topics {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.topic-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.topic-tag:hover {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
  color: var(--el-color-primary);
}

.content-section {
  background: white;
  border-radius: 12px;
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;
}

.content-section .section-header {
  padding: 20px 20px 0;
  margin-bottom: 0;
}

.sort-options {
  display: flex;
  align-items: center;
  gap: 8px;
}

.content-section :deep(.post-list) {
  padding: 0;
  height: calc(100vh - 400px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 12px;
  }
  
  .search-bar {
    flex-direction: column;
    gap: 8px;
  }
  
  .filter-options,
  .trending-section {
    padding: 16px;
  }
  
  .trending-topics {
    gap: 8px;
  }
  
  .topic-tag {
    font-size: 0.875rem;
  }
  
  .content-section .section-header {
    padding: 16px 16px 0;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .sort-options {
    align-self: flex-end;
  }
  
  .content-section :deep(.post-list) {
    height: calc(100vh - 350px);
  }
}
</style>
