<script setup lang="ts">
/**
 * 元宇宙社交空间 - 动态详情页面
 */

import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

import { useContentStore } from '@/stores/content'
import PostCard from '@/components/content/PostCard.vue'
import CommentList from '@/components/content/CommentList.vue'
import type { Post, Comment } from '@/types/content'

interface Props {
  id: string
}

const props = defineProps<Props>()
const route = useRoute()
const router = useRouter()
const contentStore = useContentStore()

// 状态管理
const post = ref<Post | null>(null)
const loading = ref(false)
const commentListRef = ref()

// 获取动态详情
const fetchPostDetail = async () => {
  try {
    loading.value = true
    const response = await contentStore.getPostDetail(Number(props.id))
    post.value = response.data
  } catch (error) {
    console.error('获取动态详情失败:', error)
    ElMessage.error('获取动态详情失败')
    router.back()
  } finally {
    loading.value = false
  }
}

// 处理评论提交
const handleCommentSubmit = (comment: Comment) => {
  // 更新动态的评论数
  if (post.value) {
    post.value.comment_count += 1
  }
}

// 处理用户点击
const handleUserClick = (userId: number) => {
  router.push(`/user/${userId}`)
}

// 处理评论
const handleComment = () => {
  // 滚动到评论区域
  const commentSection = document.querySelector('.comments-section')
  if (commentSection) {
    commentSection.scrollIntoView({ behavior: 'smooth' })
  }
}

onMounted(() => {
  fetchPostDetail()
})
</script>

<template>
  <div class="post-detail-page">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <el-button @click="$router.back()">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1>动态详情</h1>
        <div></div>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton animated>
          <template #template>
            <div class="skeleton-header">
              <el-skeleton-item variant="circle" style="width: 40px; height: 40px;" />
              <div class="skeleton-user">
                <el-skeleton-item variant="text" style="width: 120px;" />
                <el-skeleton-item variant="text" style="width: 80px;" />
              </div>
            </div>
            <el-skeleton-item variant="text" style="width: 100%; margin: 16px 0;" />
            <el-skeleton-item variant="text" style="width: 80%; margin-bottom: 16px;" />
            <el-skeleton-item variant="rect" style="width: 100%; height: 200px;" />
          </template>
        </el-skeleton>
      </div>
      
      <!-- 动态内容 -->
      <div v-else-if="post" class="post-content">
        <PostCard
          :post="post"
          @comment="handleComment"
          @user-click="handleUserClick"
        />
        
        <!-- 评论区域 -->
        <div class="comments-section">
          <CommentList
            ref="commentListRef"
            :post-id="post.id"
            @comment-submit="handleCommentSubmit"
            @user-click="handleUserClick"
          />
        </div>
      </div>
      
      <!-- 错误状态 -->
      <div v-else class="error-state">
        <el-empty description="动态不存在或已被删除">
          <el-button type="primary" @click="$router.back()">
            返回上一页
          </el-button>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<style scoped>
.post-detail-page {
  min-height: 100vh;
  background-color: var(--el-bg-color-page);
}

.page-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 0;
}

.page-header h1 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.loading-container {
  background: white;
  border-radius: 12px;
  box-shadow: var(--el-box-shadow-light);
  padding: 20px;
}

.skeleton-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.skeleton-user {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.post-content {
  margin-bottom: 20px;
}

.comments-section {
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;
}

.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 12px;
  }
  
  .page-header {
    padding: 12px 0;
  }
  

}
</style>
