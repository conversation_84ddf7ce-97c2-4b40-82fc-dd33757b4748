<script setup lang="ts">
/**
 * 元宇宙社交空间 - 动态列表页面
 */

import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'

import { useContentStore } from '@/stores/content'
import { useUserStore } from '@/stores/user'
import type { Post } from '@/types'

const router = useRouter()
const contentStore = useContentStore()
const userStore = useUserStore()

// 当前选中的标签页
const activeTab = ref('feed')

// 计算属性
const feedPosts = computed(() => contentStore.feedPosts)
const recommendedPosts = computed(() => contentStore.recommendedPosts)
const loading = computed(() => contentStore.loading)

// 组件挂载时获取数据
onMounted(async () => {
  await Promise.all([
    contentStore.fetchFeed(true),
    contentStore.fetchRecommendedPosts(),
    contentStore.fetchTrendingTopics(),
  ])
})

// 加载更多
const loadMore = async () => {
  if (activeTab.value === 'feed') {
    await contentStore.fetchFeed(false)
  }
}

// 刷新数据
const refresh = async () => {
  if (activeTab.value === 'feed') {
    await contentStore.fetchFeed(true)
  } else if (activeTab.value === 'recommended') {
    await contentStore.fetchRecommendedPosts()
  }
}

// 点赞动态
const likePost = async (post: Post) => {
  try {
    await contentStore.likePost(post.id)
  } catch (error) {
    console.error('点赞失败:', error)
  }
}

// 分享动态
const sharePost = async (post: Post) => {
  try {
    await contentStore.sharePost(post.id)
  } catch (error) {
    console.error('分享失败:', error)
  }
}

// 查看动态详情
const viewPostDetail = (post: Post) => {
  router.push(`/content/posts/${post.id}`)
}

// 查看用户资料
const viewUserProfile = (userId: number) => {
  router.push(`/user/${userId}`)
}

// 跳转到发布页面
const goToCreatePost = () => {
  router.push('/content/create')
}

// 格式化时间
const formatTime = (time: string) => {
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return date.toLocaleDateString()
}

// 获取当前列表
const getCurrentPosts = computed(() => {
  return activeTab.value === 'feed' ? feedPosts.value : recommendedPosts.value
})
</script>

<template>
  <div class="post-list-page">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1>动态</h1>
        <el-button type="primary" @click="goToCreatePost">
          <el-icon><Plus /></el-icon>
          发布动态
        </el-button>
      </div>
      
      <!-- 标签页 -->
      <el-tabs v-model="activeTab" @tab-change="refresh">
        <!-- 动态流 -->
        <el-tab-pane label="关注" name="feed">
          <div class="posts-container">
            <el-loading :loading="loading.feed">
              <div v-if="feedPosts.length === 0" class="empty-state">
                <el-empty description="暂无动态">
                  <el-button type="primary" @click="goToCreatePost">
                    发布第一条动态
                  </el-button>
                </el-empty>
              </div>
              
              <div v-else class="posts-list">
                <div 
                  v-for="post in feedPosts" 
                  :key="post.id" 
                  class="post-card"
                >
                  <!-- 用户信息 -->
                  <div class="post-header">
                    <div class="user-info" @click="viewUserProfile(post.author.id)">
                      <el-avatar 
                        :src="post.author.avatar_url"
                        :size="40"
                      >
                        {{ post.author.display_name?.charAt(0) || post.author.username.charAt(0) }}
                      </el-avatar>
                      <div class="user-details">
                        <div class="user-name">{{ post.author.display_name || post.author.username }}</div>
                        <div class="post-time">{{ formatTime(post.published_at || post.created_at) }}</div>
                      </div>
                    </div>
                    
                    <el-dropdown>
                      <el-button type="primary" link>
                        <el-icon><MoreFilled /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item @click="viewPostDetail(post)">
                            查看详情
                          </el-dropdown-item>
                          <el-dropdown-item v-if="post.author.id === userStore.currentUser?.id">
                            编辑
                          </el-dropdown-item>
                          <el-dropdown-item v-if="post.author.id === userStore.currentUser?.id">
                            删除
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                  
                  <!-- 动态内容 -->
                  <div class="post-content" @click="viewPostDetail(post)">
                    <p class="content-text">{{ post.content }}</p>
                    
                    <!-- 媒体文件 -->
                    <div v-if="post.media_urls && post.media_urls.length > 0" class="media-content">
                      <div class="media-grid">
                        <img 
                          v-for="(url, index) in post.media_urls.slice(0, 4)" 
                          :key="index"
                          :src="url" 
                          :alt="`媒体文件 ${index + 1}`"
                          class="media-item"
                        />
                      </div>
                      <div v-if="post.media_urls.length > 4" class="more-media">
                        +{{ post.media_urls.length - 4 }}
                      </div>
                    </div>
                    
                    <!-- 话题标签 -->
                    <div v-if="post.topics && post.topics.length > 0" class="topics">
                      <el-tag 
                        v-for="topic in post.topics" 
                        :key="topic.id"
                        size="small"
                        class="topic-tag"
                      >
                        #{{ topic.display_name }}
                      </el-tag>
                    </div>
                    
                    <!-- 位置信息 -->
                    <div v-if="post.location" class="location">
                      <el-icon><Location /></el-icon>
                      <span>{{ post.location }}</span>
                    </div>
                  </div>
                  
                  <!-- 互动按钮 -->
                  <div class="post-actions">
                    <el-button 
                      type="primary" 
                      :class="{ 'is-liked': post.is_liked }"
                      link
                      @click="likePost(post)"
                    >
                      <el-icon><Heart /></el-icon>
                      {{ post.like_count }}
                    </el-button>
                    
                    <el-button type="primary" link @click="viewPostDetail(post)">
                      <el-icon><ChatDotRound /></el-icon>
                      {{ post.comment_count }}
                    </el-button>
                    
                    <el-button 
                      type="primary" 
                      :class="{ 'is-shared': post.is_shared }"
                      link
                      @click="sharePost(post)"
                    >
                      <el-icon><Share /></el-icon>
                      {{ post.share_count }}
                    </el-button>
                    
                    <el-button type="primary" link>
                      <el-icon><View /></el-icon>
                      {{ post.view_count }}
                    </el-button>
                  </div>
                </div>
                
                <!-- 加载更多 -->
                <div v-if="contentStore.pagination.feed.hasMore" class="load-more">
                  <el-button @click="loadMore" :loading="loading.feed">
                    加载更多
                  </el-button>
                </div>
              </div>
            </el-loading>
          </div>
        </el-tab-pane>
        
        <!-- 推荐动态 -->
        <el-tab-pane label="推荐" name="recommended">
          <div class="posts-container">
            <el-loading :loading="loading.recommended">
              <div v-if="recommendedPosts.length === 0" class="empty-state">
                <el-empty description="暂无推荐动态" />
              </div>
              
              <div v-else class="posts-list">
                <div 
                  v-for="post in recommendedPosts" 
                  :key="post.id" 
                  class="post-card"
                >
                  <!-- 复用上面的动态卡片结构 -->
                  <!-- 用户信息 -->
                  <div class="post-header">
                    <div class="user-info" @click="viewUserProfile(post.author.id)">
                      <el-avatar 
                        :src="post.author.avatar_url"
                        :size="40"
                      >
                        {{ post.author.display_name?.charAt(0) || post.author.username.charAt(0) }}
                      </el-avatar>
                      <div class="user-details">
                        <div class="user-name">{{ post.author.display_name || post.author.username }}</div>
                        <div class="post-time">{{ formatTime(post.published_at || post.created_at) }}</div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 动态内容 -->
                  <div class="post-content" @click="viewPostDetail(post)">
                    <p class="content-text">{{ post.content }}</p>
                  </div>
                  
                  <!-- 互动按钮 -->
                  <div class="post-actions">
                    <el-button 
                      type="primary" 
                      :class="{ 'is-liked': post.is_liked }"
                      link
                      @click="likePost(post)"
                    >
                      <el-icon><Heart /></el-icon>
                      {{ post.like_count }}
                    </el-button>
                    
                    <el-button type="primary" link @click="viewPostDetail(post)">
                      <el-icon><ChatDotRound /></el-icon>
                      {{ post.comment_count }}
                    </el-button>
                    
                    <el-button 
                      type="primary" 
                      link
                      @click="sharePost(post)"
                    >
                      <el-icon><Share /></el-icon>
                      {{ post.share_count }}
                    </el-button>
                  </div>
                </div>
              </div>
            </el-loading>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<style scoped>
.post-list-page {
  min-height: 100vh;
  background-color: var(--el-bg-color-page);
}

.page-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.posts-container {
  margin-top: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.posts-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.post-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: var(--el-box-shadow-light);
  transition: box-shadow 0.2s ease;
}

.post-card:hover {
  box-shadow: var(--el-box-shadow);
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 2px;
}

.post-time {
  font-size: 0.9rem;
  color: var(--el-text-color-secondary);
}

.post-content {
  cursor: pointer;
  margin-bottom: 16px;
}

.content-text {
  color: var(--el-text-color-primary);
  line-height: 1.6;
  margin: 0 0 12px 0;
}

.media-content {
  position: relative;
  margin-bottom: 12px;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  border-radius: 8px;
  overflow: hidden;
}

.media-item {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.more-media {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.9rem;
}

.topics {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.topic-tag {
  cursor: pointer;
}

.location {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--el-text-color-secondary);
  font-size: 0.9rem;
}

.post-actions {
  display: flex;
  justify-content: space-around;
  padding-top: 12px;
  border-top: 1px solid var(--el-border-color-light);
}

.post-actions .el-button.is-liked {
  color: var(--el-color-danger);
}

.post-actions .el-button.is-shared {
  color: var(--el-color-success);
}

.load-more {
  text-align: center;
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .post-card {
    padding: 16px;
  }
  
  .media-grid {
    grid-template-columns: 1fr;
  }
  
  .media-item {
    height: 250px;
  }
}
</style>
