<script setup lang="ts">
/**
 * 元宇宙社交空间 - 404错误页面
 */

import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  router.back()
}

const goHome = () => {
  router.push('/home')
}
</script>

<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-icon">
        <el-icon><QuestionFilled /></el-icon>
      </div>
      <h1>404</h1>
      <h2>页面不存在</h2>
      <p>抱歉，您访问的页面不存在或已被删除</p>
      
      <div class="error-actions">
        <el-button @click="goBack">返回上页</el-button>
        <el-button type="primary" @click="goHome">回到首页</el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px;
}

.error-content {
  max-width: 500px;
}

.error-icon {
  font-size: 6rem;
  color: var(--el-color-info);
  margin-bottom: 20px;
}

.error-content h1 {
  font-size: 4rem;
  font-weight: 700;
  color: var(--el-text-color-primary);
  margin-bottom: 16px;
}

.error-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 16px;
}

.error-content p {
  font-size: 1rem;
  color: var(--el-text-color-regular);
  margin-bottom: 32px;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}
</style>
