<script setup lang="ts">
/**
 * 元宇宙社交空间 - 首页
 */

import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import PostList from '@/components/content/PostList.vue'
import type { Post } from '@/types/content'

const router = useRouter()
const userStore = useUserStore()

// 状态管理
const activeTab = ref('feed')
const postListRef = ref()

// 处理动态点击
const handlePostClick = (post: Post) => {
  router.push(`/content/post/${post.id}`)
}

// 处理用户点击
const handleUserClick = (userId: number) => {
  router.push(`/user/${userId}`)
}

// 切换标签页
const handleTabChange = (tab: string) => {
  activeTab.value = tab
  // 刷新对应的列表
  if (postListRef.value) {
    postListRef.value.refresh()
  }
}

onMounted(() => {
  console.log('首页加载完成')
})
</script>

<template>
  <div class="home-page">
    <!-- 欢迎区域 -->
    <div class="welcome-section" v-if="!userStore.isAuthenticated">
      <div class="welcome-content">
        <h1>欢迎来到元宇宙社交空间</h1>
        <p>连接虚拟与现实的社交平台</p>
        <div class="welcome-actions">
          <el-button type="primary" size="large" @click="router.push('/auth/login')">
            立即登录
          </el-button>
          <el-button size="large" @click="router.push('/auth/register')">
            注册账户
          </el-button>
        </div>
      </div>

      <div class="feature-grid">
        <el-card class="feature-card">
          <template #header>
            <el-icon size="32"><Avatar /></el-icon>
          </template>
          <h3>3D虚拟空间</h3>
          <p>体验沉浸式的3D社交环境</p>
        </el-card>

        <el-card class="feature-card">
          <template #header>
            <el-icon size="32"><ChatDotRound /></el-icon>
          </template>
          <h3>实时互动</h3>
          <p>与朋友进行实时语音视频交流</p>
        </el-card>

        <el-card class="feature-card">
          <template #header>
            <el-icon size="32"><Star /></el-icon>
          </template>
          <h3>个性化定制</h3>
          <p>打造专属于你的虚拟形象</p>
        </el-card>
      </div>
    </div>

    <!-- 已登录用户的内容流 -->
    <div v-else class="content-section">
      <div class="content-header">
        <div class="user-greeting">
          <h2>你好，{{ userStore.displayName || userStore.username }}！</h2>
          <p>欢迎回到元宇宙社交空间</p>
        </div>

        <el-button type="primary" @click="router.push('/content/create')">
          <el-icon><Edit /></el-icon>
          发布动态
        </el-button>
      </div>

      <!-- 内容标签页 -->
      <div class="content-tabs">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="推荐" name="recommended">
            <PostList
              ref="postListRef"
              list-type="recommended"
              @post-click="handlePostClick"
              @user-click="handleUserClick"
            />
          </el-tab-pane>

          <el-tab-pane label="关注" name="feed">
            <PostList
              list-type="feed"
              @post-click="handlePostClick"
              @user-click="handleUserClick"
            />
          </el-tab-pane>

          <el-tab-pane label="最新" name="latest">
            <PostList
              list-type="posts"
              @post-click="handlePostClick"
              @user-click="handleUserClick"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-page {
  min-height: 100vh;
  background-color: var(--el-bg-color-page);
}

/* 未登录用户的欢迎页面 */
.welcome-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 60px 20px;
}

.welcome-content {
  text-align: center;
  margin-bottom: 80px;
}

.welcome-content h1 {
  font-size: 3rem;
  font-weight: 700;
  color: var(--el-text-color-primary);
  margin-bottom: 16px;
  background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-success));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.welcome-content p {
  font-size: 1.25rem;
  color: var(--el-text-color-regular);
  margin-bottom: 32px;
}

.welcome-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.feature-card {
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--el-box-shadow);
}

.feature-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 16px 0 12px;
}

.feature-card p {
  color: var(--el-text-color-regular);
  line-height: 1.6;
}

/* 已登录用户的内容区域 */
.content-section {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: var(--el-box-shadow-light);
}

.user-greeting h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.user-greeting p {
  color: var(--el-text-color-secondary);
  font-size: 0.875rem;
}

.content-tabs {
  background: white;
  border-radius: 12px;
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;
}

.content-tabs :deep(.el-tabs__header) {
  margin: 0;
  padding: 0 24px;
  background: var(--el-fill-color-lighter);
}

.content-tabs :deep(.el-tabs__content) {
  padding: 0;
  height: calc(100vh - 300px);
  overflow: hidden;
}

.content-tabs :deep(.el-tab-pane) {
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-content h1 {
    font-size: 2rem;
  }

  .welcome-actions {
    flex-direction: column;
    align-items: center;
  }

  .welcome-actions .el-button {
    width: 200px;
  }

  .feature-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .content-section {
    padding: 12px;
  }

  .content-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
    padding: 20px;
  }

  .content-tabs :deep(.el-tabs__header) {
    padding: 0 16px;
  }

  .content-tabs :deep(.el-tabs__content) {
    height: calc(100vh - 250px);
  }
}
</style>
