<script setup lang="ts">
/**
 * 元宇宙社交空间 - 首页
 */

import { onMounted } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

onMounted(() => {
  console.log('首页加载完成')
})
</script>

<template>
  <div class="home-page">
    <div class="welcome-section">
      <h1>欢迎来到元宇宙社交空间</h1>
      <p v-if="userStore.isAuthenticated">
        你好，{{ userStore.displayName }}！
      </p>
      <p v-else>
        请先登录以体验完整功能
      </p>
    </div>
    
    <div class="content-grid">
      <el-card class="feature-card">
        <h3>3D虚拟空间</h3>
        <p>体验沉浸式的3D社交环境</p>
      </el-card>
      
      <el-card class="feature-card">
        <h3>实时互动</h3>
        <p>与朋友进行实时语音视频交流</p>
      </el-card>
      
      <el-card class="feature-card">
        <h3>个性化定制</h3>
        <p>打造专属于你的虚拟形象</p>
      </el-card>
    </div>
  </div>
</template>

<style scoped>
.home-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.welcome-section {
  text-align: center;
  margin-bottom: 60px;
}

.welcome-section h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--el-text-color-primary);
  margin-bottom: 16px;
}

.welcome-section p {
  font-size: 1.2rem;
  color: var(--el-text-color-regular);
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.feature-card {
  text-align: center;
  padding: 20px;
}

.feature-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 12px;
}

.feature-card p {
  color: var(--el-text-color-regular);
  line-height: 1.6;
}
</style>
