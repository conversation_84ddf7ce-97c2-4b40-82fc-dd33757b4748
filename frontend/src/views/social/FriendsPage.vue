<script setup lang="ts">
/**
 * 元宇宙社交空间 - 好友管理页面
 */

import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

import { useSocialStore } from '@/stores/social'
import type { Friend, FriendRequest } from '@/types'

const socialStore = useSocialStore()

// 当前选中的标签页
const activeTab = ref('friends')

// 计算属性
const friends = computed(() => socialStore.friends)
const friendRequests = computed(() => socialStore.friendRequests)
const loading = computed(() => socialStore.loading)

// 组件挂载时获取数据
onMounted(async () => {
  await Promise.all([
    socialStore.fetchFriends(),
    socialStore.fetchFriendRequests(),
  ])
})

// 处理好友请求
const handleFriendRequest = async (request: FriendRequest, action: 'accept' | 'decline') => {
  try {
    if (action === 'accept') {
      await socialStore.acceptFriendRequest(request.id)
    } else {
      await socialStore.declineFriendRequest(request.id)
    }
    
    // 刷新数据
    await socialStore.fetchFriendRequests()
    if (action === 'accept') {
      await socialStore.fetchFriends()
    }
  } catch (error) {
    console.error('处理好友请求失败:', error)
  }
}

// 删除好友
const removeFriend = async (friend: Friend) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个好友吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await socialStore.removeFriend(friend.id)
    await socialStore.fetchFriends()
  } catch (error) {
    // 用户取消删除
  }
}

// 获取好友显示名称
const getFriendDisplayName = (friend: Friend, currentUserId: number) => {
  const friendUser = friend.requester.id === currentUserId ? friend.addressee : friend.requester
  return friendUser.display_name || friendUser.username
}

// 获取好友头像
const getFriendAvatar = (friend: Friend, currentUserId: number) => {
  const friendUser = friend.requester.id === currentUserId ? friend.addressee : friend.requester
  return friendUser.avatar_url || '/default-avatar.png'
}
</script>

<template>
  <div class="friends-page">
    <div class="page-header">
      <h1>好友管理</h1>
      <p>管理您的好友关系和好友请求</p>
    </div>
    
    <el-tabs v-model="activeTab" class="friends-tabs">
      <!-- 好友列表 -->
      <el-tab-pane label="我的好友" name="friends">
        <template #label>
          <span>
            我的好友
            <el-badge 
              v-if="friends.length > 0" 
              :value="friends.length" 
              class="tab-badge"
            />
          </span>
        </template>
        
        <div class="friends-content">
          <el-loading :loading="loading.friends">
            <div v-if="friends.length === 0" class="empty-state">
              <el-empty description="暂无好友">
                <el-button type="primary" @click="$router.push('/social/search')">
                  去添加好友
                </el-button>
              </el-empty>
            </div>
            
            <div v-else class="friends-grid">
              <el-card 
                v-for="friend in friends" 
                :key="friend.id" 
                class="friend-card"
                shadow="hover"
              >
                <div class="friend-info">
                  <el-avatar 
                    :src="getFriendAvatar(friend, $store.user.currentUser?.id || 0)"
                    :size="60"
                    class="friend-avatar"
                  >
                    {{ getFriendDisplayName(friend, $store.user.currentUser?.id || 0).charAt(0) }}
                  </el-avatar>
                  
                  <div class="friend-details">
                    <h3 class="friend-name">
                      {{ getFriendDisplayName(friend, $store.user.currentUser?.id || 0) }}
                    </h3>
                    <p class="friend-username">
                      @{{ friend.requester.id === $store.user.currentUser?.id ? friend.addressee.username : friend.requester.username }}
                    </p>
                    <p class="friend-since">
                      好友时间：{{ new Date(friend.responded_at || friend.created_at).toLocaleDateString() }}
                    </p>
                  </div>
                </div>
                
                <div class="friend-actions">
                  <el-button 
                    type="primary" 
                    size="small"
                    @click="$router.push(`/user/${friend.requester.id === $store.user.currentUser?.id ? friend.addressee.id : friend.requester.id}`)"
                  >
                    查看资料
                  </el-button>
                  <el-button 
                    type="danger" 
                    size="small" 
                    plain
                    @click="removeFriend(friend)"
                  >
                    删除好友
                  </el-button>
                </div>
              </el-card>
            </div>
          </el-loading>
        </div>
      </el-tab-pane>
      
      <!-- 好友请求 -->
      <el-tab-pane label="好友请求" name="requests">
        <template #label>
          <span>
            好友请求
            <el-badge 
              v-if="socialStore.pendingRequestsCount > 0" 
              :value="socialStore.pendingRequestsCount" 
              class="tab-badge"
            />
          </span>
        </template>
        
        <div class="requests-content">
          <el-loading :loading="loading.requests">
            <div v-if="friendRequests.length === 0" class="empty-state">
              <el-empty description="暂无好友请求" />
            </div>
            
            <div v-else class="requests-list">
              <el-card 
                v-for="request in friendRequests" 
                :key="request.id" 
                class="request-card"
                shadow="hover"
              >
                <div class="request-info">
                  <el-avatar 
                    :src="request.requester.avatar_url"
                    :size="50"
                    class="request-avatar"
                  >
                    {{ request.requester.display_name?.charAt(0) || request.requester.username.charAt(0) }}
                  </el-avatar>
                  
                  <div class="request-details">
                    <h4 class="request-name">
                      {{ request.requester.display_name || request.requester.username }}
                    </h4>
                    <p class="request-username">@{{ request.requester.username }}</p>
                    <p class="request-time">
                      {{ new Date(request.requested_at).toLocaleString() }}
                    </p>
                    <p v-if="request.message" class="request-message">
                      "{{ request.message }}"
                    </p>
                  </div>
                </div>
                
                <div class="request-actions">
                  <template v-if="request.status === 'pending'">
                    <el-button 
                      type="success" 
                      size="small"
                      @click="handleFriendRequest(request, 'accept')"
                    >
                      接受
                    </el-button>
                    <el-button 
                      type="danger" 
                      size="small" 
                      plain
                      @click="handleFriendRequest(request, 'decline')"
                    >
                      拒绝
                    </el-button>
                  </template>
                  
                  <el-tag 
                    v-else 
                    :type="request.status === 'accepted' ? 'success' : 'danger'"
                  >
                    {{ request.status === 'accepted' ? '已接受' : '已拒绝' }}
                  </el-tag>
                </div>
              </el-card>
            </div>
          </el-loading>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style scoped>
.friends-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.page-header p {
  color: var(--el-text-color-regular);
  font-size: 1rem;
}

.friends-tabs {
  margin-top: 20px;
}

.tab-badge {
  margin-left: 8px;
}

.friends-content,
.requests-content {
  margin-top: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.friends-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.friend-card {
  padding: 20px;
}

.friend-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.friend-avatar {
  margin-right: 16px;
}

.friend-details {
  flex: 1;
}

.friend-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 4px 0;
}

.friend-username {
  color: var(--el-text-color-regular);
  font-size: 0.9rem;
  margin: 0 0 4px 0;
}

.friend-since {
  color: var(--el-text-color-secondary);
  font-size: 0.8rem;
  margin: 0;
}

.friend-actions {
  display: flex;
  gap: 8px;
}

.requests-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.request-card {
  padding: 16px;
}

.request-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
}

.request-avatar {
  margin-right: 16px;
}

.request-details {
  flex: 1;
}

.request-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 4px 0;
}

.request-username {
  color: var(--el-text-color-regular);
  font-size: 0.9rem;
  margin: 0 0 4px 0;
}

.request-time {
  color: var(--el-text-color-secondary);
  font-size: 0.8rem;
  margin: 0 0 8px 0;
}

.request-message {
  color: var(--el-text-color-regular);
  font-style: italic;
  margin: 0;
}

.request-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .friends-grid {
    grid-template-columns: 1fr;
  }
  
  .friend-info,
  .request-info {
    flex-direction: column;
    text-align: center;
  }
  
  .friend-avatar,
  .request-avatar {
    margin-right: 0;
    margin-bottom: 12px;
  }
}
</style>
