<script setup lang="ts">
/**
 * 元宇宙社交空间 - 用户搜索页面
 */

import { ref, onMounted, computed, watch } from 'vue'
import { useDebounceFn } from '@vueuse/core'

import { useSocialStore } from '@/stores/social'
import type { User } from '@/types'

const socialStore = useSocialStore()

// 搜索关键词
const searchQuery = ref('')

// 当前选中的标签页
const activeTab = ref('search')

// 计算属性
const searchResults = computed(() => socialStore.searchResults)
const recommendedUsers = computed(() => socialStore.recommendedUsers)
const loading = computed(() => socialStore.loading)

// 防抖搜索
const debouncedSearch = useDebounceFn(async (query: string) => {
  if (query.trim()) {
    await socialStore.searchUsers(query.trim())
  } else {
    socialStore.clearSearchResults()
  }
}, 500)

// 监听搜索关键词变化
watch(searchQuery, (newQuery) => {
  debouncedSearch(newQuery)
})

// 组件挂载时获取推荐用户
onMounted(async () => {
  await socialStore.fetchRecommendedUsers()
})

// 发送好友请求
const sendFriendRequest = async (user: User) => {
  try {
    await socialStore.sendFriendRequest(user.id)
  } catch (error) {
    console.error('发送好友请求失败:', error)
  }
}

// 关注用户
const followUser = async (user: User) => {
  try {
    await socialStore.followUser(user.id)
  } catch (error) {
    console.error('关注用户失败:', error)
  }
}

// 取消关注
const unfollowUser = async (user: User) => {
  try {
    await socialStore.unfollowUser(user.id)
  } catch (error) {
    console.error('取消关注失败:', error)
  }
}

// 查看用户详情
const viewUserProfile = (user: User) => {
  // TODO: 跳转到用户详情页面
  console.log('查看用户详情:', user)
}
</script>

<template>
  <div class="user-search-page">
    <div class="page-header">
      <h1>发现用户</h1>
      <p>搜索并发现有趣的用户</p>
    </div>
    
    <!-- 搜索框 -->
    <div class="search-section">
      <el-input
        v-model="searchQuery"
        placeholder="搜索用户名、显示名..."
        size="large"
        clearable
        class="search-input"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>
    
    <el-tabs v-model="activeTab" class="search-tabs">
      <!-- 搜索结果 -->
      <el-tab-pane label="搜索结果" name="search">
        <div class="search-content">
          <el-loading :loading="loading.search">
            <div v-if="!searchQuery.trim()" class="search-hint">
              <el-empty description="请输入关键词搜索用户" />
            </div>
            
            <div v-else-if="searchResults.length === 0" class="empty-state">
              <el-empty description="未找到相关用户" />
            </div>
            
            <div v-else class="users-grid">
              <el-card 
                v-for="user in searchResults" 
                :key="user.id" 
                class="user-card"
                shadow="hover"
              >
                <div class="user-info">
                  <el-avatar 
                    :src="user.avatar_url"
                    :size="60"
                    class="user-avatar"
                  >
                    {{ user.display_name?.charAt(0) || user.username.charAt(0) }}
                  </el-avatar>
                  
                  <div class="user-details">
                    <h3 class="user-name">
                      {{ user.display_name || user.username }}
                      <el-icon v-if="user.is_verified" class="verified-icon">
                        <CircleCheckFilled />
                      </el-icon>
                    </h3>
                    <p class="user-username">@{{ user.username }}</p>
                    <p v-if="user.bio" class="user-bio">{{ user.bio }}</p>
                    <p v-if="user.location" class="user-location">
                      <el-icon><Location /></el-icon>
                      {{ user.location }}
                    </p>
                  </div>
                </div>
                
                <div class="user-status">
                  <el-tag v-if="user.is_friend" type="success" size="small">
                    已是好友
                  </el-tag>
                  <el-tag v-else-if="user.is_following" type="info" size="small">
                    已关注
                  </el-tag>
                </div>
                
                <div class="user-actions">
                  <el-button 
                    type="primary" 
                    size="small"
                    @click="viewUserProfile(user)"
                  >
                    查看资料
                  </el-button>
                  
                  <template v-if="!user.is_friend">
                    <el-button 
                      type="success" 
                      size="small"
                      @click="sendFriendRequest(user)"
                    >
                      加好友
                    </el-button>
                  </template>
                  
                  <template v-if="!user.is_following">
                    <el-button 
                      type="info" 
                      size="small" 
                      plain
                      @click="followUser(user)"
                    >
                      关注
                    </el-button>
                  </template>
                  <template v-else>
                    <el-button 
                      type="info" 
                      size="small"
                      @click="unfollowUser(user)"
                    >
                      已关注
                    </el-button>
                  </template>
                </div>
              </el-card>
            </div>
          </el-loading>
        </div>
      </el-tab-pane>
      
      <!-- 推荐用户 -->
      <el-tab-pane label="推荐用户" name="recommended">
        <div class="recommended-content">
          <el-loading :loading="loading.recommended">
            <div v-if="recommendedUsers.length === 0" class="empty-state">
              <el-empty description="暂无推荐用户">
                <el-button type="primary" @click="socialStore.fetchRecommendedUsers()">
                  刷新推荐
                </el-button>
              </el-empty>
            </div>
            
            <div v-else class="users-grid">
              <el-card 
                v-for="user in recommendedUsers" 
                :key="user.id" 
                class="user-card"
                shadow="hover"
              >
                <div class="user-info">
                  <el-avatar 
                    :src="user.avatar_url"
                    :size="60"
                    class="user-avatar"
                  >
                    {{ user.display_name?.charAt(0) || user.username.charAt(0) }}
                  </el-avatar>
                  
                  <div class="user-details">
                    <h3 class="user-name">
                      {{ user.display_name || user.username }}
                      <el-icon v-if="user.is_verified" class="verified-icon">
                        <CircleCheckFilled />
                      </el-icon>
                    </h3>
                    <p class="user-username">@{{ user.username }}</p>
                    <p v-if="user.bio" class="user-bio">{{ user.bio }}</p>
                    <p v-if="user.location" class="user-location">
                      <el-icon><Location /></el-icon>
                      {{ user.location }}
                    </p>
                  </div>
                </div>
                
                <div class="user-actions">
                  <el-button 
                    type="primary" 
                    size="small"
                    @click="viewUserProfile(user)"
                  >
                    查看资料
                  </el-button>
                  
                  <el-button 
                    type="success" 
                    size="small"
                    @click="sendFriendRequest(user)"
                  >
                    加好友
                  </el-button>
                  
                  <el-button 
                    type="info" 
                    size="small" 
                    plain
                    @click="followUser(user)"
                  >
                    关注
                  </el-button>
                </div>
              </el-card>
            </div>
          </el-loading>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style scoped>
.user-search-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.page-header p {
  color: var(--el-text-color-regular);
  font-size: 1rem;
}

.search-section {
  margin-bottom: 30px;
}

.search-input {
  max-width: 600px;
  margin: 0 auto;
}

.search-tabs {
  margin-top: 20px;
}

.search-content,
.recommended-content {
  margin-top: 20px;
}

.search-hint,
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.user-card {
  padding: 20px;
  position: relative;
}

.user-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
}

.user-avatar {
  margin-right: 16px;
  flex-shrink: 0;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 4px 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.verified-icon {
  color: var(--el-color-primary);
  font-size: 1rem;
}

.user-username {
  color: var(--el-text-color-regular);
  font-size: 0.9rem;
  margin: 0 0 8px 0;
}

.user-bio {
  color: var(--el-text-color-regular);
  font-size: 0.9rem;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.user-location {
  color: var(--el-text-color-secondary);
  font-size: 0.8rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.user-status {
  position: absolute;
  top: 16px;
  right: 16px;
}

.user-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .users-grid {
    grid-template-columns: 1fr;
  }
  
  .user-info {
    flex-direction: column;
    text-align: center;
  }
  
  .user-avatar {
    margin-right: 0;
    margin-bottom: 12px;
    align-self: center;
  }
  
  .user-status {
    position: static;
    margin-bottom: 12px;
    text-align: center;
  }
  
  .user-actions {
    justify-content: center;
  }
}
</style>
