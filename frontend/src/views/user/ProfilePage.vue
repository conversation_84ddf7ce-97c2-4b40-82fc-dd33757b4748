<script setup lang="ts">
/**
 * 元宇宙社交空间 - 个人资料页面
 */

import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

import { useUserStore } from '@/stores/user'
import UserProfileCard from '@/components/user/UserProfileCard.vue'
import UserStatsCard from '@/components/user/UserStatsCard.vue'
import PostList from '@/components/content/PostList.vue'
import type { User, Post } from '@/types'

const router = useRouter()
const userStore = useUserStore()

// 状态管理
const loading = ref(false)
const activeTab = ref('posts')
const userStatsRef = ref()
const postListRef = ref()

// 计算属性
const currentUser = computed(() => userStore.currentUser)

// 标签页选项
const tabOptions = [
  { key: 'posts', label: '动态', icon: 'DocumentCopy' },
  { key: 'media', label: '媒体', icon: 'Picture' },
  { key: 'likes', label: '点赞', icon: 'Like' },
]

// 处理编辑资料
const handleEdit = () => {
  router.push('/user/settings')
}

// 处理统计点击
const handleStatsClick = (type: string) => {
  switch (type) {
    case 'posts':
      activeTab.value = 'posts'
      break
    case 'following':
      router.push('/user/following')
      break
    case 'followers':
      router.push('/user/followers')
      break
  }
}

// 处理动态点击
const handlePostClick = (post: Post) => {
  router.push(`/content/post/${post.id}`)
}

// 处理用户点击
const handleUserClick = (userId: number) => {
  router.push(`/user/${userId}`)
}

// 处理标签页切换
const handleTabChange = (tab: string) => {
  activeTab.value = tab
  
  // 根据标签页类型刷新对应内容
  if (tab === 'posts' && postListRef.value) {
    postListRef.value.refresh()
  }
}

// 刷新页面数据
const refreshData = () => {
  if (userStatsRef.value) {
    userStatsRef.value.refresh()
  }
  if (postListRef.value) {
    postListRef.value.refresh()
  }
}

onMounted(() => {
  console.log('个人资料页面加载完成')
})
</script>

<template>
  <div class="profile-page">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <el-button @click="$router.back()">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1>个人资料</h1>
        <el-button type="primary" @click="handleEdit">
          <el-icon><Edit /></el-icon>
          编辑资料
        </el-button>
      </div>
      
      <!-- 用户资料卡片 -->
      <div class="profile-section">
        <UserProfileCard
          v-if="currentUser"
          :user="currentUser"
          @edit="handleEdit"
        />
      </div>
      
      <!-- 统计信息 -->
      <div class="stats-section">
        <UserStatsCard
          v-if="currentUser"
          ref="userStatsRef"
          :user-id="currentUser.id"
          @posts-click="handleStatsClick('posts')"
          @following-click="handleStatsClick('following')"
          @followers-click="handleStatsClick('followers')"
        />
      </div>
      
      <!-- 内容标签页 -->
      <div class="content-section">
        <div class="content-tabs">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane
              v-for="tab in tabOptions"
              :key="tab.key"
              :label="tab.label"
              :name="tab.key"
            >
              <template #label>
                <span class="tab-label">
                  <el-icon><component :is="tab.icon" /></el-icon>
                  {{ tab.label }}
                </span>
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>
        
        <!-- 标签页内容 -->
        <div class="tab-content">
          <!-- 动态列表 -->
          <div v-if="activeTab === 'posts'" class="posts-content">
            <PostList
              ref="postListRef"
              list-type="posts"
              :user-id="currentUser?.id"
              @post-click="handlePostClick"
              @user-click="handleUserClick"
            />
          </div>
          
          <!-- 媒体内容 -->
          <div v-else-if="activeTab === 'media'" class="media-content">
            <el-empty description="媒体功能开发中">
              <template #image>
                <el-icon size="64"><Picture /></el-icon>
              </template>
            </el-empty>
          </div>
          
          <!-- 点赞内容 -->
          <div v-else-if="activeTab === 'likes'" class="likes-content">
            <el-empty description="点赞记录功能开发中">
              <template #image>
                <el-icon size="64"><Like /></el-icon>
              </template>
            </el-empty>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.profile-page {
  min-height: 100vh;
  background-color: var(--el-bg-color-page);
}

.page-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 0;
}

.page-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.profile-section {
  margin-bottom: 24px;
}

.stats-section {
  margin-bottom: 24px;
}

.content-section {
  background: white;
  border-radius: 12px;
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;
}

.content-tabs {
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.content-tabs :deep(.el-tabs__header) {
  margin: 0;
  padding: 0 24px;
  background: var(--el-fill-color-lighter);
}

.content-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
}

.tab-content {
  min-height: 400px;
}

.posts-content {
  height: calc(100vh - 500px);
  min-height: 400px;
}

.posts-content :deep(.post-list) {
  padding: 0;
  height: 100%;
}

.media-content,
.likes-content {
  padding: 60px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 12px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
  
  .page-header h1 {
    order: -1;
  }
  
  .content-tabs :deep(.el-tabs__header) {
    padding: 0 16px;
  }
  
  .tab-label {
    font-size: 0.875rem;
  }
  
  .posts-content {
    height: calc(100vh - 400px);
  }
  
  .media-content,
  .likes-content {
    padding: 40px 16px;
  }
}
</style>
