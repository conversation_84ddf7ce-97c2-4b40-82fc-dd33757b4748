<script setup lang="ts">
/**
 * 元宇宙社交空间 - 账户设置页面
 */

import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

import { useUserStore } from '@/stores/user'
import AvatarUpload from '@/components/user/AvatarUpload.vue'
import type { User, UserProfile } from '@/types/user'

const router = useRouter()
const userStore = useUserStore()

// 状态管理
const loading = ref(false)
const activeTab = ref('profile')
const formRef = ref<FormInstance>()

// 表单数据
const profileForm = reactive<Partial<UserProfile>>({
  firstName: '',
  lastName: '',
  displayName: '',
  bio: '',
  birthDate: '',
  gender: '',
  location: '',
  website: ''
})

const accountForm = reactive({
  username: '',
  email: '',
  phone: ''
})

const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const preferencesForm = reactive({
  language: 'zh-CN',
  theme: 'auto',
  notifications: {
    email: true,
    push: true,
    sms: false
  },
  privacy: {
    profileVisibility: 'public',
    showOnlineStatus: true
  }
})

// 计算属性
const currentUser = computed(() => userStore.currentUser)

// 表单验证规则
const profileRules: FormRules = {
  displayName: [
    { max: 50, message: '显示名称不能超过50个字符', trigger: 'blur' }
  ],
  bio: [
    { max: 500, message: '个人简介不能超过500个字符', trigger: 'blur' }
  ],
  website: [
    { type: 'url', message: '请输入正确的网址格式', trigger: 'blur' }
  ]
}

const passwordRules: FormRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, message: '密码长度不能少于8位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 性别选项
const genderOptions = [
  { label: '男', value: 'male' },
  { label: '女', value: 'female' },
  { label: '其他', value: 'other' },
  { label: '不愿透露', value: 'prefer_not_to_say' }
]

// 主题选项
const themeOptions = [
  { label: '浅色', value: 'light' },
  { label: '深色', value: 'dark' },
  { label: '跟随系统', value: 'auto' }
]

// 隐私选项
const privacyOptions = [
  { label: '公开', value: 'public' },
  { label: '好友可见', value: 'friends' },
  { label: '私有', value: 'private' }
]

// 初始化表单数据
const initFormData = () => {
  if (currentUser.value) {
    const user = currentUser.value
    
    // 个人资料
    profileForm.firstName = user.firstName || ''
    profileForm.lastName = user.lastName || ''
    profileForm.displayName = user.displayName || ''
    profileForm.bio = user.bio || ''
    profileForm.birthDate = user.birthDate || ''
    profileForm.gender = user.gender || ''
    profileForm.location = user.location || ''
    profileForm.website = user.website || ''
    
    // 账户信息
    accountForm.username = user.username
    accountForm.email = user.email
    accountForm.phone = user.phone || ''
    
    // 用户偏好
    const preferences = userStore.preferences
    preferencesForm.language = preferences.interface.language
    preferencesForm.theme = preferences.interface.theme
    preferencesForm.notifications = { ...preferences.notifications }
    preferencesForm.privacy = { ...preferences.privacy }
  }
}

// 保存个人资料
const saveProfile = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true
    
    await userStore.updateProfile(profileForm)
    ElMessage.success('个人资料更新成功')
    
  } catch (error) {
    console.error('更新个人资料失败:', error)
    ElMessage.error('更新失败')
  } finally {
    loading.value = false
  }
}

// 修改密码
const changePassword = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true
    
    // TODO: 实现密码修改API
    ElMessage.success('密码修改成功')
    
    // 清空表单
    passwordForm.currentPassword = ''
    passwordForm.newPassword = ''
    passwordForm.confirmPassword = ''
    
  } catch (error) {
    console.error('修改密码失败:', error)
    ElMessage.error('修改密码失败')
  } finally {
    loading.value = false
  }
}

// 保存偏好设置
const savePreferences = async () => {
  try {
    loading.value = true
    
    await userStore.updatePreferences({
      interface: {
        language: preferencesForm.language,
        theme: preferencesForm.theme,
        fontSize: 'medium',
        animations: true
      },
      notifications: preferencesForm.notifications,
      privacy: preferencesForm.privacy
    })
    
    ElMessage.success('偏好设置保存成功')
    
  } catch (error) {
    console.error('保存偏好设置失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 处理头像上传
const handleAvatarUpload = async (file: File) => {
  try {
    // TODO: 实现头像上传API
    console.log('上传头像:', file)
    ElMessage.success('头像上传成功')
  } catch (error) {
    console.error('头像上传失败:', error)
    throw error
  }
}

// 删除账户
const deleteAccount = async () => {
  try {
    await ElMessageBox.confirm(
      '删除账户后，您的所有数据将被永久删除且无法恢复。确定要继续吗？',
      '删除账户',
      {
        type: 'error',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'el-button--danger'
      }
    )
    
    // TODO: 实现账户删除API
    ElMessage.success('账户删除成功')
    
    // 登出并跳转到首页
    await userStore.logout()
    router.push('/')
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除账户失败:', error)
      ElMessage.error('删除账户失败')
    }
  }
}

onMounted(() => {
  initFormData()
})
</script>

<template>
  <div class="settings-page">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <el-button @click="$router.back()">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1>账户设置</h1>
        <div></div>
      </div>
      
      <!-- 设置内容 -->
      <div class="settings-content">
        <el-tabs v-model="activeTab" tab-position="left">
          <!-- 个人资料 -->
          <el-tab-pane label="个人资料" name="profile">
            <template #label>
              <span class="tab-label">
                <el-icon><User /></el-icon>
                个人资料
              </span>
            </template>
            
            <div class="tab-content">
              <div class="section-header">
                <h3>个人资料</h3>
                <p>管理您的个人信息和公开资料</p>
              </div>
              
              <!-- 头像上传 -->
              <div class="avatar-section">
                <h4>头像</h4>
                <AvatarUpload
                  :current-avatar="currentUser?.avatarUrl"
                  @upload="handleAvatarUpload"
                />
              </div>
              
              <!-- 个人信息表单 -->
              <el-form
                ref="formRef"
                :model="profileForm"
                :rules="profileRules"
                label-width="100px"
                class="profile-form"
              >
                <el-form-item label="显示名称" prop="displayName">
                  <el-input
                    v-model="profileForm.displayName"
                    placeholder="输入您的显示名称"
                    maxlength="50"
                    show-word-limit
                  />
                </el-form-item>
                
                <el-form-item label="姓名">
                  <div class="name-inputs">
                    <el-input
                      v-model="profileForm.firstName"
                      placeholder="名"
                      maxlength="50"
                    />
                    <el-input
                      v-model="profileForm.lastName"
                      placeholder="姓"
                      maxlength="50"
                    />
                  </div>
                </el-form-item>
                
                <el-form-item label="个人简介" prop="bio">
                  <el-input
                    v-model="profileForm.bio"
                    type="textarea"
                    :rows="4"
                    placeholder="介绍一下自己..."
                    maxlength="500"
                    show-word-limit
                  />
                </el-form-item>
                
                <el-form-item label="生日">
                  <el-date-picker
                    v-model="profileForm.birthDate"
                    type="date"
                    placeholder="选择生日"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                  />
                </el-form-item>
                
                <el-form-item label="性别">
                  <el-select v-model="profileForm.gender" placeholder="选择性别">
                    <el-option
                      v-for="option in genderOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="所在地">
                  <el-input
                    v-model="profileForm.location"
                    placeholder="输入您的所在地"
                    maxlength="100"
                  />
                </el-form-item>
                
                <el-form-item label="个人网站" prop="website">
                  <el-input
                    v-model="profileForm.website"
                    placeholder="https://example.com"
                    maxlength="200"
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button
                    type="primary"
                    :loading="loading"
                    @click="saveProfile"
                  >
                    保存更改
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
          
          <!-- 账户安全 -->
          <el-tab-pane label="账户安全" name="security">
            <template #label>
              <span class="tab-label">
                <el-icon><Lock /></el-icon>
                账户安全
              </span>
            </template>
            
            <div class="tab-content">
              <div class="section-header">
                <h3>账户安全</h3>
                <p>管理您的登录信息和安全设置</p>
              </div>
              
              <!-- 账户信息 -->
              <div class="account-info">
                <h4>账户信息</h4>
                <div class="info-item">
                  <span class="label">用户名：</span>
                  <span class="value">{{ accountForm.username }}</span>
                </div>
                <div class="info-item">
                  <span class="label">邮箱：</span>
                  <span class="value">{{ accountForm.email }}</span>
                </div>
                <div class="info-item">
                  <span class="label">手机：</span>
                  <span class="value">{{ accountForm.phone || '未绑定' }}</span>
                </div>
              </div>
              
              <!-- 修改密码 -->
              <div class="password-section">
                <h4>修改密码</h4>
                <el-form
                  ref="formRef"
                  :model="passwordForm"
                  :rules="passwordRules"
                  label-width="100px"
                  class="password-form"
                >
                  <el-form-item label="当前密码" prop="currentPassword">
                    <el-input
                      v-model="passwordForm.currentPassword"
                      type="password"
                      placeholder="输入当前密码"
                      show-password
                    />
                  </el-form-item>
                  
                  <el-form-item label="新密码" prop="newPassword">
                    <el-input
                      v-model="passwordForm.newPassword"
                      type="password"
                      placeholder="输入新密码"
                      show-password
                    />
                  </el-form-item>
                  
                  <el-form-item label="确认密码" prop="confirmPassword">
                    <el-input
                      v-model="passwordForm.confirmPassword"
                      type="password"
                      placeholder="再次输入新密码"
                      show-password
                    />
                  </el-form-item>
                  
                  <el-form-item>
                    <el-button
                      type="primary"
                      :loading="loading"
                      @click="changePassword"
                    >
                      修改密码
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 偏好设置 -->
          <el-tab-pane label="偏好设置" name="preferences">
            <template #label>
              <span class="tab-label">
                <el-icon><Setting /></el-icon>
                偏好设置
              </span>
            </template>
            
            <div class="tab-content">
              <div class="section-header">
                <h3>偏好设置</h3>
                <p>自定义您的使用体验</p>
              </div>
              
              <el-form label-width="120px" class="preferences-form">
                <!-- 界面设置 -->
                <div class="form-section">
                  <h4>界面设置</h4>
                  
                  <el-form-item label="主题">
                    <el-select v-model="preferencesForm.theme">
                      <el-option
                        v-for="option in themeOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                
                <!-- 通知设置 -->
                <div class="form-section">
                  <h4>通知设置</h4>
                  
                  <el-form-item label="邮件通知">
                    <el-switch v-model="preferencesForm.notifications.email" />
                  </el-form-item>
                  
                  <el-form-item label="推送通知">
                    <el-switch v-model="preferencesForm.notifications.push" />
                  </el-form-item>
                  
                  <el-form-item label="短信通知">
                    <el-switch v-model="preferencesForm.notifications.sms" />
                  </el-form-item>
                </div>
                
                <!-- 隐私设置 -->
                <div class="form-section">
                  <h4>隐私设置</h4>
                  
                  <el-form-item label="资料可见性">
                    <el-select v-model="preferencesForm.privacy.profileVisibility">
                      <el-option
                        v-for="option in privacyOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                  
                  <el-form-item label="显示在线状态">
                    <el-switch v-model="preferencesForm.privacy.showOnlineStatus" />
                  </el-form-item>
                </div>
                
                <el-form-item>
                  <el-button
                    type="primary"
                    :loading="loading"
                    @click="savePreferences"
                  >
                    保存设置
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
          
          <!-- 危险操作 -->
          <el-tab-pane label="危险操作" name="danger">
            <template #label>
              <span class="tab-label danger">
                <el-icon><Warning /></el-icon>
                危险操作
              </span>
            </template>
            
            <div class="tab-content">
              <div class="section-header">
                <h3>危险操作</h3>
                <p>这些操作可能会对您的账户造成不可逆的影响</p>
              </div>
              
              <div class="danger-section">
                <div class="danger-item">
                  <div class="danger-info">
                    <h4>删除账户</h4>
                    <p>永久删除您的账户和所有相关数据。此操作无法撤销。</p>
                  </div>
                  <el-button type="danger" @click="deleteAccount">
                    删除账户
                  </el-button>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<style scoped>
.settings-page {
  min-height: 100vh;
  background-color: var(--el-bg-color-page);
}

.page-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 0;
}

.page-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.settings-content {
  background: white;
  border-radius: 12px;
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;
  min-height: 600px;
}

.settings-content :deep(.el-tabs--left .el-tabs__header) {
  width: 200px;
  background: var(--el-fill-color-lighter);
}

.settings-content :deep(.el-tabs--left .el-tabs__content) {
  padding: 0;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
}

.tab-label.danger {
  color: var(--el-color-danger);
}

.tab-content {
  padding: 32px;
  max-width: 600px;
}

.section-header {
  margin-bottom: 32px;
}

.section-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px;
}

.section-header p {
  color: var(--el-text-color-secondary);
  margin: 0;
}

.avatar-section {
  margin-bottom: 32px;
}

.avatar-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 16px;
}

.name-inputs {
  display: flex;
  gap: 12px;
}

.name-inputs .el-input {
  flex: 1;
}

.account-info {
  margin-bottom: 32px;
}

.account-info h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 16px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.info-item:last-child {
  border-bottom: none;
}

.info-item .label {
  width: 100px;
  color: var(--el-text-color-secondary);
  font-weight: 500;
}

.info-item .value {
  color: var(--el-text-color-primary);
}

.password-section h4,
.form-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 16px;
}

.form-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.form-section:last-child {
  border-bottom: none;
}

.danger-section {
  border: 1px solid var(--el-color-danger-light-7);
  border-radius: 8px;
  padding: 24px;
  background: var(--el-color-danger-light-9);
}

.danger-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.danger-info h4 {
  color: var(--el-color-danger);
  margin: 0 0 8px;
}

.danger-info p {
  color: var(--el-text-color-secondary);
  margin: 0;
  font-size: 0.875rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 12px;
  }
  
  .settings-content :deep(.el-tabs--left) {
    flex-direction: column;
  }
  
  .settings-content :deep(.el-tabs--left .el-tabs__header) {
    width: 100%;
  }
  
  .settings-content :deep(.el-tabs--left .el-tabs__nav) {
    display: flex;
    overflow-x: auto;
  }
  
  .tab-content {
    padding: 20px 16px;
  }
  
  .danger-item {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .name-inputs {
    flex-direction: column;
  }
}
</style>
