<script setup lang="ts">
/**
 * 元宇宙社交空间 - 用户详情页面
 */

import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'

import { socialApi } from '@/api/social'
import { useSocialStore } from '@/stores/social'
import { useUserStore } from '@/stores/user'
import UserProfileCard from '@/components/user/UserProfileCard.vue'
import UserStatsCard from '@/components/user/UserStatsCard.vue'
import PostList from '@/components/content/PostList.vue'
import type { User, Post } from '@/types'

const route = useRoute()
const socialStore = useSocialStore()
const userStore = useUserStore()

// 用户详情数据
const userDetail = ref<User | null>(null)
const loading = ref(false)
const activeTab = ref('posts')
const userStatsRef = ref()
const postListRef = ref()

// 用户ID
const userId = computed(() => parseInt(route.params.id as string))

// 是否为当前用户
const isCurrentUser = computed(() => {
  return userStore.currentUser?.id === userId.value
})

// 组件挂载时获取用户详情
onMounted(async () => {
  await fetchUserDetail()
})

// 获取用户详情
const fetchUserDetail = async () => {
  try {
    loading.value = true
    const response = await socialApi.getUserDetail(userId.value)
    userDetail.value = response.data
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
  } finally {
    loading.value = false
  }
}

// 发送好友请求
const sendFriendRequest = async () => {
  if (!userDetail.value) return
  
  try {
    await socialStore.sendFriendRequest(userDetail.value.id)
    // 刷新用户详情
    await fetchUserDetail()
  } catch (error) {
    console.error('发送好友请求失败:', error)
  }
}

// 关注用户
const followUser = async () => {
  if (!userDetail.value) return
  
  try {
    await socialStore.followUser(userDetail.value.id)
    // 更新本地状态
    if (userDetail.value) {
      userDetail.value.is_following = true
    }
  } catch (error) {
    console.error('关注用户失败:', error)
  }
}

// 取消关注
const unfollowUser = async () => {
  if (!userDetail.value) return
  
  try {
    await socialStore.unfollowUser(userDetail.value.id)
    // 更新本地状态
    if (userDetail.value) {
      userDetail.value.is_following = false
    }
  } catch (error) {
    console.error('取消关注失败:', error)
  }
}

// 发送消息
const sendMessage = () => {
  // TODO: 实现发送消息功能
  ElMessage.info('消息功能即将上线')
}
</script>

<template>
  <div class="user-detail-page">
    <el-loading :loading="loading" class="loading-container">
      <div v-if="userDetail" class="user-profile">
        <!-- 用户头部信息 -->
        <div class="profile-header">
          <div class="cover-section">
            <div 
              class="cover-image"
              :style="{ backgroundImage: userDetail.cover_url ? `url(${userDetail.cover_url})` : 'none' }"
            />
            
            <div class="profile-info">
              <el-avatar 
                :src="userDetail.avatar_url"
                :size="120"
                class="profile-avatar"
              >
                {{ userDetail.display_name?.charAt(0) || userDetail.username.charAt(0) }}
              </el-avatar>
              
              <div class="profile-details">
                <h1 class="profile-name">
                  {{ userDetail.display_name || userDetail.username }}
                  <el-icon v-if="userDetail.is_verified" class="verified-icon">
                    <CircleCheckFilled />
                  </el-icon>
                </h1>
                <p class="profile-username">@{{ userDetail.username }}</p>
                <p v-if="userDetail.bio" class="profile-bio">{{ userDetail.bio }}</p>
                
                <div class="profile-meta">
                  <span v-if="userDetail.location" class="meta-item">
                    <el-icon><Location /></el-icon>
                    {{ userDetail.location }}
                  </span>
                  <span v-if="userDetail.website" class="meta-item">
                    <el-icon><Link /></el-icon>
                    <a :href="userDetail.website" target="_blank">{{ userDetail.website }}</a>
                  </span>
                </div>
              </div>
              
              <!-- 操作按钮 -->
              <div v-if="!isCurrentUser" class="profile-actions">
                <template v-if="!userDetail.is_friend">
                  <el-button 
                    type="primary"
                    @click="sendFriendRequest"
                  >
                    <el-icon><Plus /></el-icon>
                    加好友
                  </el-button>
                </template>
                <template v-else>
                  <el-tag type="success">已是好友</el-tag>
                </template>
                
                <template v-if="!userDetail.is_following">
                  <el-button 
                    type="info"
                    plain
                    @click="followUser"
                  >
                    <el-icon><Star /></el-icon>
                    关注
                  </el-button>
                </template>
                <template v-else>
                  <el-button 
                    type="info"
                    @click="unfollowUser"
                  >
                    <el-icon><StarFilled /></el-icon>
                    已关注
                  </el-button>
                </template>
                
                <el-button @click="sendMessage">
                  <el-icon><ChatDotRound /></el-icon>
                  发消息
                </el-button>
              </div>
              
              <div v-else class="profile-actions">
                <el-button type="primary" @click="$router.push('/user/profile')">
                  <el-icon><Edit /></el-icon>
                  编辑资料
                </el-button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 统计信息 -->
        <div class="stats-section">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-number">{{ userDetail.stats?.friends_count || 0 }}</div>
              <div class="stat-label">好友</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ userDetail.stats?.followers_count || 0 }}</div>
              <div class="stat-label">粉丝</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ userDetail.stats?.following_count || 0 }}</div>
              <div class="stat-label">关注</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ userDetail.stats?.visits_count || 0 }}</div>
              <div class="stat-label">访问</div>
            </div>
          </div>
        </div>
        
        <!-- 内容区域 -->
        <div class="content-section">
          <el-tabs>
            <el-tab-pane label="动态" name="posts">
              <div class="tab-content">
                <el-empty description="暂无动态" />
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="相册" name="photos">
              <div class="tab-content">
                <el-empty description="暂无照片" />
              </div>
            </el-tab-pane>
            
            <el-tab-pane v-if="isCurrentUser || userDetail.is_friend" label="好友" name="friends">
              <div class="tab-content">
                <el-empty description="暂无好友信息" />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      
      <div v-else class="error-state">
        <el-empty description="用户不存在或已被删除" />
      </div>
    </el-loading>
  </div>
</template>

<style scoped>
.user-detail-page {
  min-height: 100vh;
  background-color: var(--el-bg-color-page);
}

.loading-container {
  min-height: 400px;
}

.user-profile {
  max-width: 1000px;
  margin: 0 auto;
}

.profile-header {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--el-box-shadow-light);
  margin-bottom: 20px;
}

.cover-section {
  position: relative;
}

.cover-image {
  height: 200px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-size: cover;
  background-position: center;
}

.profile-info {
  padding: 20px 30px 30px;
  position: relative;
}

.profile-avatar {
  position: absolute;
  top: -60px;
  left: 30px;
  border: 4px solid white;
  box-shadow: var(--el-box-shadow);
}

.profile-details {
  margin-left: 140px;
  margin-bottom: 20px;
}

.profile-name {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.verified-icon {
  color: var(--el-color-primary);
  font-size: 1.2rem;
}

.profile-username {
  color: var(--el-text-color-regular);
  font-size: 1rem;
  margin: 0 0 12px 0;
}

.profile-bio {
  color: var(--el-text-color-regular);
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 16px 0;
}

.profile-meta {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--el-text-color-secondary);
  font-size: 0.9rem;
}

.meta-item a {
  color: var(--el-color-primary);
  text-decoration: none;
}

.meta-item a:hover {
  text-decoration: underline;
}

.profile-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-left: 140px;
}

.stats-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: var(--el-box-shadow-light);
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  color: var(--el-text-color-secondary);
  font-size: 0.9rem;
}

.content-section {
  background: white;
  border-radius: 12px;
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;
}

.tab-content {
  padding: 20px;
  min-height: 300px;
}

.error-state {
  text-align: center;
  padding: 60px 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-info {
    padding: 20px;
  }
  
  .profile-avatar {
    position: static;
    margin: 0 auto 20px;
    display: block;
  }
  
  .profile-details {
    margin-left: 0;
    text-align: center;
  }
  
  .profile-actions {
    margin-left: 0;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .profile-meta {
    justify-content: center;
  }
}
</style>
